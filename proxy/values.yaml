namespace: proxy
image:
  tag: latest
  pullPolicy: Always
  pullSecrets: registrysecrets
  karpenter.sh/nodepool: proxy-ondemand-pool

service:
  type: ClusterIP
  channels:
    ppmp:
      enabled: true
      port: 8090
      replicaCount: 2
      image: nexus-docker.unicommerce.infra:8123/ppmp:45
      env:
        enabled: true
        JAVA_OPTS: "-Xmx4800M -Xloggc:/usr/local/ppmp/logs/gc.log -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/usr/local/ppmp/heapdump/heapdump.hprof -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintGCDateStamps -DconfigDir=/usr/local/ppmp/config -DlogDir=/usr/local/ppmp/logs -Dserver.tomcat.threads.max=100"
      hostName: apiproxy.unicommerce.com
      readinessProbe:
        enabled: true
        healthapi: "/healthCheck"
        initialDelaySeconds: 60
        periodSeconds: 10
        failureThreshold: 3
      livenessProbe:
        enabled: true
        healthapi: "/healthCheck"
        initialDelaySeconds: 120   
        periodSeconds: 15         
        timeoutSeconds: 5         
        failureThreshold: 3
      volume:
        name: "ppmp-volume"
        storageClass: "gp3"
        accessMode: "ReadWriteOnce"
        size: "25Gi"
      resources:
        requests:
          cpu: "2"
          memory: "5Gi"
        limits:
          cpu: "2"
          memory: "5Gi"
      logConfig:
        logPath: /usr/local/ppmp
      hpa:
        enabled: true
        minReplicas: 2
        maxReplicas: 3 
        utilizationCpu: 85
        utilizationMemory: 90
      filebeat:
        enabled: true
        securityContext:
          runAsGroup: 1000
          runAsUser: 
      podSecurityContext:
        fsGroup: 1000
      promtail:
        enabled: true
      clientProperty:
        enabled: true
        configFiles:
          clientProperties: |
            com.ppmp.web.clientId=myntra.ppmp
            com.ppmp.web.secretKey=33fb112e-2987-4207-8525-f859660b0686
            com.ppmp.web.clients=myntra.ppmp:33fb112e-2987-4207-8525-f859660b0686,generic.simsim:12sf452g-3452-1245-2558-a343117b8610,generic.bab:92hi253b-5167-1978-2893-e762238x7247,channelProxyQA:268314-093643-2756610,channelProxyProd:02cl253z-5167-4337-1027-l762230d7432,generic.cred:4a49e96f-a3e0-432d-a0f0-291dd9bf0e27,generic.cultfit:481139c6-a081-11ec-b909-0242ac12000,generic.omuni:2095db08-d809-11ec-9d64-0242ac120002,generic.warpli:3616e3b5-4c5a-4955-a7e7-aaf1b36a2787,generic.taggd:a2bbd64a-3fec-4f07-aa9c-92592ad1604c,zivameProxy:d96b20f5-b3d5-4e59-b723-eb5e2c2cc63f,generic.zaamo:3516e3b5-4c5a-4955-a7e7-aaf1b36a2787,stgZivameProxy:de2db6f6-26ad-40f6-b1f0-92aa90130716,generic.bettercommerce:48b4ec50-43e8-11ed-b878-0242ac120002,generic.stumble:fa9a5647-4648-4618-9c44-d21168d25ca6,generic.industrybuying:523c01c4-5768-442f-be6d-0cd090c367d8,generic.dpanda:2178be46-3127-11ee-be56-0242ac120002,generic.thecollective:be6e5ec9-5c43-4437-9a4b-5dd9d1cfe4fb,generic.tradyl:e614f852-254a-11ee-be56-0242ac120002,generic.fancode:2c51975c-d8b5-4b8f-b70a-109c82f33289,generic.westburym:bd6e5dc9-5c43-4437-9a4b-5ds9d1nfe4fb

    flipkart:
      enabled: false
      port: 80
      replicaCount: 2
      image: nexus-docker.unicommerce.infra:8123/flipkart:40
      env:
        enabled: false
        JAVA_OPTS: "-Xms1024M -Xmx12G -Xloggc:/usr/local/uniware-flipkart-service/logs/gc.log -Dserver.tomcat.max-threads=5000 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/usr/local/uniware-flipkart-service/heapdump/heapdump.hprof -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintGCDateStamps -Dspring.profiles.active=prod -DlogDir=/usr/local/uniware-flipkart-service/logs -DexecutorDir=/usr/local/uniware-flipkart-service/executorDir -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=unicom -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -javaagent:/opt/pinpoint-agent-2.5.2/pinpoint-bootstrap-2.5.2.jar -Dpinpoint.applicationName=Flipkart -Dpinpoint.agentId=${HOSTNAME}  -DexecutorDir=/usr/local/uniware-flipkart-service/executorDir -Dserver.port=80 /usr/local/uniware-flipkart-service/uniwareFlipkartNotification-0.0.1.jar"
      hostName: apiproxy.unicommerce.com
      readinessProbe:
        enabled: false
        healthapi: "/healthCheck"
        initialDelaySeconds: 60
        periodSeconds: 10
        failureThreshold: 3
      livenessProbe:
        enabled: false
        healthapi: "/healthCheck"
        initialDelaySeconds: 120   
        periodSeconds: 15         
        timeoutSeconds: 5         
        failureThreshold: 3
      volume:
        name: "flipkart-volume"
        storageClass: "gp3"
        accessMode: "ReadWriteOnce"
        size: "25Gi"
      resources:
        requests:
          cpu: "2"
          memory: "7Gi"
        limits:
          cpu: "2"
          memory: "7Gi"
      logConfig:
        logPath: /usr/local/flipkart
      hpa:
        enabled: false
        minReplicas: 2
        maxReplicas: 3 
        utilizationCpu: 85
        utilizationMemory: 90
      filebeat:
        enabled: false
        securityContext:
          runAsGroup: 1000
          runAsUser: 
      podSecurityContext:
        fsGroup: 1000
      promtail:
        enabled: false
      clientProperty:
        enabled: false 
        configFiles:
          clientProperties: |
            com.ppmp.web.clientId=flipkart   

    shopify:
      enabled: false
      port: 8080
      replicaCount: 2
      image: nexus-docker.unicommerce.infra:8123/shopify:40
      hostName: apiproxy.unicommerce.com
      readinessProbe:
        enabled: false
        healthapi: "/healthCheck"
        initialDelaySeconds: 60
        periodSeconds: 10
        failureThreshold: 3
      livenessProbe:
        enabled: false
        healthapi: "/healthCheck"
        initialDelaySeconds: 120   
        periodSeconds: 15         
        timeoutSeconds: 5         
        failureThreshold: 3
      volume:
        name: "shopify-volume"
        storageClass: "gp3"
        accessMode: "ReadWriteOnce"
        size: "25Gi"
      resources:
        requests:
          cpu: "4"
          memory: "13Gi"
        limits:
          cpu: "4"
          memory: "13Gi"
      logConfig:
        logPath: /usr/local/shopify
      hpa:
        enabled: false
        minReplicas: 2
        maxReplicas: 3 
        utilizationCpu: 85
        utilizationMemory: 90
      filebeat:
        enabled: false
        securityContext:
          runAsGroup: 1000
          runAsUser: 
      podSecurityContext:
        fsGroup: 1000
      promtail:
        enabled: false
      clientProperty:
        enabled: false 
        configFiles:
          clientProperties: |
            com.ppmp.web.clientId=myntra.ppmp   

    generic:
      enabled: true
      port: 8090
      replicaCount: 2  
      image: nexus-docker.unicommerce.infra:8123/generic:41
      env:
        enabled: true
        JAVA_OPTS: "-Xms512M -Xmx1400M -Xloggc:/usr/local/generic/logs/gc.log -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/usr/local/generic/heapdump/heapdump.hprof -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintGCDateStamps -DconfigDir=/usr/local/generic/config  -DlogDir=/usr/local/generic/logs  -Dserver.tomcat.threads.max=100"
      hostName: genericproxy.unicommerce.com
      readinessProbe:
        enabled: true
        healthapi: "/healthCheck"
        initialDelaySeconds: 60
        periodSeconds: 10
        failureThreshold: 3
      livenessProbe:
        enabled: true
        healthapi: "/healthCheck"
        initialDelaySeconds: 120   
        periodSeconds: 15         
        timeoutSeconds: 5         
        failureThreshold: 3 
      volume:
        name: "generic-volume"
        storageClass: "gp3"
        accessMode: "ReadWriteOnce"
        size: "25Gi"
      resources:
        requests:
          cpu: '1'
          memory: "1.5Gi"
        limits:
          cpu: '1'
          memory: "1.5Gi"
      logConfig:
        logPath: /usr/local/generic
      hpa:
        enabled: true
        minReplicas: 2
        maxReplicas: 3
        utilizationCpu: 85
        utilizationMemory: 90
      filebeat:
        enabled: true
        securityContext:
          runAsGroup: 1000
          runAsUser: 
      podSecurityContext:
        fsGroup: 1000
      promtail:
        enabled: true
      clientProperty:
        enabled: true
        configFiles:
          clientProperties: |
            com.ppmp.web.clientId=myntra.ppmp
            com.ppmp.web.secretKey=33fb112e-2987-4207-8525-f859660b0686
            com.ppmp.web.clients=myntra.ppmp:33fb112e-2987-4207-8525-f859660b0686,generic.tradyl:e614f852-254a-11ee-be56-0242ac120002,generic.ansCommerce:daad0e6e-cb5a-43d3-b7bf-e1e20f4b95cd,generic.simsim:12sf452g-3452-1245-2558-a343117b8610,generic.bab:92hi253b-5167-1978-2893-e762238x7247,channelProxyQA:268314-093643-2756610,channelProxyProd:02cl253z-5167-4337-1027-l762230d7432,generic.cred:4a49e96f-a3e0-432d-a0f0-291dd9bf0e27,generic.cultfit:481139c6-a081-11ec-b909-0242ac12000,generic.omuni:2095db08-d809-11ec-9d64-0242ac120002,generic.warpli:3616e3b5-4c5a-4955-a7e7-aaf1b36a2787,generic.taggd:a2bbd64a-3fec-4f07-aa9c-92592ad1604c,zivameProxy:d96b20f5-b3d5-4e59-b723-eb5e2c2cc63f,generic.zaamo:3516e3b5-4c5a-4955-a7e7-aaf1b36a2787,stgZivameProxy:de2db6f6-26ad-40f6-b1f0-92aa90130716,generic.bettercommerce:48b4ec50-43e8-11ed-b878-0242ac120002,generic.stumble:fa9a5647-4648-4618-9c44-d21168d25ca6,generic.industrybuying:523c01c4-5768-442f-be6d-0cd090c367d8,generic.dpanda:2178be46-3127-11ee-be56-0242ac120002,generic.thecollective:be6e5ec9-5c43-4437-9a4b-5dd9d1cfe4fb,generic.oker:6a7686b6-c429-43c6-b029-b5e14d7eaced,generic.fancode:2c51975c-d8b5-4b8f-b70a-109c82f33289,blinkit:4a49e96f-a3e0-432d-a0f0-291dd9bf0e24,instamart:4a42e96f-a3v0-432d-a0f0-271dd9bf0e24,zepto:1a42e93f-a3v0-432d-a0f0-271dd3bf0e24,bigbasket:4a69e96f-d3d0-432d-a0a0-291dd5bf0e24,generic.westburym:bd6e5dc9-5c43-4437-9a4b-5ds9d1nfe4fb,generic.zopsmart:be6e5ec9-5c43-6969-9a4b-5dq9d1xfe4fb,generic.sellercraft:bdpe5dd9-5c93-4437-9a4b-1ds9d1nfe4fb,generic.kult:bdpe5dd9-5c93-6937-9a6p-1ds9d1nfe4fb,generic.blinkit:fj9a3647-4648-4688-9c44-s21068d25cb6,generic.distacart:4a4se96f-a3e0-4d2d-a0f0-29qdd9bf0er7,generic.zopsmart:1b7wd26d-c5f9-48d1-b0c1-84qaa9v,generic.hypdUat:81eb6165-06f5-40bc-8e7a-99477bfad473,generic.smytten:4f44u96c-k3o0-432f-f0de0-291ep9ef0s2h,generic.amalaearth:4a5se09f-93e2-1d2d-a8k6-29ldd9bf0er7



      
      
        
     
affinity: {}

tolerations:
  - key: "proxy/ondemand"
    operator: "Exists"
    effect: "NoSchedule"

nameOverride: ""
fullnameOverride: ""

