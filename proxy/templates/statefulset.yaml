{{- range $channel, $config := .Values.service.channels }}
{{- if $config.enabled }}
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: proxy-{{ $channel }}  
  namespace: {{ $.Values.namespace }}
  labels:
    app.kubernetes.io/name: proxy
    helm.sh/chart: proxy-1.0.0  
    app.kubernetes.io/instance: proxy-release-{{ $channel }}  
spec:
  replicas: {{ $config.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: proxy
      app.kubernetes.io/instance: proxy-release-{{ $channel }}  
  template:
    metadata:
      labels:
        app.kubernetes.io/name: proxy
        app.kubernetes.io/instance: proxy-release-{{ $channel }}  
    spec:
      securityContext:
        {{- toYaml $config.podSecurityContext | nindent 8 }}
      nodeSelector:
        karpenter.sh/nodepool: proxy-ondemand-pool  
      tolerations:
        - key: "proxy/ondemand"
          operator: "Exists"
          effect: "NoSchedule"  
      imagePullSecrets:
        - name: registrysecrets  
      containers:
        - name: proxy-{{ $channel }}  
          image: {{ $config.image }}
          imagePullPolicy: Always
          {{- if $config.env.enabled }} 
          env:
            - name: JAVA_OPTS
              value: "{{ $config.env.JAVA_OPTS }}"
          {{- end}}     
          ports:
            - containerPort: {{ $config.port }}
          {{- if $config.readinessProbe.enabled }}  
          readinessProbe:
            httpGet:
              path: {{ $config.readinessProbe.healthapi }}
              port: {{ $config.port }}
            initialDelaySeconds: {{ $config.readinessProbe.initialDelaySeconds}}    
            periodSeconds: {{ $config.readinessProbe.periodSeconds}}         
            failureThreshold: {{ $config.readinessProbe.failureThreshold}}
          {{- end}}  
          {{- if $config.livenessProbe.enabled }}
          livenessProbe:
            httpGet:
              path: {{ $config.livenessProbe.healthapi }}
              port: {{ $config.port }}
            initialDelaySeconds: {{ $config.livenessProbe.initialDelaySeconds }}   
            periodSeconds: {{ $config.livenessProbe.periodSeconds }}         
            timeoutSeconds: {{ $config.livenessProbe.timeoutSeconds }}         
            failureThreshold: {{ $config.livenessProbe.failureThreshold }}  
          {{- end}}
          resources:
            requests:
              cpu: {{$config.resources.requests.cpu}}
              memory: {{$config.resources.requests.memory}}
            limits:
              cpu: {{$config.resources.requests.cpu}}
              memory: {{$config.resources.requests.memory}}
          volumeMounts:
            - name: {{ $config.volume.name }}
              mountPath: {{ $config.logConfig.logPath }}/logs
            {{- if $config.clientProperty.enabled}}  
            - name: {{ $channel }}-clientproperties-config
              mountPath: {{ $config.logConfig.logPath }}/config/client.properties 
              subPath: client.properties 
            {{- end}}  
        - name: proxy-promtail-{{ $channel }} 
          image: grafana/promtail:3.0.0
          env:
            - name: HOSTNAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
          args:
            - -config.file=/etc/promtail/promtail-config.yaml
            - -config.expand-env=true
          resources:
            requests:
              cpu: "100m"
              memory: "100Mi"
            limits:
              cpu: "200m"
              memory: "200Mi" 
          volumeMounts:
            - name: {{ $channel }}-promtail-config
              mountPath: /etc/promtail
            - name: {{ $config.volume.name }}
              mountPath: {{ $config.logConfig.logPath }}/logs
        - name: proxy-filebeat-{{ $channel }} 
          image: nexus-docker.unicommerce.infra:8123/uni-filebeat:6.5.0
          resources:
            requests:
              cpu: "50m"
              memory: "50Mi"
            limits:
              cpu: "100m"
              memory: "100Mi"
          securityContext:
            {{- toYaml $config.filebeat.SecurityContext | nindent 12 }}
          volumeMounts:
            - name: {{ $config.volume.name }}
              mountPath: {{ $config.logConfig.logPath }}/logs
            - name: {{ $channel }}-filebeat-config 
              mountPath: /usr/share/filebeat/filebeat.yml
              subPath: filebeat.yml
      volumes:
        - name: {{ $channel }}-promtail-config
          configMap:
            name: {{ $channel }}-promtail-config
        - name: {{ $channel }}-filebeat-config
          configMap:
            name: {{ $channel }}-filebeat-config
        {{- if $config.clientProperty.enabled }}
        - name: {{ $channel }}-clientproperties-config
          configMap:
            name: {{ $channel }}-clientproperties-config
        {{- end}}
          
  volumeClaimTemplates:
    - metadata:
        name: {{ $config.volume.name }}
      spec:
        accessModes:
          - {{ $config.volume.accessMode }}
        resources:
          requests:
            storage: {{ $config.volume.size }}
        storageClassName: {{ $config.volume.storageClass }}    

      #affinity: {} 
---
apiVersion: v1
kind: Service
metadata:
  name: proxy-{{ $channel }}  
  namespace: {{ $.Values.namespace }}
  labels:
    app.kubernetes.io/name: proxy
    helm.sh/chart: proxy-1.0.0
    app.kubernetes.io/instance: proxy-release-{{ $channel }}
spec:
  type: {{ $.Values.service.type }}
  ports:
    - port: {{ $config.port }}
      protocol: TCP
      targetPort: {{ $config.port }}
  selector:
    app.kubernetes.io/name: proxy
    app.kubernetes.io/instance: proxy-release-{{ $channel }}
 
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: proxy-{{ $channel }}
  namespace: {{ $.Values.namespace }}
spec:
  hosts:
    - {{ $config.hostName }}  
  gateways:
    - istio-system/ingress-gateway 
  http:
    - match:
        - uri:
            prefix: "/"
      route:
        - destination:
            host: proxy-{{ $channel }} 
            port:
              number: {{ $config.port }}
      headers:
        request:
          set:
            X-Forwarded-Proto: "https"
---
{{- if $config.hpa.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: proxy-{{ $channel }}
  namespace: {{ $.Values.namespace }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: StatefulSet
    name: proxy-{{ $channel }}
  minReplicas: {{ $config.hpa.minReplicas }}
  maxReplicas: {{$config.hpa.maxReplicas}}
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ $config.hpa.utilizationCpu }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ $config.hpa.utilizationMemory }}
{{- end}}
---
{{- if $config.clientProperty.enabled }}

apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $channel }}-clientproperties-config
  namespace: proxy
  labels:
    serverName: {{ $channel }}
data:
  client.properties: |
    {{- $config.clientProperty.configFiles.clientProperties | default "" | nindent 4 }}

{{- end }}
---
{{- end}}
{{- end }}