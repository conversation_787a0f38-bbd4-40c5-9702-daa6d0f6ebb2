{{- range $channel, $config := .Values.service.channels }}
{{- if $config.promtail.enabled }}
apiVersion: v1
data:
  promtail-config.yaml: |
    server:
      http_listen_port: 9080
      grpc_listen_port: 0

    positions:
      filename: /var/log/positions.yaml

    clients:
      - url: http://loki-gateway.monitoring.svc.cluster.local:80/loki/api/v1/push
        tenant_id: '{{ $channel }}'
    target_config:
      sync_period: 10s

    scrape_configs:
      - job_name: {{ $channel }}
        static_configs:
          # - targets:
          #     - localhost
          #   labels:
          #     job: {{ $channel }}-access-logs
          #     __path__: {{ $config.logConfig.logPath }}/logs/access_log.*.log
          #     pod_name: ${HOSTNAME}

          # - targets:
          #     - localhost
          #   labels:
          #     job: {{ $channel }}-sqs-logs
          #     __path__: {{ $config.logConfig.logPath }}/logs/sqs-event-log.*
          #     pod_name: ${HOSTNAME}

          # - targets:
          #     - localhost
          #   labels:
          #     job: {{ $channel }}-third-party-logs
          #     __path__: {{ $config.logConfig.logPath }}/logs/third-party-logs/*
          #     pod_name: ${HOSTNAME} 

          - targets:
              - localhost
            labels:
              job: {{ $channel }}-invoice-label-logs
              __path__: {{ $config.logConfig.logPath }}/logs/invoice-label-logger-logs/*
              pod_name: ${HOSTNAME}
          
          - targets:
              - localhost
            labels:
              job: {{ $channel }}-thread-pool-logs
              __path__: {{ $config.logConfig.logPath }}/logs/thread-pool-executor-logs/*
              pod_name: ${HOSTNAME}

          - targets:
              - localhost
            labels:
              job: {{ $channel }}-appllcation-logs
              __path__: {{ $config.logConfig.logPath }}/logs/application.log
              pod_name: ${HOSTNAME}
kind: ConfigMap
metadata:
  name: {{ $channel }}-promtail-config
  namespace: proxy
{{- end}}
---
{{- end }}