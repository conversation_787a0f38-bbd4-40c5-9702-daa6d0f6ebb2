{{- range $channel, $config := .Values.service.channels }}
{{- if $config.filebeat.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $channel }}-filebeat-config
  namespace: proxy
  labels:
    serverName: {{ $channel }}

data:
  filebeat.yml: |-
    filebeat.inputs:
    - type: log
      enabled: true
      paths:
        - {{ $config.logConfig.logPath }}/logs/access_log*.log
      fields:
        target_index: proxy-access-log
      tail_files: true
      ignore_older: 1h

    - type: log
      enabled: true
      paths:
        - {{ $config.logConfig.logPath }}/logs/third-party-logs/third-party-api-metrics.log
      fields:
        target_index: third-party-api-metrics
      tail_files: true
      ignore_older: 1h

    #============================= Filebeat modules ===============================
    filebeat.config.modules:
      # Glob pattern for configuration loading
      path: ${path.config}/modules.d/*.yml

      # Set to true to enable config reloading
      reload.enabled: false

      # Period on which files under path should be checked for changes
      #reload.period: 10s

    #==================== Elasticsearch template setting ==========================
    setup.template.settings:
      index.number_of_shards: 3
      #index.codec: best_compression
      #_source.enabled: false

    #============================== Kibana =====================================
    setup.kibana:

    #----------------------------- Logstash output --------------------------------
    output.logstash:
      # The Logstash hosts
      hosts: ["partnerproxylogstash1-in.unicommerce.infra:5044"]
      #hosts: ["logstash1-in.unicommerce.infra:5044","logstash2-in.unicommerce.infra:5044"]
      loadbalance: true
{{- end }}
---
{{- end }}
