apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: external-ingress
  namespace: argocd
spec:
  syncPolicy:
    preserveResourcesOnDeletion: true
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
  - list:
      elements:
        - name: stgril
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: ril
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
  template:
    metadata:
      name: 'external-ingress-{{.name}}'
    spec:
      project: '{{.project}}'
      sources:
        - repoURL: 'https://github.com/devops-unicommerce/devops-team-env.git'
          targetRevision: HEAD
          path: externalIngress/{{.name}}
      destination:
        name: '{{.cluster}}'
        namespace: '{{.namespace}}'
    
  templatePatch: |
    spec:
    {{- if .autoSync }}
      syncPolicy:
        automated:
          prune: {{ .prune }}
          selfHeal: true
        syncOptions:
        - ApplyOutOfSyncOnly=true
    {{- end }}
