backend:
  - 10.0.5.239
  - 10.0.5.198
  
service:
  port: 8080

task:
  enabled: true
  k8s: false
  service:
    port: 8080
  backend: app2.ril-in.unicommerce.infra

ingress:
  enabled: false
  className: "haproxy-public"
  annotations:
    haproxy-ingress.github.io/session-cookie-name: unicommerce
    haproxy-ingress.github.io/session-cookie-strategy: insert
    haproxy-ingress.github.io/session-cookie-keywords: indirect nocache httponly
    haproxy-ingress.github.io/session-cookie-preserve: false
    haproxy-ingress.github.io/session-cookie-samesite: false
    haproxy-ingress.github.io/session-cookie-dynamic: false
    haproxy-ingress.github.io/session-cookie-shared: false
    ingress.kubernetes.io/affinity: cookie
    haproxy-ingress.github.io/config-backend: |
      http-request set-header X-Forwarded-Proto https
  hosts:
    - host: ril2-test.unicommerce.co.in
