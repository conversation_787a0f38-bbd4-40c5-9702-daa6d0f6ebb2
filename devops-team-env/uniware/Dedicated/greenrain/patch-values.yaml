enabled: true
replicaCount: 0
podSecurityContext:
  fsGroup: 1000
pdb:
  enabled: true
image:
  repository: nexus-docker.unicommerce.infra:8123/uniware
  pullPolicy: IfNotPresent
  tag: 1709.9-k8s
imagePullSecrets:
  - name: nexus-secrets
service:
  type: ClusterIP
  hazelCastport: 5701
  headless:
    enabled: true
arguments:
  - "sed \"/$HOSTNAME/d\" /usr/local/tomcat/logs/server.xml >  /usr/local/tomcat/conf/server.xml && catalina.sh run"
resources:
  limits:
    cpu: 1.0
    memory: 5Gi
  requests:
    cpu: 1.0
    memory: 5Gi
init:
  arguments:
    - mkdir -p /tmp/RotatedAppLogs
    - /tmp/files/customizedTemplates
    - /tmp/files/reconciliationInvoices
    - /tmp/files/saleOrderReturn
    - /tmp/files/import/logs
    - /tmp/files/saleOrder
    - /tmp/files/shippingLabel
nodeSelector:
  karpenter.sh/nodepool: dedicated-ondemand-pool
tolerations:
  - key: "dedicated/ondemand"
    operator: "Exists"
    effect: "NoSchedule"
livenessProbe: {}
readinessProbe:
  exec:
    command:
      - /bin/bash
      - -c
      - |-
        health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/live")
        if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  failureThreshold: 3
  periodSeconds: 30
  successThreshold: 1
  timeoutSeconds: 10
  initialDelaySeconds: 400
env:
  JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms512M -Xmx4000M -XX:MaxPermSize=256M -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -Duser.timezone=Asia/Kolkata -DserverName=GreenRain -Dhazelcast.local.publicAddress=greenrain-uniware-api-0.prod.svc.cluster.local:5701 -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml  -DzookeeperConnectionTimeout=15000 -DhazelcastMembers=greenrain-uniware-api-0.prod.svc.cluster.local:5701,greenrain-uniware-task-0.greenrain-uniware-task.prod.svc.cluster.local:5701 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.e1-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo1.e3-in.unicommerce.infra:27017 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/greenrain#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/greenrain#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DclusterName=GreenRain -DactiveMQBrokerUrl=failover:tcp://activemq.e1-in.unicommerce.infra:61616,tcp://activemq.e1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=GreenRain -DactiveMQBrokerPassword=<path:kv/data/uniware/greenrain#activeMQBrokerPassword> -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -Dcom.sun.management.jmxremote -DAsyncLogger.ThreadNameStrategy=UNCACHED -Dcom.sun.management.jmxremote.port=6969 -DappIdentifier=${HOSTNAME} -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -DactiveMQMaxConnections=5 -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 -DactiveMQ2BrokerUrl=failover:tcp://activemq.e2-in.unicommerce.infra:61616,tcp://activemq.e2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/greenrain#activeMQBrokerPassword> -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DexecuteJobs=false -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017"
volumeMounts:
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
    subPath: vault.properties
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/vaultConfig
    name: uniware-vault-config
  - mountPath: /usr/local/tomcat/conf/context.xml
    subPath: context.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/server.xml
    subPath: server.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/catalina.sh
    subPath: catalina.sh
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/hazelcast.yml
    subPath: hazelcast.yml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
    subPath: gc_monitor.sh
    name: uniware-config
volumes:
  - name: uniware-config
    configMap:
      name: greenrain-uniware
      defaultMode: 0777
  - name: uniware-vault-config
    configMap:
      name: greenrain-vault-uniware
      defaultMode: 0777
ingress:
  enabled: false
  className: "haproxy-public"
  annotations:
    haproxy-ingress.github.io/config-backend: |
      http-request set-header X-Forwarded-Proto https
  hosts:
    - host: brickbrown1.unicommerce.co.in
    - host: decorkart.unicommerce.co.in
    - host: greenrain.unicommerce.co.in
    - host: jaipurugs.unicommerce.co.in
    - host: kadamhaat2.unicommerce.co.in
    - host: kaunteya.unicommerce.co.in
    - host: nakshikathaa.unicommerce.co.in
    - host: okerxakirafashion.unicommerce.co.in
    - host: okerxaltvibes.unicommerce.co.in
    - host: okerxamalfieeceramics.unicommerce.co.in
    - host: okerxarhat.unicommerce.co.in
    - host: okerxartecasa.unicommerce.co.in
    - host: okerxarttidnox.unicommerce.co.in
    - host: okerxasterlane.unicommerce.co.in
    - host: okerxbarcollective.unicommerce.co.in
    - host: okerxbergner.unicommerce.co.in
    - host: okerxcasagold.unicommerce.co.in
    - host: okerxcleandco.unicommerce.co.in
    - host: okerxdessineart.unicommerce.co.in
    - host: okerxedamame.unicommerce.co.in
    - host: okerxerishhome.unicommerce.co.in
    - host: okerxerishome.unicommerce.co.in
    - host: okerxfemora.unicommerce.co.in
    - host: okerxfigliving.unicommerce.co.in
    - host: okerxfunctional.unicommerce.co.in
    - host: okerxgado.unicommerce.co.in
    - host: okerxgemtherapy.unicommerce.co.in
    - host: okerxglimpsehomes.unicommerce.co.in
    - host: okerxgoodmelts.unicommerce.co.in
    - host: okerxgudeelife.unicommerce.co.in
    - host: okerxhitkari.unicommerce.co.in
    - host: okerxholygrail.unicommerce.co.in
    - host: okerxhome4u.unicommerce.co.in
    - host: okerxhomeblitz.unicommerce.co.in
    - host: okerxhouseofsajja.unicommerce.co.in
    - host: okerximperialknots.unicommerce.co.in
    - host: okerxisaaka.unicommerce.co.in
    - host: okerxkansso.unicommerce.co.in
    - host: okerxkarighar.unicommerce.co.in
    - host: okerxklotthe.unicommerce.co.in
    - host: okerxmasonhome.unicommerce.co.in
    - host: okerxmulticreations.unicommerce.co.in
    - host: okerxmunnhome.unicommerce.co.in
    - host: okerxoodaii.unicommerce.co.in
    - host: okerxorangetree.unicommerce.co.in
    - host: okerxpitaraproject.unicommerce.co.in
    - host: okerxpurefinds.unicommerce.co.in
    - host: okerxrayt.unicommerce.co.in
    - host: okerxrosha.unicommerce.co.in
    - host: okerxsamskarahome.unicommerce.co.in
    - host: okerxshaze.unicommerce.co.in
    - host: okerxtablefable.unicommerce.co.in
    - host: okerxtablejoy.unicommerce.co.in
    - host: okerxtablemanners.unicommerce.co.in
    - host: okerxtada.unicommerce.co.in
    - host: okerxtesu.unicommerce.co.in
    - host: okerxthetablefable.unicommerce.co.in
    - host: okerxthoppia.unicommerce.co.in
    - host: okerxvigneto.unicommerce.co.in
    - host: radliving.unicommerce.co.in
    - host: saphed1.unicommerce.co.in
    - host: testing8765.unicommerce.co.in
    - host: testwishlink6911.unicommerce.co.in
    - host: theartment1.unicommerce.co.in
    - host: theartment.unicommerce.co.in
    - host: trjksdjdjwishklshhdee.unicommerce.co.in
  tls: []
istio:
  enabled: true
autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 75
uniware-task:
  enabled: true
  podSecurityContext:
    fsGroup: 1000
  resources:
    limits:
      cpu: 1.0
      memory: 4.4Gi
    requests:
      cpu: 0.5
      memory: 3Gi
  service:
    hazelCastport: 5701
    type: ClusterIP
    headless:
      enabled: true
  spec:
    consolidation:
      enabled: true
  image:
    repository: nexus-docker.unicommerce.infra:8123/uniware
    pullPolicy: Always
    tag: 1709.9-k8s
  nodeSelector:
    karpenter.sh/nodepool: dedicated-spot-pool
  imagePullSecrets:
    - name: nexus-secrets
  tolerations:
    - key: "dedicated/spot"
      operator: "Exists"
      effect: "NoSchedule"
  init:
    arguments:
      - mkdir -p /tmp/RotatedAppLogs
      - /tmp/files/customizedTemplates
      - /tmp/files/reconciliationInvoices
      - /tmp/files/saleOrderReturn
      - /tmp/files/import/logs
      - /tmp/files/saleOrder
      - /tmp/files/shippingLabel
  livenessProbe: {}
  readinessProbe: {}
  env:
    JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms512M -Xmx3500M -XX:MaxPermSize=256M -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -Duser.timezone=Asia/Kolkata -DserverName=GreenRain -Dhazelcast.local.publicAddress=greenrain-uniware-task-0.greenrain-uniware-task.prod.svc.cluster.local:5701 -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DhazelcastMembers=-DhazelcastMembers=greenrain-uniware-api-0.prod.svc.cluster.local:5701,greenrain-uniware-task-0.greenrain-uniware-task.prod.svc.cluster.local:5701 -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.e1-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo1.e3-in.unicommerce.infra:27017 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/greenrain#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/greenrain#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DclusterName=GreenRain -DactiveMQBrokerUrl=failover:tcp://activemq.e1-in.unicommerce.infra:61616,tcp://activemq.e1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=GreenRain -DactiveMQBrokerPassword=<path:kv/data/uniware/greenrain#activeMQBrokerPassword> -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -Dcom.sun.management.jmxremote -DAsyncLogger.ThreadNameStrategy=UNCACHED -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -DactiveMQMaxConnections=5 -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 -DactiveMQ2BrokerUrl=failover:tcp://activemq.e2-in.unicommerce.infra:61616,tcp://activemq.e2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/greenrain#activeMQBrokerPassword> -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DexecuteJobs=true -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017"
  volumeMounts:
    - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
      subPath: vault.properties
      name: uniware-config
    - mountPath: /usr/local/tomcat/logs/vaultConfig
      name: uniware-vault-config
    - mountPath: /usr/local/tomcat/conf/context.xml
      subPath: context.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/conf/server.xml
      subPath: server.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/catalina.sh
      subPath: catalina.sh
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/hazelcast.yml
      subPath: hazelcast.yml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
      subPath: gc_monitor.sh
      name: uniware-config
  volumes:
    - name: uniware-config
      configMap:
        name: greenrain-uniware
        defaultMode: 0777
    - name: uniware-vault-config
      configMap:
        name: greenrain-vault-uniware
        defaultMode: 0777
global:
  filebeat:
    config:
      name: ${PODNAME}.greenrain-in.unicommerce.infra
      hosts:
        - "logstash1-in.unicommerce.infra:5044"
        - "logstash2-in.unicommerce.infra:5044"
  promtail:
    config:
      clients:
        - url: http://loki-gateway.monitoring.svc.cluster.local:80/loki/api/v1/push
          tenant_id: 'greenrain'
