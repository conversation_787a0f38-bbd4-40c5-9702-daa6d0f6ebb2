apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: uniware-dedicated
  namespace: argocd
spec:
  syncPolicy:
    preserveResourcesOnDeletion: true
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
  - list:
      elements:
        - name: stgdevops1
          autoSync: true
          prune: true
          project: staging
          namespace: staging
          cluster: staging-cluster
        - name: unistage
          autoSync: true
          prune: true
          project: staging
          namespace: staging
          cluster: staging-cluster
        - name: wmsuat1
          autoSync: true
          prune: true
          project: staging
          namespace: staging
          cluster: staging-cluster
        - name: mamaearth
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: tcns
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: hamilton
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: brandstudio
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: capl
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: bewakoofwms
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: fabindialtd
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: pep
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: mensabrand
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: brightlifecareindia
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: buildingparadise
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cred
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: dhani
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: donoshopmp
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: eternzfashionprivatelimited
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: greenrain
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: healthkart
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: pharmeasy
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: portronics
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: stgril
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: amydus2
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: bajaao
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: pmcg
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: leayanglobal
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: wishlink
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: urbanclap
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
  template:
    metadata:
      name: 'uniware-{{.name}}'
    spec:
      project: '{{.project}}'
      sources:
        - repoURL: 'https://github.com/devops-unicommerce/devops-team-env.git'
          targetRevision: HEAD
          path: uniware/Dedicated/{{.name}}
      destination:
        name: '{{.cluster}}'
        namespace: '{{.namespace}}'
    
  templatePatch: |
    spec:
    {{- if .autoSync }}
      syncPolicy:
        automated:
          prune: {{ .prune }}
          selfHeal: true
        syncOptions:
        - ApplyOutOfSyncOnly=true
    {{- end }}