enabled: false
replicaCount: 1
podSecurityContext:
  fsGroup: 1000
pdb:
  enabled: true
service:
  hazelCastport: 5701
  type: ClusterIP
  headless:
    enabled: true
image:
  repository: nexus-docker.unicommerce.infra:8123/uniware
  pullPolicy: IfNotPresent
  tag: stg_3693-k8s
imagePullSecrets:
  - name: nexus-secrets
nodeSelector:
  karpenter.sh/nodepool: devops-spot-pool
arguments:
  - "sed \"/$HOSTNAME/d\" /usr/local/tomcat/logs/server.xml >  /usr/local/tomcat/conf/server.xml && catalina.sh run"
tolerations:
  - key: "devops/spot"
    operator: "Exists"
    effect: "NoSchedule"
resources:
  limits:
    cpu: 1.4
    memory: 6.4Gi
  requests:
    cpu: 1.4
    memory: 6.4Gi
init:
  arguments:
    - mkdir -p /tmp/RotatedAppLogs
    - /tmp/files/customizedTemplates
    - /tmp/files/reconciliationInvoices
    - /tmp/files/saleOrderReturn
    - /tmp/files/import/logs
    - /tmp/files/saleOrder
    - /tmp/files/shippingLabel
livenessProbe: {}
readinessProbe:
  exec:
    command:
    - /bin/bash
    - -c
    - |-
      health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/live")
      if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  failureThreshold: 3
  periodSeconds: 60
  successThreshold: 1
  timeoutSeconds: 40
  initialDelaySeconds: 180
env:
  JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar 
    -XX:MaxPermSize=256M -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300
    -Duser.timezone=Asia/Kolkata -DserverName=StgDevops1 -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml 
    -Dhazelcast.local.publicAddress=${HOSTNAME}.stgdevops1-uniware-api.staging.svc.cluster.local:5701 
    -DhazelcastMembers=stgdevops1-uniware-api-0.stgdevops1-uniware-api.staging.svc.cluster.local:5701,stgdevops1-uniware-task-0.stgdevops1-uniware-task.staging.svc.cluster.local:5701
    -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 
    -DzookeeperUrl=zookeeper.e1-in.unicommerce.infra:2181 -DexecuteJobs=false
    -DtenantSpecificMongoHosts=mongo1.e1-in.unicommerce.infra:27017,mongo2.e1-in.unicommerce.infra:27017 
    -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 
    -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5
    -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/stgdevops1#activeMQBrokerPassword> 
    -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 
    -DclusterName=StgDevops1 -DactiveMQBrokerUrl=failover:tcp://activemq.c1-in.unicommerce.infra:61616,tcp://activemq.c1-in.unicommerce.infra:61616 
    -DactiveMQBrokerUsername=unicommercemq -DserverNames=StgDevops1 -DactiveMQBrokerPassword=<path:kv/data/uniware/stgdevops1#activeMQBrokerPassword> 
    -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -Dcom.sun.management.jmxremote 
    -DAsyncLogger.ThreadNameStrategy=UNCACHED -Dcom.sun.management.jmxremote.port=6969 
    -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -DactiveMQMaxConnections=5 
    -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 -DactiveMQ2BrokerUrl=failover:tcp://activemq.c2-in.unicommerce.infra:61616,tcp://activemq.c2-in.unicommerce.infra:61616 
    -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/stgdevops1#activeMQBrokerPassword> 
    -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 
    -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 
    -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 
    -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin 
    -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/stgdevops1#prefetchActiveMQ2BrokerPassword> 
    -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616
    -javaagent:/usr/local/tomcat/conf/uniwareConfig/pinpoint-agent-2.5.2/pinpoint-bootstrap-2.5.2.jar 
    -Dpinpoint.agentId=${HOSTNAME} -Dpinpoint.applicationName=${HOSTNAME} -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) 
    -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource 
    -Xms512M -Xmx4500M"
volumeMounts:
- mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
  subPath: vault.properties
  name: uniware-config
- mountPath: /usr/local/tomcat/conf/uniwareConfig/vaultCredentials.yml
  subPath: vaultCredentials.yml
  name: uniware-config
- mountPath: /usr/local/tomcat/conf/context.xml
  subPath: context.xml
  name: uniware-config
- mountPath: /usr/local/tomcat/logs/server.xml
  subPath: server.xml
  name: uniware-config
- mountPath: /usr/local/tomcat/bin/catalina.sh
  subPath: catalina.sh
  name: uniware-config
- mountPath: /usr/local/tomcat/bin/hazelcast.yml
  subPath: hazelcast.yml
  name: uniware-config
volumes:
- name: uniware-config
  configMap:
    name: stgdevops1-uniware
    defaultMode: 0777
ingress:
  enabled: false
  className: "haproxy-public"
  hosts:
    - host: stgdevops1.unicommerce.com
  tls: []

istio:
  enabled: true

autoscaling:
  enabled: false

uniware-task:
  enabled: false
  podSecurityContext:
    fsGroup: 1000
  image:
    repository: nexus-docker.unicommerce.infra:8123/uniware
    pullPolicy: IfNotPresent
    tag: stg_3693-k8s
  resources:
    limits:
      cpu: 1.4
      memory: 6.4Gi
    requests:
      cpu: 1.4
      memory: 6.4Gi
  nodeSelector:
    karpenter.sh/nodepool: devops-spot-pool
  arguments:
    - "sed \"/$HOSTNAME/d\" /usr/local/tomcat/logs/server.xml >  /usr/local/tomcat/conf/server.xml && catalina.sh run"
  imagePullSecrets:
    - name: nexus-secrets
  tolerations:
    - key: "devops/spot"
      operator: "Exists"
      effect: "NoSchedule"
  init:
    arguments:
      - mkdir -p /tmp/RotatedAppLogs
      - /tmp/files/customizedTemplates
      - /tmp/files/reconciliationInvoices
      - /tmp/files/saleOrderReturn
      - /tmp/files/import/logs
      - /tmp/files/saleOrder
      - /tmp/files/shippingLabel
  livenessProbe:  {}
  readinessProbe:
    exec:
      command:
      - /bin/bash
      - -c
      - |-
        health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/live")
        if [[ ${health_status} -ne 200 ]]; then exit 1; fi
    failureThreshold: 3
    periodSeconds: 30
    successThreshold: 1
    timeoutSeconds: 10
    initialDelaySeconds: 180
  env:
    JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar 
    -XX:MaxPermSize=256M -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300
    -Duser.timezone=Asia/Kolkata -DserverName=StgDevops1 -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml 
    -Dhazelcast.local.publicAddress=${HOSTNAME}.stgdevops1-uniware-task.staging.svc.cluster.local:5701
    -DhazelcastMembers=stgdevops1-uniware-task-0.stgdevops1-uniware-task.staging.svc.cluster.local:5701,stgdevops1-uniware-api-0.stgdevops1-uniware-api.staging.svc.cluster.local:5701
    -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 
    -DzookeeperUrl=zookeeper.e1-in.unicommerce.infra:2181 
    -DtenantSpecificMongoHosts=mongo1.e1-in.unicommerce.infra:27017,mongo2.e1-in.unicommerce.infra:27017 
    -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 
    -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 
    -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/stgdevops1#activeMQBrokerPassword> 
    -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 
    -DclusterName=StgDevops1 -DactiveMQBrokerUrl=failover:tcp://activemq.c1-in.unicommerce.infra:61616,tcp://activemq.c1-in.unicommerce.infra:61616 
    -DactiveMQBrokerUsername=unicommercemq -DserverNames=StgDevops1 -DactiveMQBrokerPassword=<path:kv/data/uniware/stgdevops1#activeMQBrokerPassword> 
    -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -Dcom.sun.management.jmxremote 
    -DAsyncLogger.ThreadNameStrategy=UNCACHED -Dcom.sun.management.jmxremote.port=6969 
    -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -DactiveMQMaxConnections=5 
    -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 -DactiveMQ2BrokerUrl=failover:tcp://activemq.c2-in.unicommerce.infra:61616,tcp://activemq.c2-in.unicommerce.infra:61616 
    -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/stgdevops1#activeMQBrokerPassword> 
    -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 
    -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 
    -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 
    -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin 
    -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/stgdevops1#prefetchActiveMQ2BrokerPassword> 
    -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DexecuteJobs=false 
    -javaagent:/usr/local/tomcat/conf/uniwareConfig/pinpoint-agent-2.5.2/pinpoint-bootstrap-2.5.2.jar 
    -Dpinpoint.agentId=${HOSTNAME} -Dpinpoint.applicationName=${HOSTNAME} -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) 
    -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource 
    -Xms512M -Xmx4500M"
  volumeMounts:
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
    subPath: vault.properties
    name: uniware-config
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vaultCredentials.yml
    subPath: vaultCredentials.yml
    name: uniware-config
  - mountPath: /usr/local/tomcat/conf/context.xml
    subPath: context.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/server.xml
    subPath: server.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/catalina.sh
    subPath: catalina.sh
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/hazelcast.yml
    subPath: hazelcast.yml
    name: uniware-config
  volumes:
  - name: uniware-config
    configMap:
      name: stgdevops1-uniware
      defaultMode: 0777
  service:
    hazelCastport: 5701
    type: ClusterIP
    headless:
      enabled: true
global:
  filebeat:
    image:
      repository: elastic/filebeat
      tag: 7.15.2
    config:
      file: |
        path.data: /usr/local/tomcat/logs/filebeat
        filebeat.inputs:
        - type: log
          enabled: true
          paths:
            - /usr/local/tomcat/logs/access_log*.txt
          fields:
            release_version: '${RELEASE_VERSION}'
          file_identity.path: ~


        filebeat.config.modules:
          path: ${path.config}/modules.d/*.yml
          reload.enabled: false
        
        filebeat.config.inputs:
          enabled: true
          path: ${path.config}/configs/*.yml
          reload.enabled: true
          reload.period: 10s

        setup.template.settings:
          index.number_of_shards: 3

        name: {{ .Values.global.filebeat.config.name | quote }}

        setup.kibana:

        output.logstash:
          hosts:
          {{- range .Values.global.filebeat.config.hosts }}
            - {{ . | quote }}
          {{- end }}
          loadbalance: true
      name: ${PODNAME}.stgdevops1-in.unicommerce.infra
      hosts: 
        - "logstash1-in.unicommerce.infra:5044"
        - "logstash2-in.unicommerce.infra:5044"
  promtail:
    config:
      clients:
        - url: http://loki-gateway.monitoring.svc.cluster.local:80/loki/api/v1/push
          tenant_id: 'stgdevops1'
