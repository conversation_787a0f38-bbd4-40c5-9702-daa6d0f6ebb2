enabled: true
replicaCount: 0
podSecurityContext:
  fsGroup: 1000
pdb:
  enabled: true
service:
  hazelCastport: 5701
  type: ClusterIP
  headless:
    enabled: true
image:
  repository: nexus-docker.unicommerce.infra:8123/uniware
  pullPolicy: IfNotPresent
  tag: 1709.9-k8s
imagePullSecrets:
  - name: nexus-secrets
arguments:
  - "sed \"/$HOSTNAME/d\" /usr/local/tomcat/logs/server.xml >  /usr/local/tomcat/conf/server.xml && catalina.sh run"
resources:
  limits:
    cpu: 0.5
    memory: 3.5Gi
  requests:
    cpu: 0.5
    memory: 3.5Gi
init:
  arguments:
    - mkdir -p /tmp/RotatedAppLogs
    - /tmp/files/customizedTemplates
    - /tmp/files/reconciliationInvoices
    - /tmp/files/saleOrderReturn
    - /tmp/files/import/logs
    - /tmp/files/saleOrder
    - /tmp/files/shippingLabel
nodeSelector:
  karpenter.sh/nodepool: dedicated-ondemand-pool
tolerations:
  - key: "dedicated/ondemand"
    operator: "Exists"
    effect: "NoSchedule"
livenessProbe: {}
readinessProbe:
  exec:
    command:
    - /bin/bash
    - -c
    - |-
      health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/live")
      if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  failureThreshold: 3
  periodSeconds: 30
  successThreshold: 1
  timeoutSeconds: 10
  initialDelaySeconds: 180
env:
  JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms512M -Xmx2650M 
    -XX:MaxPermSize=256M -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -Duser.timezone=Asia/Kolkata 
    -DappIdentifier=${HOSTNAME} -DserverName=BuildingParadise -Dhazelcast.local.publicAddress=${HOSTNAME}.prod.svc.cluster.local:5701
    -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml 
    -DhazelcastMembers=buildingparadise-uniware-task-0.buildingparadise-uniware-task.prod.svc.cluster.local:5701,buildingparadise-uniware-api-0.prod.svc.cluster.local:5701 
    -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.e1-in.unicommerce.infra:2181 
    -DtenantSpecificMongoHosts=mongo1.e3-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 
    -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq 
    -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/buildingparadise#activeMQBrokerPassword>  -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 
    -DclusterName=BuildingParadise -DactiveMQBrokerUrl=failover:tcp://activemq.e1-in.unicommerce.infra:61616,tcp://activemq.e1-in.unicommerce.infra:61616 
    -DactiveMQBrokerUsername=unicommercemq -DserverNames=BuildingParadise -DactiveMQBrokerPassword=<path:kv/data/uniware/buildingparadise#activeMQBrokerPassword>  
    -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -Dcom.sun.management.jmxremote -DAsyncLogger.ThreadNameStrategy=UNCACHED 
    -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false 
    -DactiveMQMaxConnections=5 -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 
    -DactiveMQ2BrokerUrl=failover:tcp://activemq.e2-in.unicommerce.infra:61616,tcp://activemq.e2-in.unicommerce.infra:61616 
    -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/buildingparadise#activeMQBrokerPassword>  -DactiveMQ2MaxConnections=5 
    -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 
    -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 
    -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017  -DexecuteJobs=false 
    -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017
    -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump 
    -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource 
    -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin 
    -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/buildingparadise#prefetchActiveMQ2BrokerPassword>  -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 
    -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017"
volumeMounts:
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
    subPath: vault.properties
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/vaultConfig
    name: uniware-vault-config
  - mountPath: /usr/local/tomcat/conf/context.xml
    subPath: context.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/server.xml
    subPath: server.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/catalina.sh
    subPath: catalina.sh
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/hazelcast.yml
    subPath: hazelcast.yml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
    subPath: gc_monitor.sh
    name: uniware-config
volumes:
- name: uniware-config
  configMap:
    name: buildingparadise-uniware
    defaultMode: 0777
- name: uniware-vault-config
  configMap:
    name: buildingparadise-vault-uniware
    defaultMode: 0777
ingress:
  enabled: false
  className: "haproxy-public"
  annotations:
    haproxy-ingress.github.io/config-backend: |
      http-request set-header X-Forwarded-Proto https
  hosts:
    - host: acclimited.unicommerce.com
    - host: actiontesa.unicommerce.com
    - host: aisen.unicommerce.com
    - host: alcove.unicommerce.com
    - host: ambujacementslimited.unicommerce.com
    - host: americanstandard.unicommerce.com
    - host: bhutantuff.unicommerce.com
    - host: bigcem.unicommerce.com
    - host: birlauttam.unicommerce.com
    - host: buildingparadisetest01.unicommerce.com
    - host: buildingparadisetest02.unicommerce.com
    - host: buildingparadise.unicommerce.com
    - host: buildintest02.unicommerce.com
    - host: buildintest03.unicommerce.com
    - host: buildintest04.unicommerce.com
    - host: buildintest05.unicommerce.com
    - host: buildintest06.unicommerce.com
    - host: carolieto.unicommerce.com
    - host: carysilsouthdelhi.unicommerce.com
    - host: carysil.unicommerce.com
    - host: cenit.unicommerce.com
    - host: cera.unicommerce.com
    - host: compact.unicommerce.com
    - host: concrete.unicommerce.com
    - host: delixidesign.unicommerce.com
    - host: dorset.unicommerce.com
    - host: dukelocks.unicommerce.com
    - host: dulux.unicommerce.com
    - host: enox.unicommerce.com
    - host: essel.unicommerce.com
    - host: excel.unicommerce.com
    - host: excelwallpapers.unicommerce.com
    - host: ferrousfaridabad.unicommerce.com
    - host: ferrous.unicommerce.com
    - host: glen.unicommerce.com
    - host: godrejlive.unicommerce.com
    - host: greenlam.unicommerce.com
    - host: grohe.unicommerce.com
    - host: grotto.unicommerce.com
    - host: havells.unicommerce.com
    - host: hbmgold.unicommerce.com
    - host: hbmgolduttarpradesh.unicommerce.com
    - host: hettichindiapvtltd.unicommerce.com
    - host: hindware.unicommerce.com
    - host: ipsa.unicommerce.com
    - host: jklakshmicement.unicommerce.com
    - host: jksupercement.unicommerce.com
    - host: keisouthdelhi.unicommerce.com
    - host: kei.unicommerce.com
    - host: kesseboehm.unicommerce.com
    - host: kohler.unicommerce.com
    - host: merino.unicommerce.com
    - host: mozio.unicommerce.com
    - host: nipponpaintghaziabad.unicommerce.com
    - host: nipponpaint.unicommerce.com
    - host: niralilive.unicommerce.com
    - host: nirali.unicommerce.com
    - host: novamax.unicommerce.com
    - host: nuvoco.unicommerce.com
    - host: omniscrews.unicommerce.com
    - host: orientbell.unicommerce.com
    - host: ozone.unicommerce.com
    - host: peacockrevera.unicommerce.com
    - host: prabhatdelhi.unicommerce.com
    - host: primegoldchaukhat.unicommerce.com
    - host: ptcengineering.unicommerce.com
    - host: purever.unicommerce.com
    - host: reintech.unicommerce.com
    - host: sakarni.unicommerce.com
    - host: sentiniflopipes.unicommerce.com
    - host: shalimarpaints.unicommerce.com
    - host: shankarglass.unicommerce.com
    - host: shreecement.unicommerce.com
    - host: shubhabptesting.unicommerce.com
    - host: skydecor.unicommerce.com
    - host: smyphony.unicommerce.com
    - host: somanygvt.unicommerce.com
    - host: somany.unicommerce.com
    - host: somanyuttarpradesh.unicommerce.com
    - host: stanleyblackanddecker.unicommerce.com
    - host: stylam.unicommerce.com
    - host: sunhearrt.unicommerce.com
    - host: trimurtisarawativihar.unicommerce.com
    - host: trimurti.unicommerce.com
    - host: ultratech.unicommerce.com
    - host: ventoluxe.unicommerce.com
    - host: vguardlive.unicommerce.com
    - host: whirpool.unicommerce.com
    - host: xiaomi.unicommerce.com
    - host: yale.unicommerce.com
  tls: []
autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 75

istio:
  enabled: true

uniware-task:
  enabled: true
  podSecurityContext:
    fsGroup: 1000
  resources:
    limits:
      cpu: 0.8
      memory: 8Gi
    requests:
      cpu: 0.5
      memory: 4.5Gi
  service:
    hazelCastport: 5701
    type: ClusterIP
    headless:
      enabled: true
  spec:
    consolidation:
      enabled: true
  image:
    repository: nexus-docker.unicommerce.infra:8123/uniware
    pullPolicy: Always
    tag: 1709.9-k8s
  nodeSelector:
    karpenter.sh/nodepool: dedicated-spot-pool
  imagePullSecrets:
    - name: nexus-secrets
  tolerations:
    - key: "dedicated/spot"
      operator: "Exists"
      effect: "NoSchedule"
  init:
    arguments:
      - mkdir -p /tmp/RotatedAppLogs
      - /tmp/files/customizedTemplates
      - /tmp/files/reconciliationInvoices
      - /tmp/files/saleOrderReturn
      - /tmp/files/import/logs
      - /tmp/files/saleOrder
      - /tmp/files/shippingLabel
  livenessProbe: {}
  readinessProbe: {}
  env:
    JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms512M -Xmx6000M 
      -XX:MaxPermSize=256M -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -Duser.timezone=Asia/Kolkata 
      -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin 
      -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/buildingparadise#prefetchActiveMQ2BrokerPassword>  -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 
      -DappIdentifier=app2 -DserverName=BuildingParadise -Dhazelcast.local.publicAddress=buildingparadise-uniware-task-0.buildingparadise-uniware-task.prod.svc.cluster.local:5701 
      -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml 
      -DhazelcastMembers=buildingparadise-uniware-task-0.buildingparadise-uniware-task.prod.svc.cluster.local:5701,buildingparadise-uniware-api-0.prod.svc.cluster.local:5701 
      -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.e1-in.unicommerce.infra:2181 
      -DtenantSpecificMongoHosts=mongo1.e3-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 
      -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq 
      -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/buildingparadise#activeMQBrokerPassword>  -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 
      -DclusterName=BuildingParadise -DactiveMQBrokerUrl=failover:tcp://activemq.e1-in.unicommerce.infra:61616,tcp://activemq.e1-in.unicommerce.infra:61616 
      -DactiveMQBrokerUsername=unicommercemq -DserverNames=BuildingParadise -DactiveMQBrokerPassword=<path:kv/data/uniware/buildingparadise#activeMQBrokerPassword>  
      -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -Dcom.sun.management.jmxremote -DAsyncLogger.ThreadNameStrategy=UNCACHED 
      -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false 
      -DactiveMQMaxConnections=5 -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 -DactiveMQ2BrokerUrl=failover:tcp://activemq.e2-in.unicommerce.infra:61616,tcp://activemq.e2-in.unicommerce.infra:61616 
      -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/buildingparadise#activeMQBrokerPassword>  -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 
      -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword 
      -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017  
      -DexecuteJobs=true -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017
      -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails 
      -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource 
      -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017"
  volumeMounts:
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
    subPath: vault.properties
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/vaultConfig
    name: uniware-vault-config
  - mountPath: /usr/local/tomcat/conf/context.xml
    subPath: context.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/conf/server.xml
    subPath: server.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/catalina.sh
    subPath: catalina.sh
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/hazelcast.yml
    subPath: hazelcast.yml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
    subPath: gc_monitor.sh
    name: uniware-config
  volumes:
  - name: uniware-config
    configMap:
      name: buildingparadise-uniware
      defaultMode: 0777
  - name: uniware-vault-config
    configMap:
      name: buildingparadise-vault-uniware
      defaultMode: 0777
global:
  filebeat:
    config:
      name: ${PODNAME}.buildingparadise-in.unicommerce.infra
      hosts: 
        - "logstash1-in.unicommerce.infra:5044"
        - "logstash2-in.unicommerce.infra:5044"
  promtail:
    config:
      clients:
        - url: http://loki-gateway.monitoring.svc.cluster.local:80/loki/api/v1/push
          tenant_id: 'buildingparadise'
