enabled: false
replicaCount: 0
podSecurityContext:
  fsGroup: 1000
pdb:
  enabled: true
image:
  repository: nexus-docker.unicommerce.infra:8123/uniware
  pullPolicy: IfNotPresent
  tag: stg_3437-k8s
imagePullSecrets:
  - name: nexus-secrets
service:
  hazelCastport: 5701
  type: ClusterIP
  headless:
    enabled: true
resources:
  limits:
    cpu: 2
    memory: 4Gi
  requests:
    cpu: 2
    memory: 4Gi
nodeSelector:
  karpenter.sh/nodepool: dedicated-spot-pool
tolerations:
  - key: "dedicated/spot"
    operator: "Exists"
    effect: "NoSchedule"
init:
  arguments:
    - mkdir -p /tmp/RotatedAppLogs
    - /tmp/files/customizedTemplates
    - /tmp/files/reconciliationInvoices
    - /tmp/files/saleOrderReturn
    - /tmp/files/import/logs
    - /tmp/files/saleOrder
    - /tmp/files/shippingLabel
livenessProbe:
  exec:
    command:
    - /bin/bash
    - -c
    - |-
      health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/health")
      if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  failureThreshold: 3
  periodSeconds: 30
  successThreshold: 1
  timeoutSeconds: 10
  initialDelaySeconds: 600
readinessProbe:
  exec:
    command:
    - /bin/bash
    - -c
    - |-
      health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/health")
      if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  failureThreshold: 3
  periodSeconds: 30
  successThreshold: 1
  timeoutSeconds: 10
  initialDelaySeconds: 180
env:
  JAVA_OPTS: "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=8000 
    -javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms512M -Xmx1136M 
    -XX:MaxPermSize=256M -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -Duser.timezone=Asia/Kolkata -DserverName=WmsUat1 
    -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 
    -DzookeeperUrl=zookeeper.e1-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo1.e2-in.unicommerce.infra:27017,mongo2.e2-in.unicommerce.infra:27017 
    -DcommonMongoHosts=mongo1.e2-in.unicommerce.infra:27017,mongo2.e2-in.unicommerce.infra:27017 -DtraceLogActiveMQMaxConnections=2 
    -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/wmsuat1#activeMQBrokerPassword>  
    -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DclusterName=WmsUat1 
    -DactiveMQBrokerUrl=failover:tcp://activemq.c1-in.unicommerce.infra:61616,tcp://activemq.c1-in.unicommerce.infra:61616 
    -DactiveMQBrokerUsername=unicommercemq -DserverNames=WmsUat1 -DactiveMQBrokerPassword=<path:kv/data/uniware/wmsuat1#activeMQBrokerPassword>  -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr 
    -Dcom.sun.management.jmxremote -DAsyncLogger.ThreadNameStrategy=UNCACHED 
    -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false 
    -DactiveMQMaxConnections=5 -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 
    -DactiveMQ2BrokerUrl=failover:tcp://activemq.c2-in.unicommerce.infra:61616,tcp://activemq.c2-in.unicommerce.infra:61616 
    -DactiveMQ2BrokerUsername=unicommercemq 
    -DactiveMQ2BrokerPassword=<path:kv/data/uniware/wmsuat1#activeMQBrokerPassword>  -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 
    -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword 
    -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DexecuteJobs=false -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017
    -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump 
    -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource 
    -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin 
    -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/wmsuat1#prefetchActiveMQ2BrokerPassword>  -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 
    -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 
    -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017
    -Dhazelcast.local.publicAddress=${HOSTNAME}.wmsuat1-uniware-api.staging.svc.cluster.local
    -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml 
    -DhazelcastMembers=wmsuat1-uniware-task-0.wmsuat1-uniware-task.staging.svc.cluster.local,wmsuat1-uniware-api-0.wmsuat1-uniware-api.staging.svc.cluster.local,wmsuat1-uniware-api-1.wmsuat1-uniware-api.staging.svc.cluster.local"
volumeMounts:
- mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
  subPath: vault.properties
  name: uniware-config
- mountPath: /usr/local/tomcat/logs/vaultConfig
  name: uniware-vault-config
- mountPath: /usr/local/tomcat/conf/context.xml
  subPath: context.xml
  name: uniware-config
- mountPath: /usr/local/tomcat/conf/server.xml
  subPath: server.xml
  name: uniware-config
- mountPath: /usr/local/tomcat/bin/catalina.sh
  subPath: catalina.sh
  name: uniware-config
- mountPath: /usr/local/tomcat/bin/hazelcast.yml
  subPath: hazelcast.yml
  name: uniware-config
volumes:
- name: uniware-config
  configMap:
    name: wmsuat1-uniware
    defaultMode: 0777
- name: uniware-vault-config
  configMap:
    name: wmsuat1-vault-uniware
    defaultMode: 0777
ingress:
  enabled: false
  className: "haproxy-public"
  hosts:
    - host: wmsuat1.unicommerce.com
    - host: wmsuat1-unireco.unicommerce.com
  tls: []

istio:
  enabled: true

autoscaling:
  enabled: false

uniware-task:
  enabled: false
  podSecurityContext:
    fsGroup: 1000
  resources:
    limits:
      cpu: 3
      memory: 12Gi
    requests:
      cpu: 3
      memory: 12Gi
  nodeSelector:
    karpenter.sh/nodepool: dedicated-spot-pool
  tolerations:
    - key: "dedicated/spot"
      operator: "Exists"
      effect: "NoSchedule"
  image:
    repository: nexus-docker.unicommerce.infra:8123/uniware
    pullPolicy: IfNotPresent
    tag: stg_3270-k8s
  imagePullSecrets:
    - name: nexus-secrets
  init:
    arguments:
      - mkdir -p /tmp/RotatedAppLogs
      - /tmp/files/customizedTemplates
      - /tmp/files/reconciliationInvoices
      - /tmp/files/saleOrderReturn
      - /tmp/files/import/logs
      - /tmp/files/saleOrder
      - /tmp/files/shippingLabel
  livenessProbe:
    exec:
      command:
      - /bin/bash
      - -c
      - |-
        health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/health")
        if [[ ${health_status} -ne 200 ]]; then exit 1; fi
    failureThreshold: 3
    periodSeconds: 30
    successThreshold: 1
    timeoutSeconds: 10
    initialDelaySeconds: 600
  readinessProbe:
    exec:
      command:
      - /bin/bash
      - -c
      - |-
        health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/health")
        if [[ ${health_status} -ne 200 ]]; then exit 1; fi
    failureThreshold: 3
    periodSeconds: 30
    successThreshold: 1
    timeoutSeconds: 10
    initialDelaySeconds: 180
  env:
    JAVA_OPTS: "-Djava.security.egd=file:/dev/./urandom -javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar 
      -Xms512M -Xmx9303M -XX:MaxPermSize=256M -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 
      -Dlog4j2.enableJndiJms=true -Duser.timezone=Asia/Kolkata -DserverName=wmsuat1 
      -Dhazelcast.local.publicAddress=${HOSTNAME}.wmsuat1-uniware-task.staging.svc.cluster.local
      -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml 
      -DhazelcastMembers=wmsuat1-uniware-task-0.wmsuat1-uniware-task.staging.svc.cluster.local,wmsuat1-uniware-api-0.wmsuat1-uniware-api.staging.svc.cluster.local,wmsuat1-uniware-api-1.wmsuat1-uniware-api.staging.svc.cluster.local -DzookeeperConnectionTimeout=15000 
      -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper1.e2-in.unicommerce.infra:2181 
      -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 
      -DtenantSpecificMongoHosts=mongo1.e1-in.unicommerce.infra:27017,mongo2.e1-in.unicommerce.infra:27017 
      -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 
      -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq 
      -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/wmsuat1#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 
      -DclusterName=wmsuat1 -DactiveMQBrokerUrl=failover:tcp://activemq.c1-in.unicommerce.infra:61616,tcp://activemq.c1-in.unicommerce.infra:61616 
      -DactiveMQBrokerUsername=unicommercemq -DserverNames=wmsuat1 -DactiveMQBrokerPassword=<path:kv/data/uniware/wmsuat1#activeMQBrokerPassword> 
      -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -Dcom.sun.management.jmxremote -DAsyncLogger.ThreadNameStrategy=UNCACHED 
      -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false 
      -DactiveMQMaxConnections=5 -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 
      -DactiveMQ2BrokerUrl=failover:tcp://activemq.c2-in.unicommerce.infra:61616,tcp://activemq.c2-in.unicommerce.infra:61616 
      -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/wmsuat1#activeMQBrokerPassword> -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 
      -DmongoMaxWaitTime=5000 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 
      -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword  -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 
      -DexecuteJobs=false -Djdk.tls.client.protocols=TLSv1.2
      -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:+PrintGCDetails -XX:OnOutOfMemoryError=/bin/HeapDump 
      -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource 
      -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 
      -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/wmsuat1#prefetchActiveMQ2BrokerPassword>  
      -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 
      -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017"
  volumeMounts:
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
    subPath: vault.properties
    name: uniware-config
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vaultCredentials.yml
    subPath: vaultCredentials.yml
    name: uniware-config
  - mountPath: /usr/local/tomcat/conf/context.xml
    subPath: context.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/conf/server.xml
    subPath: server.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/catalina.sh
    subPath: catalina.sh
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/hazelcast.yml
    subPath: hazelcast.yml
    name: uniware-config
  volumes:
  - name: uniware-config
    configMap:
      name: wmsuat1-uniware
      defaultMode: 0777
  service:
    hazelCastport: 5701
    type: ClusterIP
    headless:
      enabled: true
global:
  filebeat:
    config:
      name: ${PODNAME}.wmsuat1-in.unicommerce.infra
      hosts: 
        - "logstash1-in.unicommerce.infra:5044"
        - "logstash2-in.unicommerce.infra:5044"
  promtail:
    config:
      clients:
        - url: http://loki-gateway.monitoring.svc.cluster.local:80/loki/api/v1/push
          tenant_id: 'wmsuat1'
  efs:
    enabled: false
    accessModes: ReadWriteMany
    storage: 10Gi
    storageClass: efs-sc
    volumeId: fs-0f59aeeaceff595d8

