enabled: true
replicaCount: 0
podSecurityContext:
  fsGroup: 1000
pdb:
  enabled: true
service:
  hazelCastport: 5701
  type: ClusterIP
  headless:
    enabled: true
arguments:
  - "sed \"/$HOSTNAME/d\" /usr/local/tomcat/logs/server.xml >  /usr/local/tomcat/conf/server.xml && catalina.sh run"
resources:
  limits:
    cpu: 0.5
    memory: 3.5Gi
  requests:
    cpu: 0.5
    memory: 3.5Gi
image:
  repository: nexus-docker.unicommerce.infra:8123/uniware
  pullPolicy: IfNotPresent
  tag: 1709.9-k8s
imagePullSecrets:
  - name: nexus-secrets
init:
  arguments:
    - mkdir -p /tmp/RotatedAppLogs
    - /tmp/files/customizedTemplates
    - /tmp/files/reconciliationInvoices
    - /tmp/files/saleOrderReturn
    - /tmp/files/import/logs
    - /tmp/files/saleOrder
    - /tmp/files/shippingLabel
nodeSelector:
  karpenter.sh/nodepool: dedicated-ondemand-pool
tolerations:
  - key: "dedicated/ondemand"
    operator: "Exists"
    effect: "NoSchedule"
livenessProbe: {}
readinessProbe:
  exec:
    command:
    - /bin/bash
    - -c
    - |-
      health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/live")
      if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  failureThreshold: 3
  periodSeconds: 30
  successThreshold: 1
  timeoutSeconds: 10
  initialDelaySeconds: 180
env:
  JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms512M -Xmx2650M 
    -XX:MaxPermSize=256M -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -Duser.timezone=Asia/Kolkata 
    -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/donoshopmp#prefetchActiveMQ2BrokerPassword> 
    -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DappIdentifier=${HOSTNAME} 
    -DserverName=Donoshopmp -Dhazelcast.local.publicAddress=${HOSTNAME}.prod.svc.cluster.local:5701 
    -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml 
    -DhazelcastMembers=donoshopmp-uniware-api-0.prod.svc.cluster.local:5701,donoshopmp-uniware-task-0.donoshopmp-uniware-task.prod.svc.cluster.local:5701 
    -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper1.e2-in.unicommerce.infra:2181 
    -DtenantSpecificMongoHosts=mongo1.e1-in.unicommerce.infra:27017,mongo2.e1-in.unicommerce.infra:27017 
    -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 
    -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq 
    -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/donoshopmp#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 
    -DclusterName=Donoshopmp -DactiveMQBrokerUrl=failover:tcp://activemq.e1-in.unicommerce.infra:61616,tcp://activemq.e1-in.unicommerce.infra:61616 
    -DactiveMQBrokerUsername=unicommercemq -DserverNames=Donoshopmp -DactiveMQBrokerPassword=<path:kv/data/uniware/donoshopmp#activeMQBrokerPassword> -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr 
    -Dcom.sun.management.jmxremote -DAsyncLogger.ThreadNameStrategy=UNCACHED -Dcom.sun.management.jmxremote.port=6969 
    -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -DactiveMQMaxConnections=5 
    -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 -DactiveMQ2BrokerUrl=failover:tcp://activemq.e2-in.unicommerce.infra:61616,tcp://activemq.e2-in.unicommerce.infra:61616 
    -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/donoshopmp#activeMQBrokerPassword> -DactiveMQ2MaxConnections=5 
    -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= 
    -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 
    -DexecuteJobs=false
    -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump 
    -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource 
    -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017"
volumeMounts:
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
    subPath: vault.properties
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/vaultConfig
    name: uniware-vault-config
  - mountPath: /usr/local/tomcat/conf/context.xml
    subPath: context.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/server.xml
    subPath: server.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/catalina.sh
    subPath: catalina.sh
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/hazelcast.yml
    subPath: hazelcast.yml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
    subPath: gc_monitor.sh
    name: uniware-config
volumes:
- name: uniware-config
  configMap:
    name: donoshopmp-uniware
    defaultMode: 0777
- name: uniware-vault-config
  configMap:
    name: donoshopmp-vault-uniware
    defaultMode: 0777
ingress:
  enabled: false
  className: "haproxy-public"
  annotations:
    haproxy-ingress.github.io/config-backend: |
      http-request set-header X-Forwarded-Proto https
  hosts:
    - host: allgoodscentspop.unicommerce.com
    - host: allgoodscents.unicommerce.com
    - host: allthingschocolate.unicommerce.com
    - host: assembly.unicommerce.com
    - host: basedono.unicommerce.com
    - host: beelicioushoney.unicommerce.com
    - host: beyondarie.unicommerce.com
    - host: biocule.unicommerce.com
    - host: blabel.unicommerce.com
    - host: blackandgreen.unicommerce.com
    - host: blindgilt.unicommerce.com
    - host: bornequal.unicommerce.com
    - host: borngood.unicommerce.com
    - host: bowtoes.unicommerce.com
    - host: brewhouseteabrewingco.unicommerce.com
    - host: countryclay.unicommerce.com
    - host: cupidcotton.unicommerce.com
    - host: curryit.unicommerce.com
    - host: detoxie.unicommerce.com
    - host: doftdecorllp.unicommerce.com
    - host: donoartest1.unicommerce.com
    - host: donoartest2.unicommerce.com
    - host: donoartest3.unicommerce.com
    - host: donoshopmptestar1.unicommerce.com
    - host: donoshoptest22.unicommerce.com
    - host: donotesttenant1.unicommerce.com
    - host: donotesttenant2.unicommerce.com
    - host: doodlepop.unicommerce.com
    - host: drocto.unicommerce.com
    - host: eatwithbetter.unicommerce.com
    - host: ecoright.unicommerce.com
    - host: environmanly.unicommerce.com
    - host: epiphanysnacks.unicommerce.com
    - host: eright.unicommerce.com
    - host: everpret.unicommerce.com
    - host: exhalelabel.unicommerce.com
    - host: fancypastels.unicommerce.com
    - host: flavure.unicommerce.com
    - host: foxtale.unicommerce.com
    - host: globalbeautysecrets.unicommerce.com
    - host: gonuts.unicommerce.com
    - host: goodgraze.unicommerce.com
    - host: happypuppyorganics.unicommerce.com
    - host: hookdfood.unicommerce.com
    - host: ikram.unicommerce.com
    - host: ilana.unicommerce.com
    - host: imbuenatural.unicommerce.com
    - host: ishkafarms.unicommerce.com
    - host: jodilife.unicommerce.com
    - host: juicychemistry.unicommerce.com
    - host: karconsciousliving.unicommerce.com
    - host: kayos.unicommerce.com
    - host: kelbyhuston.unicommerce.com
    - host: kesarco.unicommerce.com
    - host: lealcosmetics.unicommerce.com
    - host: lilgoodness.unicommerce.com
    - host: lovekaapi.unicommerce.com
    - host: magicpaw.unicommerce.com
    - host: maisknn.unicommerce.com
    - host: mantheory.unicommerce.com
    - host: mecraaz.unicommerce.com
    - host: miaj.unicommerce.com
    - host: mikololo.unicommerce.com
    - host: misa.unicommerce.com
    - host: momstherapy.unicommerce.com
    - host: morazecosmetics.unicommerce.com
    - host: motherlandsuperstore.unicommerce.com
    - host: mushroommistri.unicommerce.com
    - host: naagin.unicommerce.com
    - host: naariofoods.unicommerce.com
    - host: nandancoffee.unicommerce.com
    - host: naturalvibes.unicommerce.com
    - host: navvayd.unicommerce.com
    - host: niblerzz.unicommerce.com
    - host: nomadfoodproject.unicommerce.com
    - host: novanova.unicommerce.com
    - host: nysahomeaccents.unicommerce.com
    - host: organicroots.unicommerce.com
    - host: pahadilocal.unicommerce.com
    - host: pallaviswadi.unicommerce.com
    - host: perca.unicommerce.com
    - host: petsnugs.unicommerce.com
    - host: phabpop.unicommerce.com
    - host: pips.unicommerce.com
    - host: postbox.unicommerce.com
    - host: primefoods.unicommerce.com
    - host: ragecoffee.unicommerce.com
    - host: sagajaipur.unicommerce.com
    - host: saphed.unicommerce.com
    - host: sassiest.unicommerce.com
    - host: satat.unicommerce.com
    - host: secretalchemist.unicommerce.com
    - host: shararatpop.unicommerce.com
    - host: skyntillate.unicommerce.com
    - host: smoor.unicommerce.com
    - host: snackible.unicommerce.com
    - host: sneads.unicommerce.com
    - host: stgpopclub.unicommerce.com
    - host: supermenaturals.unicommerce.com
    - host: tabakashibyartikashah.unicommerce.com
    - host: tarunatural.unicommerce.com
    - host: teafit.unicommerce.com
    - host: teatrunk.unicommerce.com
    - host: testdono1122.unicommerce.com
    - host: testdonoshp.unicommerce.com
    - host: testmpdono.unicommerce.com
    - host: thebasics.unicommerce.com
    - host: thecobeing.unicommerce.com
    - host: thelagaadi.unicommerce.com
    - host: themintenfold.unicommerce.com
    - host: thesummerhouse.unicommerce.com
    - host: tokike.unicommerce.com
    - host: tstdonoshp2.unicommerce.com
    - host: vedicvalley.unicommerce.com
    - host: vsmaniandco.unicommerce.com
    - host: wabslife.unicommerce.com
    - host: whatsupwellness.unicommerce.com
    - host: wiggles.unicommerce.com
    - host: zennials.unicommerce.com
  tls: []

istio:
  enabled: true

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 75

uniware-task:
  enabled: true
  podSecurityContext:
    fsGroup: 1000
  resources:
    limits:
      cpu: 1.2
      memory: 4Gi
    requests:
      cpu: 0.7
      memory: 3Gi
  service:
    hazelCastport: 5701
    type: ClusterIP
    headless:
      enabled: true
  spec:
    consolidation:
      enabled: true
  image:
    repository: nexus-docker.unicommerce.infra:8123/uniware
    pullPolicy: Always
    tag: 1709.9-k8s
  nodeSelector:
    karpenter.sh/nodepool: dedicated-spot-pool
  imagePullSecrets:
    - name: nexus-secrets
  tolerations:
    - key: "dedicated/spot"
      operator: "Exists"
      effect: "NoSchedule"
  init:
    arguments:
      - mkdir -p /tmp/RotatedAppLogs
      - /tmp/files/customizedTemplates
      - /tmp/files/reconciliationInvoices
      - /tmp/files/saleOrderReturn
      - /tmp/files/import/logs
      - /tmp/files/saleOrder
      - /tmp/files/shippingLabel
  livenessProbe: {}
  readinessProbe: {}
  env:
    JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms512M -Xmx3500M -XX:MaxPermSize=256M 
      -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -Duser.timezone=Asia/Kolkata 
      -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin 
      -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/donoshopmp#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 
      -DappIdentifier=app2 -DserverName=Donoshopmp -Dhazelcast.local.publicAddress=donoshopmp-uniware-task-0.donoshopmp-uniware-task.prod.svc.cluster.local:5701  
      -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DhazelcastMembers=donoshopmp-uniware-api-0.prod.svc.cluster.local:5701,donoshopmp-uniware-task-0.donoshopmp-uniware-task.prod.svc.cluster.local:5701  
      -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper1.e2-in.unicommerce.infra:2181 
      -DtenantSpecificMongoHosts=mongo1.e1-in.unicommerce.infra:27017,mongo2.e1-in.unicommerce.infra:27017 
      -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 
      -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq 
      -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/donoshopmp#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 
      -DclusterName=Donoshopmp -DactiveMQBrokerUrl=failover:tcp://activemq.e1-in.unicommerce.infra:61616,tcp://activemq.e1-in.unicommerce.infra:61616 
      -DactiveMQBrokerUsername=unicommercemq -DserverNames=Donoshopmp -DactiveMQBrokerPassword=<path:kv/data/uniware/donoshopmp#activeMQBrokerPassword> -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr 
      -Dcom.sun.management.jmxremote -DAsyncLogger.ThreadNameStrategy=UNCACHED -Dcom.sun.management.jmxremote.port=6969 
      -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -DactiveMQMaxConnections=5 
      -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 -DactiveMQ2BrokerUrl=failover:tcp://activemq.e2-in.unicommerce.infra:61616,tcp://activemq.e2-in.unicommerce.infra:61616 
      -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/donoshopmp#activeMQBrokerPassword> -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 
      -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword 
      -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017  
      -DexecuteJobs=true -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017
      -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails 
      -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource 
      -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017"
  volumeMounts:
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
    subPath: vault.properties
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/vaultConfig
    name: uniware-vault-config
  - mountPath: /usr/local/tomcat/conf/context.xml
    subPath: context.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/conf/server.xml
    subPath: server.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/catalina.sh
    subPath: catalina.sh
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/hazelcast.yml
    subPath: hazelcast.yml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
    subPath: gc_monitor.sh
    name: uniware-config
  volumes:
  - name: uniware-config
    configMap:
      name: donoshopmp-uniware
      defaultMode: 0777
  - name: uniware-vault-config
    configMap:
      name: donoshopmp-vault-uniware
      defaultMode: 0777
global:
  filebeat:
    config:
      name: ${PODNAME}.donoshopmp-in.unicommerce.infra
      hosts: 
        - "logstash1-in.unicommerce.infra:5044"
        - "logstash2-in.unicommerce.infra:5044"
  promtail:
    config:
      clients:
        - url: http://loki-gateway.monitoring.svc.cluster.local:80/loki/api/v1/push
          tenant_id: 'donoshopmp'
