enabled: false
replicaCount: 0
podSecurityContext:
  fsGroup: 1000
pdb:
  enabled: true
image:
  repository: nexus-docker.unicommerce.infra:8123/uniware
  pullPolicy: IfNotPresent
  tag: stg_4155-k8s
imagePullSecrets:
  - name: nexus-secrets
service:
  hazelCastport: 5701
  type: ClusterIP
  headless:
    enabled: true
arguments:
  - "sed \"/$HOSTNAME/d\" /usr/local/tomcat/logs/server.xml >  /usr/local/tomcat/conf/server.xml && catalina.sh run"
resources:
  limits:
    cpu: 1.4
    memory: 6.4Gi
  requests:
    cpu: 1.4
    memory: 6.4Gi
nodeSelector:
  karpenter.sh/nodepool: dedicated-ondemand-pool
tolerations:
  - key: "dedicated/ondemand"
    operator: "Exists"
    effect: "NoSchedule"
init:
  arguments:
    - mkdir -p /tmp/RotatedAppLogs
    - /tmp/files/customizedTemplates
    - /tmp/files/reconciliationInvoices
    - /tmp/files/saleOrderReturn
    - /tmp/files/import/logs
    - /tmp/files/saleOrder
    - /tmp/files/shippingLabel
livenessProbe: {}
readinessProbe:
  exec:
    command:
      - /bin/bash
      - -c
      - |-
        health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/live")
        if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  failureThreshold: 3
  periodSeconds: 60
  successThreshold: 1
  timeoutSeconds: 30
  initialDelaySeconds: 180
env:
  JAVA_OPTS: "-Djava.security.egd=file:/dev/./urandom -javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms512M -Xmx4500M -XX:MetaspaceSize=512M -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -Dlog4j2.enableJndiJms=true -Duser.timezone=Asia/Kolkata -DserverName=UniStage -Dhazelcast.local.publicAddress=${HOSTNAME}.staging.svc.cluster.local -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DhazelcastMembers=unistage-uniware-task-0.unistage-uniware-task.staging.svc.cluster.local,unistage-uniware-api-0.staging.svc.cluster.local,unistage-uniware-api-1.staging.svc.cluster.local -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper1.e2-in.unicommerce.infra:2181 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DtenantSpecificMongoHosts=mongo1.e1-in.unicommerce.infra:27017,mongo2.e1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/unistage#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DclusterName=UniStage -DactiveMQBrokerUrl=failover:tcp://activemq.c1-in.unicommerce.infra:61616,tcp://activemq.c1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=UniStage -DactiveMQBrokerPassword=<path:kv/data/uniware/unistage#activeMQBrokerPassword> -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -Dcom.sun.management.jmxremote -DAsyncLogger.ThreadNameStrategy=UNCACHED -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -DactiveMQMaxConnections=5 -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 -DactiveMQ2BrokerUrl=failover:tcp://activemq.c2-in.unicommerce.infra:61616,tcp://activemq.c2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/unistage#activeMQBrokerPassword> -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DmongoMaxWaitTime=5000 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword  -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DexecuteJobs=false -Djdk.tls.client.protocols=TLSv1.2 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:+PrintGCDetails -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/unistage#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017"
volumeMounts:
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
    subPath: vault.properties
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/vaultConfig
    name: uniware-vault-config
  - mountPath: /usr/local/tomcat/conf/context.xml
    subPath: context.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/server.xml
    subPath: server.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/catalina.sh
    subPath: catalina.sh
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/hazelcast.yml
    subPath: hazelcast.yml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
    subPath: gc_monitor.sh
    name: uniware-config
volumes:
  - name: uniware-config
    configMap:
      name: unistage-uniware
      defaultMode: 0777
  - name: uniware-vault-config
    configMap:
      name: unistage-vault-uniware
      defaultMode: 0777
ingress:
  enabled: false
  className: "haproxy-public"
  annotations:
    haproxy-ingress.github.io/session-cookie-name: unicommerce
    haproxy-ingress.github.io/session-cookie-strategy: insert
    haproxy-ingress.github.io/session-cookie-keywords: indirect nocache httponly
    haproxy-ingress.github.io/session-cookie-preserve: false
    haproxy-ingress.github.io/session-cookie-samesite: false
    haproxy-ingress.github.io/session-cookie-dynamic: false
    haproxy-ingress.github.io/session-cookie-shared: false
    ingress.kubernetes.io/affinity: cookie
    haproxy-ingress.github.io/config-backend: |
      http-request set-header X-Forwarded-Proto https
  hosts:
    - host: unistage.unicommerce.com
    - host: mayanktest-unireco.unicommerce.com
    - host: unistage-unireco.unicommerce.com
  tls: []

istio:
  enabled: false
  web:
    enabled: false

web:
  enabled: false
  pdb:
    enabled: true
    minAvailable: 1
  resources:
    limits:
      cpu: 1.4
      memory: 6.4Gi
    requests:
      cpu: 1.4
      memory: 6.4Gi
  env:
    JAVA_OPTS: "-Djava.security.egd=file:/dev/./urandom -javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar 
      -Xms512M -Xmx4500M -XX:MaxPermSize=256M -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 
      -Dlog4j2.enableJndiJms=true -Duser.timezone=Asia/Kolkata -DserverName=UniStage -Dhazelcast.local.publicAddress=${HOSTNAME}.staging.svc.cluster.local 
      -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml 
      -DhazelcastMembers=unistage-uniware-task-0.unistage-uniware-task.staging.svc.cluster.local,unistage-uniware-api-0.staging.svc.cluster.local,unistage-uniware-web-0.staging.svc.cluster.local,unistage-uniware-api-1.staging.svc.cluster.local 
      -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper1.e2-in.unicommerce.infra:2181 
      -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DtenantSpecificMongoHosts=mongo1.e1-in.unicommerce.infra:27017,mongo2.e1-in.unicommerce.infra:27017 
      -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 
      -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq 
      -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/unistage#activeMQBrokerPassword> 
      -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DclusterName=UniStage 
      -DactiveMQBrokerUrl=failover:tcp://activemq.c1-in.unicommerce.infra:61616,tcp://activemq.c1-in.unicommerce.infra:61616 
      -DactiveMQBrokerUsername=unicommercemq -DserverNames=UniStage -DactiveMQBrokerPassword=<path:kv/data/uniware/unistage#activeMQBrokerPassword> 
      -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -Dcom.sun.management.jmxremote -DAsyncLogger.ThreadNameStrategy=UNCACHED 
      -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false 
      -Dcom.sun.management.jmxremote.authenticate=false -DactiveMQMaxConnections=5 -DactiveMQQueuePrefetch=1 
      -DquartzThreadPoolSize=10 -DactiveMQ2BrokerUrl=failover:tcp://activemq.c2-in.unicommerce.infra:61616,tcp://activemq.c2-in.unicommerce.infra:61616 
      -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/unistage#activeMQBrokerPassword> 
      -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DmongoMaxWaitTime=5000 
      -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= 
      -DinvoiceMQBrokerPassword  -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DexecuteJobs=false 
      -Djdk.tls.client.protocols=TLSv1.2 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) 
      -XX:+PrintGCDetails -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDateStamps -XX:-LoopUnswitching 
      -DdataSource=UniwareHikariDataSource -DprefetchActiveMQ2MaxConnections=5 
      -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin 
      -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/unistage#prefetchActiveMQ2BrokerPassword> 
      -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 
      -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017"
  autoscaling:
    enabled: false
    minReplicas: 1
    maxReplicas: 2
    targetCPUUtilizationPercentage: 80

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 2
  targetCPUUtilizationPercentage: 75

uniware-task:
  enabled: false
  podSecurityContext:
    fsGroup: 1000
  resources:
    limits:
      cpu: 1.4
      memory: 6.4Gi
    requests:
      cpu: 1.4
      memory: 6.4Gi
  nodeSelector:
    karpenter.sh/nodepool: dedicated-spot-pool
  tolerations:
    - key: "dedicated/spot"
      operator: "Exists"
      effect: "NoSchedule"
  image:
    repository: nexus-docker.unicommerce.infra:8123/uniware
    pullPolicy: IfNotPresent
    tag: stg_4155-k8s
  imagePullSecrets:
    - name: nexus-secrets
  init:
    arguments:
      - mkdir -p /tmp/RotatedAppLogs
      - /tmp/files/customizedTemplates
      - /tmp/files/reconciliationInvoices
      - /tmp/files/saleOrderReturn
      - /tmp/files/import/logs
      - /tmp/files/saleOrder
      - /tmp/files/shippingLabel
  livenessProbe:  {}
  readinessProbe:
    exec:
      command:
        - /bin/bash
        - -c
        - |-
          health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/live")
          if [[ ${health_status} -ne 200 ]]; then exit 1; fi
    failureThreshold: 3
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 30
    initialDelaySeconds: 180
  env:
    JAVA_OPTS: "-Djava.security.egd=file:/dev/./urandom -javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms512M -Xmx4500M -XX:MetaspaceSize=512M -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -Dlog4j2.enableJndiJms=true -Duser.timezone=Asia/Kolkata -DserverName=UniStage -Dhazelcast.local.publicAddress=${HOSTNAME}.unistage-uniware-task.staging.svc.cluster.local -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DhazelcastMembers=unistage-uniware-task-0.unistage-uniware-task.staging.svc.cluster.local,unistage-uniware-api-0.staging.svc.cluster.local,unistage-uniware-api-1.staging.svc.cluster.local -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper1.e2-in.unicommerce.infra:2181 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DtenantSpecificMongoHosts=mongo1.e1-in.unicommerce.infra:27017,mongo2.e1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/unistage#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DclusterName=UniStage -DactiveMQBrokerUrl=failover:tcp://activemq.c1-in.unicommerce.infra:61616,tcp://activemq.c1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=UniStage -DactiveMQBrokerPassword=<path:kv/data/uniware/unistage#activeMQBrokerPassword> -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -Dcom.sun.management.jmxremote -DAsyncLogger.ThreadNameStrategy=UNCACHED -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -DactiveMQMaxConnections=5 -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 -DactiveMQ2BrokerUrl=failover:tcp://activemq.c2-in.unicommerce.infra:61616,tcp://activemq.c2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/unistage#activeMQBrokerPassword> -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DmongoMaxWaitTime=5000 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword  -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DexecuteJobs=true -Djdk.tls.client.protocols=TLSv1.2 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:+PrintGCDetails -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/unistage#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017"
  volumeMounts:
    - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
      subPath: vault.properties
      name: uniware-config
    - mountPath: /usr/local/tomcat/logs/vaultConfig
      name: uniware-vault-config
    - mountPath: /usr/local/tomcat/conf/context.xml
      subPath: context.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/conf/server.xml
      subPath: server.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/catalina.sh
      subPath: catalina.sh
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/hazelcast.yml
      subPath: hazelcast.yml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
      subPath: gc_monitor.sh
      name: uniware-config
  volumes:
    - name: uniware-config
      configMap:
        name: unistage-uniware
        defaultMode: 0777
    - name: uniware-vault-config
      configMap:
        name: unistage-vault-uniware
        defaultMode: 0777
  service:
    hazelCastport: 5701
    type: ClusterIP
    headless:
      enabled: true
global:
  filebeat:
    config:
      name: ${PODNAME}.unistage-in.unicommerce.infra
      hosts:
        - "logstash1-in.unicommerce.infra:5044"
        - "logstash2-in.unicommerce.infra:5044"
  promtail:
    config:
      clients:
        - url: http://loki-gateway.monitoring.svc.cluster.local:80/loki/api/v1/push
          tenant_id: 'unistage'
  efs:
    enabled: false
