enabled: true
replicaCount: 0
podSecurityContext:
  fsGroup: 1000
pdb:
  enabled: true
service:
  hazelCastport: 5701
  type: NodePort
  headless:
    enabled: false
image:
  repository: nexus-docker.unicommerce.infra:8123/uniware
  pullPolicy: IfNotPresent
  tag: 1709.9-k8s
imagePullSecrets:
  - name: nexus-secrets
init:
  arguments:
    - mkdir -p /tmp/RotatedAppLogs
    - /tmp/files/customizedTemplates
    - /tmp/files/reconciliationInvoices
    - /tmp/files/saleOrderReturn
    - /tmp/files/import/logs
    - /tmp/files/saleOrder
    - /tmp/files/shippingLabel
livenessProbe:
  exec:
    command:
      - /bin/bash
      - -c
      - |-
        health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/health")
        if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  failureThreshold: 3
  periodSeconds: 30
  successThreshold: 1
  timeoutSeconds: 10
  initialDelaySeconds: 180
readinessProbe:
  exec:
    command:
      - /bin/bash
      - -c
      - |-
        health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/health")
        if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  failureThreshold: 3
  periodSeconds: 30
  successThreshold: 1
  timeoutSeconds: 10
  initialDelaySeconds: 180
env:
  JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms512M -Xmx5298M -XX:MaxPermSize=256M -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/stgril#activeMQBrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DappIdentifier=app2 -DserverName=StgRIL -Dhazelcast.local.publicAddress=app2.stgril-in.unicommerce.infra:5701 -DhazelcastMembers=app1.stgril-in.unicommerce.infra:5701,app2.stgril-in.unicommerce.infra:5701,app3.stgril-in.unicommerce.infra:5701 -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.e1-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo1.e1-in.unicommerce.infra:27017,mongo2.e1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/stgril#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DclusterName=StgRIL -DactiveMQBrokerUrl=failover:tcp://activemq1.e1-in.unicommerce.infra:61616,tcp://activemq1.e1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=StgRIL -DactiveMQBrokerPassword=<path:kv/data/uniware/stgril#activeMQBrokerPassword> -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -Dcom.sun.management.jmxremote -DAsyncLogger.ThreadNameStrategy=UNCACHED -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -DactiveMQMaxConnections=5 -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 -DactiveMQ2BrokerUrl=failover:tcp://activemq1.e1-in.unicommerce.infra:61616,tcp://activemq1.e1-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/stgril#activeMQBrokerPassword> -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DexecuteJobs=false -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:+PrintGCDetails -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/usr/local/apache-tomcat/logs/heapdump.log -XX:OnOutOfMemoryError=/usr/local/apache-tomcat/bin/send_alert_email.sh  -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017"
volumeMounts:
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
    subPath: vault.properties
    name: uniware-config
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vaultCredentials.yml
    subPath: vaultCredentials.yml
    name: uniware-config
  - mountPath: /usr/local/tomcat/conf/context.xml
    subPath: context.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/conf/server.xml
    subPath: server.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/catalina.sh
    subPath: catalina.sh
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/hazelcast.yml
    subPath: hazelcast.yml
    name: uniware-config
volumes:
  - name: uniware-config
    configMap:
      name: stgril-uniware
      defaultMode: 0777
ingress:
  enabled: false
  className: "haproxy-public"
  hosts:
    - host: stgril.unicommerce.com
  tls: []

istio:
  enabled: false

autoscaling:
  enabled: false
uniware-task:
  enabled: true
  podSecurityContext:
    fsGroup: 1000
  resources:
    limits:
      cpu: 1.5
      memory: 3.5Gi
    requests:
      cpu: 1
      memory: 2.5Gi
  service:
    hazelCastport: 5701
    type: NodePort
    headless:
      enabled: false
  spec:
    consolidation:
      enabled: true
  image:
    repository: nexus-docker.unicommerce.infra:8123/uniware
    pullPolicy: Always
    tag: stg_4126-k8s
  nodeSelector:
    karpenter.sh/nodepool: dedicated-spot-pool
  imagePullSecrets:
    - name: nexus-secrets
  tolerations:
    - key: "dedicated/spot"
      operator: "Exists"
      effect: "NoSchedule"
  init:
    arguments:
      - mkdir -p /tmp/RotatedAppLogs
      - /tmp/files/customizedTemplates
      - /tmp/files/reconciliationInvoices
      - /tmp/files/saleOrderReturn
      - /tmp/files/import/logs
      - /tmp/files/saleOrder
      - /tmp/files/shippingLabel
  livenessProbe: {}
  readinessProbe: {}
  env:
    JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms512M -Xmx2700M -XX:MaxPermSize=256M -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/stgril#activeMQBrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DappIdentifier=app2 -DserverName=StgRIL -Dhazelcast.local.publicAddress=app2.stgril-in.unicommerce.infra:31268 -DhazelcastMembers=app1.stgril-in.unicommerce.infra:5701,app2.stgril-in.unicommerce.infra:31268,app3.stgril-in.unicommerce.infra:5701 -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.e1-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo1.e1-in.unicommerce.infra:27017,mongo2.e1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/stgril#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DclusterName=StgRIL -DactiveMQBrokerUrl=failover:tcp://activemq1.e1-in.unicommerce.infra:61616,tcp://activemq1.e1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=StgRIL -DactiveMQBrokerPassword=<path:kv/data/uniware/stgril#activeMQBrokerPassword> -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -Dcom.sun.management.jmxremote -DAsyncLogger.ThreadNameStrategy=UNCACHED -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -DactiveMQMaxConnections=5 -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 -DactiveMQ2BrokerUrl=failover:tcp://activemq1.e1-in.unicommerce.infra:61616,tcp://activemq1.e1-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/stgril#activeMQBrokerPassword> -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DexecuteJobs=true -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:+PrintGCDetails -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/usr/local/apache-tomcat/logs/heapdump.log -XX:OnOutOfMemoryError=/usr/local/apache-tomcat/bin/send_alert_email.sh  -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017"
  volumeMounts:
    - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
      subPath: vault.properties
      name: uniware-config
    - mountPath: /usr/local/tomcat/conf/uniwareConfig/vaultCredentials.yml
      subPath: vaultCredentials.yml
      name: uniware-config
    - mountPath: /usr/local/tomcat/conf/context.xml
      subPath: context.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/conf/server.xml
      subPath: server.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/catalina.sh
      subPath: catalina.sh
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/hazelcast.yml
      subPath: hazelcast.yml
      name: uniware-config
  volumes:
    - name: uniware-config
      configMap:
        name: stgril-uniware
        defaultMode: 0777
global:
  filebeat:
    config:
      name: ${PODNAME}.stgril-in.unicommerce.infra
      hosts:
        - "logstash1-in.unicommerce.infra:5044"
        - "logstash2-in.unicommerce.infra:5044"
  promtail:
    config:
      clients:
        - url: http://loki-gateway.monitoring.svc.cluster.local:80/loki/api/v1/push
          tenant_id: 'stgril'
