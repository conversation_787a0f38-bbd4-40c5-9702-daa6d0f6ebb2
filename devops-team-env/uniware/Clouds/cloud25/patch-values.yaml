enabled: true
replicaCount: 0
podSecurityContext:
  fsGroup: 1000
pdb:
  enabled: true
  minAvailable: 2
image:
  repository: nexus-docker.unicommerce.infra:8123/uniware
  pullPolicy: Always
  tag: 1714.2-k8s
imagePullSecrets:
  - name: nexus-secrets
service:
  type: ClusterIP
  hazelCastport: 5701
  headless:
    enabled: true
arguments:
  - "sed \"/$HOSTNAME/d\" /usr/local/tomcat/logs/server.xml >  /usr/local/tomcat/conf/server.xml && catalina.sh run"
resources:
  limits:
    cpu: 1.5
    memory: 12Gi
  requests:
    cpu: 1.5
    memory: 12Gi
nodeSelector:
  karpenter.sh/nodepool: clouds-ondemand-pool
affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app.kubernetes.io/instance: cloud25
            app.kubernetes.io/name: uniware-api
        topologyKey: kubernetes.io/hostname
tolerations:
  - key: "clouds/ondemand"
    operator: "Exists"
    effect: "NoSchedule"
init:
  arguments:
    - mkdir -p /tmp/RotatedAppLogs
    - /tmp/files/customizedTemplates
    - /tmp/files/reconciliationInvoices
    - /tmp/files/saleOrderReturn
    - /tmp/files/import/logs
    - /tmp/files/saleOrder
    - /tmp/files/shippingLabel
livenessProbe: {}
readinessProbe: {}
env:
  JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms512M -Xmx9000M -XX:MaxPermSize=512M -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -DtenantSpecificMongoConnectionsMaxSize=100 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/cloud25#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DactiveMQMaxConnections=5 -DserverName=Cloud25 -DactiveMQQueuePrefetch=1 -Ds3bucketSuffix=-in -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DAsyncLoggerConfig.ExceptionHandler=com.lmax.disruptor.IgnoreExceptionHandler -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/cloud25#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.c1-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo1.c3-in.unicommerce.infra:27017,mongo2.c3-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DactiveMQBrokerUrl=failover:tcp://activemq.c1-in.unicommerce.infra:61616,tcp://activemq.c1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=Cloud25 -DactiveMQBrokerPassword=<path:kv/data/uniware/cloud25#activeMQBrokerPassword> -DquartzThreadPoolSize=500 -DexecuteJobs=false -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr  -DappIdentifier=${HOSTNAME} -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DactiveMQ2BrokerUrl=failover:tcp://activemq.c2-in.unicommerce.infra:61616,tcp://activemq.c2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/cloud25#activeMQBrokerPassword> -DclusterName=Cloud25 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource -XX:+UseG1GC -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -XX:+UseStringDeduplication -XX:StringDeduplicationAgeThreshold=15 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Dhazelcast.local.publicAddress=${HOSTNAME}.prod.svc.cluster.local:5701 -DhazelcastMembers=cloud25-uniware-api-0.prod.svc.cluster.local:5701,cloud25-uniware-api-1.prod.svc.cluster.local:5701,cloud25-uniware-task-0.cloud25-uniware-task.prod.svc.cluster.local:5701"
volumeMounts:
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
    subPath: vault.properties
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/vaultConfig
    name: uniware-vault-config
  - mountPath: /usr/local/tomcat/conf/context.xml
    subPath: context.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/server.xml
    subPath: server.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/catalina.sh
    subPath: catalina.sh
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/hazelcast.yml
    subPath: hazelcast.yml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
    subPath: gc_monitor.sh
    name: uniware-config
volumes:
  - name: uniware-config
    configMap:
      name: cloud25-uniware
      defaultMode: 0777
  - name: uniware-vault-config
    configMap:
      name: cloud25-vault-uniware
      defaultMode: 0777
ingress:
  enabled: false
  className: "haproxy-public"
  annotations:
    haproxy-ingress.github.io/config-backend: |
      http-request set-header X-Forwarded-Proto https
  hosts:
    - host: 3mads.unicommerce.com
    - host: 7herbmaya.unicommerce.com
    - host: aanyor.unicommerce.com
    - host: aarogya360.unicommerce.com
    - host: aceinternational.unicommerce.com
    - host: alayacotton1.unicommerce.com
    - host: alfadotenterprises.unicommerce.com
    - host: aqcorporation.unicommerce.com
    - host: areionvet.unicommerce.com
    - host: artklim.unicommerce.com
    - host: atrimedpharmaceuticalspvtltd.unicommerce.com
    - host: baseprofessional25.unicommerce.com
    - host: bellofox.unicommerce.com
    - host: bespokebeautyprivatelimited.unicommerce.com
    - host: bhandareeconsumerproductsprivatelimited.unicommerce.com
    - host: bhandareeconsumerproductsprivatelimite.unicommerce.com
    - host: blackberrys.unicommerce.com
    - host: bmactivegear.unicommerce.com
    - host: britishclub.unicommerce.com
    - host: bshtexandcarellp.unicommerce.com
    - host: buyerwell1.unicommerce.com
    - host: ceresfoodspvtltd.unicommerce.com
    - host: chevetol.unicommerce.com
    - host: clarksff.unicommerce.com
    - host: clockwork.unicommerce.com
    - host: cloglondon.unicommerce.com
    - host: coalcleanbeauty.unicommerce.com
    - host: contourcatwalkprivatelimited.unicommerce.com
    - host: cresale.unicommerce.com
    - host: cslifestyle.unicommerce.com
    - host: dancebible.unicommerce.com
    - host: decolete.unicommerce.com
    - host: divaexport.unicommerce.com
    - host: divyamshoe.unicommerce.com
    - host: eatab.unicommerce.com
    - host: exoticacreations.unicommerce.com
    - host: fabricsworld.unicommerce.com
    - host: fashbrostore.unicommerce.com
    - host: florencare.unicommerce.com
    - host: forestfuse.unicommerce.com
    - host: freewayclothingcompany.unicommerce.com
    - host: frenchclub.unicommerce.com
    - host: friendzcompany.unicommerce.com
    - host: ghumakkad.unicommerce.com
    - host: gkgcanvaspvtltd.unicommerce.com
    - host: glancedistributor.unicommerce.com
    - host: govindamhandicrafts.unicommerce.com
    - host: goyawellness.unicommerce.com
    - host: gravitaztechknit.unicommerce.com
    - host: greenhale.unicommerce.com
    - host: griffel.unicommerce.com
    - host: hayyan.unicommerce.com
    - host: healthyforever.unicommerce.com
    - host: hustlign.unicommerce.com
    - host: ikaroa.unicommerce.com
    - host: jayantspecialities.unicommerce.com
    - host: jcsexports.unicommerce.com
    - host: jeweljunction.unicommerce.com
    - host: jorymunfashionllp.unicommerce.com
    - host: juneenterprises.unicommerce.com
    - host: katish.unicommerce.com
    - host: khwaabi.unicommerce.com
    - host: kiana.unicommerce.com
    - host: kiasha.unicommerce.com
    - host: kidsdew.unicommerce.com
    - host: kinderkids.unicommerce.com
    - host: ladaks.unicommerce.com
    - host: ladylyka.unicommerce.com
    - host: laelegancia.unicommerce.com
    - host: lattooland.unicommerce.com
    - host: laxmifab.unicommerce.com
    - host: littletagsluxury.unicommerce.com
    - host: livegreenclothingco.unicommerce.com
    - host: m3rseamlesspvtltd.unicommerce.com
    - host: mackly.unicommerce.com
    - host: magaritatech.unicommerce.com
    - host: mahalakshmitextiletower.unicommerce.com
    - host: marudhartextile.unicommerce.com
    - host: mcgillfoodspvtltd.unicommerce.com
    - host: medfashions.unicommerce.com
    - host: meenucreation.unicommerce.com
    - host: mine4nine.unicommerce.com
    - host: minglay.unicommerce.com
    - host: mnvp.unicommerce.com
    - host: moonair.unicommerce.com
    - host: neshanka.unicommerce.com
    - host: opulent.unicommerce.com
    - host: petprakalp.unicommerce.com
    - host: pinkvillejpr.unicommerce.com
    - host: pixeltowelpvtltd.unicommerce.com
    - host: play.unicommerce.com
    - host: prakrtechsolutions.unicommerce.com
    - host: pranyasarees.unicommerce.com
    - host: pritejenterprises93.unicommerce.com
    - host: radheflutes.unicommerce.com
    - host: raymondapparel.unicommerce.com
    - host: rcubedelhi.unicommerce.com
    - host: rdgcreation.unicommerce.com
    - host: rsfashions.unicommerce.com
    - host: rudrakshaapparels.unicommerce.com
    - host: rusticindifashion.unicommerce.com
    - host: saadaa.unicommerce.com
    - host: samuimpex.unicommerce.com
    - host: sanskar.unicommerce.com
    - host: sashawear.unicommerce.com
    - host: sbdesign.unicommerce.com
    - host: sbtpl.unicommerce.com
    - host: sheljaenterprises.unicommerce.com
    - host: shereen.unicommerce.com
    - host: shivakcoy.unicommerce.com
    - host: shubhamknitwears.unicommerce.com
    - host: slovacosmetics.unicommerce.com
    - host: smlifestyle.unicommerce.com
    - host: sonammarketing.unicommerce.com
    - host: ssefpl.unicommerce.com
    - host: stepupbrands.unicommerce.com
    - host: stravahealthcare.unicommerce.com
    - host: styfun.unicommerce.com
    - host: sudarshandesign.unicommerce.com
    - host: sumittraders.unicommerce.com
    - host: swayam1.unicommerce.com
    - host: sweetginger.unicommerce.com
    - host: tankhidesigns1pvtltd.unicommerce.com
    - host: tashifashion.unicommerce.com
    - host: tbgt.unicommerce.com
    - host: teenilicious.unicommerce.com
    - host: theflavorbag.unicommerce.com
    - host: theoptifylens.unicommerce.com
    - host: thesobtistudio.unicommerce.com
    - host: theupvanvalley.unicommerce.com
    - host: thinkaheadenterprises.unicommerce.com
    - host: tipsyfly.unicommerce.com
    - host: tizora.unicommerce.com
    - host: topwayenergyindia.unicommerce.com
    - host: trancehomelinen.unicommerce.com
    - host: varitraders.unicommerce.com
    - host: veenuenterprises.unicommerce.com
    - host: vixenwrap.unicommerce.com
    - host: vtradition.unicommerce.com
    - host: vventures.unicommerce.com
    - host: w9y.unicommerce.com
    - host: wholesomeessentials.unicommerce.com
    - host: woakers01.unicommerce.com
    - host: yayynaturalspvtltd.unicommerce.com
    - host: yel.unicommerce.com
    - host: zaintreadars.unicommerce.com
  tls: []
istio:
  enabled: true
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 2
  targetCPUUtilizationPercentage: 75
uniware-task:
  enabled: true
  podSecurityContext:
    fsGroup: 1000
  resources:
    limits:
      cpu: 2.5
      memory: 22.1Gi
    requests:
      cpu: 2.5
      memory: 22.1Gi
  nodeSelector:
    karpenter.sh/nodepool: dedicated-spot-pool
  tolerations:
    - key: "dedicated/spot"
      operator: "Exists"
      effect: "NoSchedule"
  image:
    repository: nexus-docker.unicommerce.infra:8123/uniware
    pullPolicy: Always
    tag: 1714.2-k8s
  imagePullSecrets:
    - name: nexus-secrets
  init:
    arguments:
      - mkdir -p /tmp/RotatedAppLogs
      - /tmp/files/customizedTemplates
      - /tmp/files/reconciliationInvoices
      - /tmp/files/saleOrderReturn
      - /tmp/files/import/logs
      - /tmp/files/saleOrder
      - /tmp/files/shippingLabel
  livenessProbe: {}
  readinessProbe: {}
  env:
    JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -XX:MaxPermSize=512M -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -DtenantSpecificMongoConnectionsMaxSize=100 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/cloud25#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DactiveMQMaxConnections=50 -DserverName=Cloud25 -Ds3bucketSuffix=-in -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/cloud25#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DactiveMQQueuePrefetch=1 -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DAsyncLoggerConfig.ExceptionHandler=com.lmax.disruptor.IgnoreExceptionHandler -DzookeeperUrl=zookeeper.c1-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo1.c3-in.unicommerce.infra:27017,mongo2.c3-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DactiveMQBrokerUrl=failover:tcp://activemq.c1-in.unicommerce.infra:61616,tcp://activemq.c1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=Cloud25 -DactiveMQBrokerPassword=<path:kv/data/uniware/cloud25#activeMQBrokerPassword> -DquartzThreadPoolSize=50  -DappIdentifier=app2 -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DactiveMQ2BrokerUrl=failover:tcp://activemq.c2-in.unicommerce.infra:61616,tcp://activemq.c2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/cloud25#activeMQBrokerPassword> -DclusterName=Cloud25 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -XX:+UseG1GC -XX:+UseStringDeduplication -XX:StringDeduplicationAgeThreshold=15 -DdataSource=UniwareHikariDataSource -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xms11050M -Xmx17600M -DexecuteJobs=true -Dhazelcast.local.publicAddress=${HOSTNAME}.cloud25-uniware-task.prod.svc.cluster.local:5701 -DhazelcastMembers=cloud25-uniware-api-0.prod.svc.cluster.local:5701,cloud25-uniware-api-1.prod.svc.cluster.local:5701,cloud25-uniware-task-0.cloud25-uniware-task.prod.svc.cluster.local:5701 -DrecoSpecificCommonMongoConnectionsMaxSize=50"
  volumeMounts:
    - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
      subPath: vault.properties
      name: uniware-config
    - mountPath: /usr/local/tomcat/logs/vaultConfig
      name: uniware-vault-config
    - mountPath: /usr/local/tomcat/conf/context.xml
      subPath: context.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/conf/server.xml
      subPath: server.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/catalina.sh
      subPath: catalina.sh
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/hazelcast.yml
      subPath: hazelcast.yml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
      subPath: gc_monitor.sh
      name: uniware-config
  volumes:
    - name: uniware-config
      configMap:
        name: cloud25-uniware
        defaultMode: 0777
    - name: uniware-vault-config
      configMap:
        name: cloud25-vault-uniware
        defaultMode: 0777
  service:
    hazelCastport: 5701
    type: ClusterIP
    headless:
      enabled: true
  spec:
    consolidation:
      enabled: true
global:
  filebeat:
    config:
      name: ${PODNAME}.cloud25-in.unicommerce.infra
      hosts:
        - "logstash1-in.unicommerce.infra:5044"
        - "logstash2-in.unicommerce.infra:5044"
  promtail:
    config:
      clients:
        - url: http://loki-gateway.monitoring.svc.cluster.local:80/loki/api/v1/push
          tenant_id: 'cloud25'
  efs:
    enabled: false
