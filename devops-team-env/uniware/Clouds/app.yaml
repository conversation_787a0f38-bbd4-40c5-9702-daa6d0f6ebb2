apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: uniware-clouds
  namespace: argocd
spec:
  syncPolicy:
    preserveResourcesOnDeletion: true
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
  - list:
      elements:
        - name: cloud1
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud2
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud3
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud4
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud5
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud6
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud7
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud8
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud9
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud10
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud11
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud12
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud13
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud14
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud15
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud16
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud17
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud18
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud19
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud20
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud21
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud22
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud23
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud25
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud26
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud27
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud28
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud29
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud30
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud31
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud32
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud33
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud34
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud35
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: cloud8testing
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
  template:
    metadata:
      name: 'uniware-{{.name}}'
    spec:
      project: '{{.project}}'
      sources:
        - repoURL: 'https://github.com/devops-unicommerce/devops-team-env.git'
          targetRevision: HEAD
          path: uniware/Clouds/{{.name}}
      destination:
        name: '{{.cluster}}'
        namespace: '{{.namespace}}'
    
  templatePatch: |
    spec:
    {{- if .autoSync }}
      syncPolicy:
        automated:
          prune: {{ .prune }}
          selfHeal: true
        syncOptions:
        - ApplyOutOfSyncOnly=true
    {{- end }}
