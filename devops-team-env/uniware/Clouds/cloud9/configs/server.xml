<?xml version='1.0' encoding='utf-8'?>

<Server port="8006" shutdown="SHUTDOWN">
  <Listener className="org.apache.catalina.startup.VersionLoggerListener" />
  <Listener className="org.apache.catalina.core.JreMemoryLeakPreventionListener" />
  <Listener className="org.apache.catalina.core.ThreadLocalLeakPreventionListener" />
  <Service name="Catalina">
    <Connector port="8080" protocol="org.apache.coyote.http11.Http11NioProtocol" compression="on" compressionMinSize="2048" noCompressionUserAgents="gozilla, traviata" compressableMimeType="text/html,text/xml,text/javascript,application/javascript,text/css,application/json" maxThreads="1000" minSpareThreads="50" acceptCount="1000" />
    <Engine name="Catalina" defaultHost="localhost">
      <Host name="localhost"  appBase="webapps"
            unpackWARs="true" autoDeploy="false">
	      <Valve className="org.apache.catalina.valves.RemoteIpValve" remoteIpHeader="X-Forwarded-For" internalProxies=".*" protocolHeader="X-Forwarded-Proto" protocolHeaderHttpsValue="https"/>
        <Valve className="com.uniware.tomcat.MaskedAccessLogValve" directory="logs" prefix="access_log" suffix=".txt" requestAttributesEnabled="true" pattern="[%{tenant}r %{username}r %{api_user_name}r] %h %t &quot;%m %U%q %H&quot; [%{soap_operation_name}r] [%{User-Agent}i] %s %b %D [%{androidAppReleaseVersion}i] [%{androidDeviceUUID}i] [%{egress_time}r %{script_excl_egress_time}r %{channel_src_code}r %{courier_src_code}r] [%{api_status}r %{request_identifier}r %{error_response_code}r %{error_response_message}r]" />
      </Host>
    <Cluster className="org.apache.catalina.ha.tcp.SimpleTcpCluster"
      channelSendOptions="6" channelStartOptions="3">
        <Manager className="org.apache.catalina.ha.session.DeltaManager"
            sendAllSessions="false" sendAllSessionsSize="100" sendAllSessionsWaitTime="200"/>

        <Channel className="org.apache.catalina.tribes.group.GroupChannel">
            <Receiver className="org.apache.catalina.tribes.transport.nio.NioReceiver"
                address="auto" port="4000" autoBind="0"/>

            <Sender className="org.apache.catalina.tribes.transport.ReplicationTransmitter">
                <Transport className="org.apache.catalina.tribes.transport.nio.PooledParallelSender" timeout="60000"/>
            </Sender>

            <Interceptor className="org.apache.catalina.tribes.group.interceptors.TcpPingInterceptor"/>
            <Interceptor className="org.apache.catalina.tribes.group.interceptors.TcpFailureDetector"/>
            <Interceptor className="org.apache.catalina.tribes.group.interceptors.StaticMembershipInterceptor">
                <Member className="org.apache.catalina.tribes.membership.StaticMember" port="4000" host="cloud9-uniware-api-0.prod.svc.cluster.local"/>
                <Member className="org.apache.catalina.tribes.membership.StaticMember" port="4000" host="cloud9-uniware-api-1.prod.svc.cluster.local"/>
            </Interceptor>
            <Interceptor className="org.apache.catalina.tribes.group.interceptors.ThroughputInterceptor"/>
        </Channel>

        <Valve className="org.apache.catalina.ha.tcp.ReplicationValve" filter=""/>

        <ClusterListener className="org.apache.catalina.ha.session.ClusterSessionListener"/>
    </Cluster>
    </Engine>
  </Service>
</Server>
