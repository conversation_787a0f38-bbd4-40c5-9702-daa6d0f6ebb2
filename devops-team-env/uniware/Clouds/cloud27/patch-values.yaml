enabled: true
replicaCount: 0
podSecurityContext:
  fsGroup: 1000
pdb:
  enabled: true
image:
  repository: nexus-docker.unicommerce.infra:8123/uniware
  pullPolicy: Always
  tag: 1714.2-k8s
imagePullSecrets:
  - name: nexus-secrets
service:
  type: ClusterIP
  hazelCastport: 5701
  headless:
    enabled: true
arguments:
  - "sed \"/$HOSTNAME/d\" /usr/local/tomcat/logs/server.xml >  /usr/local/tomcat/conf/server.xml && catalina.sh run"
resources:
  limits:
    cpu: 1
    memory: 10Gi
  requests:
    cpu: 1
    memory: 10Gi
nodeSelector:
  karpenter.sh/nodepool: clouds-ondemand-pool
tolerations:
  - key: "clouds/ondemand"
    operator: "Exists"
    effect: "NoSchedule"
init:
  arguments:
    - mkdir -p /tmp/RotatedAppLogs
    - /tmp/files/customizedTemplates
    - /tmp/files/reconciliationInvoices
    - /tmp/files/saleOrderReturn
    - /tmp/files/import/logs
    - /tmp/files/saleOrder
    - /tmp/files/shippingLabel
livenessProbe: {}
readinessProbe: {}
env:
  JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms512M -Xmx7200M -XX:MaxPermSize=512M -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -DtenantSpecificMongoConnectionsMaxSize=100 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/cloud27#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DactiveMQMaxConnections=5  -DserverNames=Cloud27 -DserverName=Cloud27 -DactiveMQQueuePrefetch=1 -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.c1-in.unicommerce.infra:2181 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DtenantSpecificMongoHosts=mongo1.c5-in.unicommerce.infra:27017,mongo2.c5-in.unicommerce.infra:27017 -DactiveMQBrokerUrl=failover:tcp://activemq.c1-in.unicommerce.infra:61616,tcp://activemq.c1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -Ds3bucketSuffix=-in -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DAsyncLoggerConfig.ExceptionHandler=com.lmax.disruptor.IgnoreExceptionHandler -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/cloud27#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DactiveMQBrokerPassword=<path:kv/data/uniware/cloud27#activeMQBrokerPassword> -DquartzThreadPoolSize=500 -DexecuteJobs=false -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -DappIdentifier=${HOSTNAME} -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017  -DactiveMQ2BrokerUrl=failover:tcp://activemq.c2-in.unicommerce.infra:61616,tcp://activemq.c2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/cloud27#activeMQBrokerPassword> -DclusterName=Cloud27 -XX:OnOutOfMemoryError=/bin/HeapDump -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource -XX:+UseG1GC -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -XX:+UseStringDeduplication -XX:StringDeduplicationAgeThreshold=15 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Dhazelcast.local.publicAddress=${HOSTNAME}.prod.svc.cluster.local:5701 -DhazelcastMembers=cloud27-uniware-api-0.prod.svc.cluster.local:5701,cloud27-uniware-task-0.cloud27-uniware-task.prod.svc.cluster.local:5701"
volumeMounts:
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
    subPath: vault.properties
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/vaultConfig
    name: uniware-vault-config
  - mountPath: /usr/local/tomcat/conf/context.xml
    subPath: context.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/server.xml
    subPath: server.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/catalina.sh
    subPath: catalina.sh
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/hazelcast.yml
    subPath: hazelcast.yml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
    subPath: gc_monitor.sh
    name: uniware-config
volumes:
  - name: uniware-config
    configMap:
      name: cloud27-uniware
      defaultMode: 0777
  - name: uniware-vault-config
    configMap:
      name: cloud27-vault-uniware
      defaultMode: 0777
ingress:
  enabled: false
  className: "haproxy-public"
  annotations:
    haproxy-ingress.github.io/config-backend: |
      http-request set-header X-Forwarded-Proto https
  hosts:
    - host: aarshinternational.unicommerce.com
    - host: aavni.unicommerce.com
    - host: advindhealthcarepvtltd.unicommerce.com
    - host: amrutvita.unicommerce.com
    - host: aquanza.unicommerce.com
    - host: arsilkdupattahub.unicommerce.com
    - host: arunasarees.unicommerce.com
    - host: ashbabotanics.unicommerce.com
    - host: ashkara.unicommerce.com
    - host: asossenterprises.unicommerce.com
    - host: axolotl.unicommerce.com
    - host: ballbox.unicommerce.com
    - host: baseprofessional27.unicommerce.com
    - host: beautybarn.unicommerce.com
    - host: beco.unicommerce.com
    - host: bellecreatives.unicommerce.com
    - host: belleziya.unicommerce.com
    - host: belloherbals.unicommerce.com
    - host: bhumieknitfab.unicommerce.com
    - host: braskindia.unicommerce.com
    - host: buddsbuddy.unicommerce.com
    - host: captain.unicommerce.com
    - host: caratcafe.unicommerce.com
    - host: cazzanoapparels.unicommerce.com
    - host: chandracrockery.unicommerce.com
    - host: chandranipearls.unicommerce.com
    - host: clapton.unicommerce.com
    - host: comicsense.unicommerce.com
    - host: cornetoverseas.unicommerce.com
    - host: cottonsandsatins.unicommerce.com
    - host: craftlipi.unicommerce.com
    - host: creativehands.unicommerce.com
    - host: cursive.unicommerce.com
    - host: delcaper.unicommerce.com
    - host: dkcreations.unicommerce.com
    - host: doodad.unicommerce.com
    - host: eethnicstore.unicommerce.com
    - host: elayne.unicommerce.com
    - host: eslifestyleoverseas.unicommerce.com
    - host: etnicawear.unicommerce.com
    - host: fabgalaxy.unicommerce.com
    - host: fapparel.unicommerce.com
    - host: fazals.unicommerce.com
    - host: feelfe.unicommerce.com
    - host: fitnutrition.unicommerce.com
    - host: flagbearer.unicommerce.com
    - host: frenchessence.unicommerce.com
    - host: ginza.unicommerce.com
    - host: glamhood.unicommerce.com
    - host: goldstarfootwearpvtltd.unicommerce.com
    - host: goldstarfootwear.unicommerce.com
    - host: greybox.unicommerce.com
    - host: guppies.unicommerce.com
    - host: hartoshaffinity.unicommerce.com
    - host: herbidus.unicommerce.com
    - host: hisra.unicommerce.com
    - host: huggun.unicommerce.com
    - host: indianepic.unicommerce.com
    - host: isakfragrances.unicommerce.com
    - host: iwf03.unicommerce.com
    - host: jaig.unicommerce.com
    - host: jantabags.unicommerce.com
    - host: jantahandbagstore.unicommerce.com
    - host: jdretailindia.unicommerce.com
    - host: jewelworld.unicommerce.com
    - host: kakufancydresses.unicommerce.com
    - host: katrends.unicommerce.com
    - host: kavyasarees.unicommerce.com
    - host: kedarfab.unicommerce.com
    - host: keralanaturals.unicommerce.com
    - host: kiarvi.unicommerce.com
    - host: killer.unicommerce.com
    - host: kripaluagencies.unicommerce.com
    - host: kscretails.unicommerce.com
    - host: kurtibrand.unicommerce.com
    - host: leathercraftexim.unicommerce.com
    - host: lorem.unicommerce.com
    - host: lueurvesturespvtltd.unicommerce.com
    - host: maruti.unicommerce.com
    - host: meerapretails.unicommerce.com
    - host: mglobalindia.unicommerce.com
    - host: mglobal.unicommerce.com
    - host: microbeelifesciencellp.unicommerce.com
    - host: milesretail.unicommerce.com
    - host: mjenterptises.unicommerce.com
    - host: modernjewellerytools.unicommerce.com
    - host: mrcreation01.unicommerce.com
    - host: mushio.unicommerce.com
    - host: myratexworld.unicommerce.com
    - host: nimbarka.unicommerce.com
    - host: nitein.unicommerce.com
    - host: offiratexworld.unicommerce.com
    - host: olivefashions.unicommerce.com
    - host: omicronfab.unicommerce.com
    - host: paiofootwearandaccessoriesllp.unicommerce.com
    - host: palival.unicommerce.com
    - host: parodebiimpexllp.unicommerce.com
    - host: peonycottonfab.unicommerce.com
    - host: peonysmartworld.unicommerce.com
    - host: pmkkgems.unicommerce.com
    - host: prashantadvaitfoundation.unicommerce.com
    - host: preetyhubappare.unicommerce.com
    - host: premroopthestyleyoulove.unicommerce.com
    - host: pulakin.unicommerce.com
    - host: purelifestyle.unicommerce.com
    - host: ramahandicraft.unicommerce.com
    - host: rangaraa.unicommerce.com
    - host: redwolf98.unicommerce.com
    - host: rexburg.unicommerce.com
    - host: robecult.unicommerce.com
    - host: royalmishty.unicommerce.com
    - host: rranakcreation.unicommerce.com
    - host: samigarments.unicommerce.com
    - host: sandeeclothingco93.unicommerce.com
    - host: sandpuppy.unicommerce.com
    - host: sdesigner.unicommerce.com
    - host: sekhaniindustries.unicommerce.com
    - host: shakambarifoodproducts.unicommerce.com
    - host: sharpexengineering.unicommerce.com
    - host: shiprabead.unicommerce.com
    - host: shivamhandicraft.unicommerce.com
    - host: shocknshop1.unicommerce.com
    - host: shocknshop.unicommerce.com
    - host: shreejidesigner.unicommerce.com
    - host: silvercrestclothingpvtltd.unicommerce.com
    - host: skyasia.unicommerce.com
    - host: soccerinternationalpvtltd.unicommerce.com
    - host: sterlingbookcenter.unicommerce.com
    - host: sterlingbookcentre.unicommerce.com
    - host: tarabooks.unicommerce.com
    - host: thegentlemensclub.unicommerce.com
    - host: thegrowinggiraffe.unicommerce.com
    - host: thenaaznx.unicommerce.com
    - host: tnw.unicommerce.com
    - host: tomdoxx.unicommerce.com
    - host: tothub.unicommerce.com
    - host: trendif1.unicommerce.com
    - host: trendif2.unicommerce.com
    - host: trendifapparal.unicommerce.com
    - host: trendif.unicommerce.com
    - host: ubrenterprises1.unicommerce.com
    - host: vagadskhadi.unicommerce.com
    - host: vendicate.unicommerce.com
    - host: vilaorganics.unicommerce.com
    - host: vivinks.unicommerce.com
    - host: voom.unicommerce.com
    - host: vybnindustries.unicommerce.com
    - host: weddingvastra.unicommerce.com
    - host: wheezalhomoeopharma.unicommerce.com
    - host: wiepl.unicommerce.com
    - host: wobbly.unicommerce.com
    - host: wowbag.unicommerce.com
    - host: xpressimp.unicommerce.com
    - host: yogiexports.unicommerce.com
    - host: zaveripearls.unicommerce.com
    - host: zavlinapparels.unicommerce.com
    - host: zavlin.unicommerce.com
    - host: zeezeezoo.unicommerce.com
  tls: []
istio:
  enabled: true
autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 75
uniware-task:
  enabled: true
  podSecurityContext:
    fsGroup: 1000
  resources:
    limits:
      cpu: 2.0
      memory: 15.1Gi
    requests:
      cpu: 2.0
      memory: 15.1Gi
  nodeSelector:
    karpenter.sh/nodepool: clouds-spot-pool
  tolerations:
    - key: "clouds/spot"
      operator: "Exists"
      effect: "NoSchedule"
  image:
    repository: nexus-docker.unicommerce.infra:8123/uniware
    pullPolicy: Always
    tag: 1714.2-k8s
  imagePullSecrets:
    - name: nexus-secrets
  init:
    arguments:
      - mkdir -p /tmp/RotatedAppLogs
      - /tmp/files/customizedTemplates
      - /tmp/files/reconciliationInvoices
      - /tmp/files/saleOrderReturn
      - /tmp/files/import/logs
      - /tmp/files/saleOrder
      - /tmp/files/shippingLabel
  livenessProbe: {}
  #   exec:

  #     command:

  #     - /bin/bash

  #     - -c

  #     - |-

  #       health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/health")

  #       if [[ ${health_status} -ne 200 ]]; then exit 1; fi

  #   failureThreshold: 3

  #   periodSeconds: 30

  #   successThreshold: 1

  #   timeoutSeconds: 10

  #   initialDelaySeconds: 600
  readinessProbe: {}
  #   exec:

  #     command:

  #     - /bin/bash

  #     - -c

  #     - |-

  #       health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/health")

  #       if [[ ${health_status} -ne 200 ]]; then exit 1; fi

  #   failureThreshold: 3

  #   periodSeconds: 30

  #   successThreshold: 1

  #   timeoutSeconds: 10

  #   initialDelaySeconds: 180
  env:
    JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -XX:MaxPermSize=512M -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -DtenantSpecificMongoConnectionsMaxSize=100 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/cloud27#prefetchActiveMQ2BrokerPassword>  -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DactiveMQMaxConnections=50 -DactiveMQQueuePrefetch=1 -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DAsyncLoggerConfig.ExceptionHandler=com.lmax.disruptor.IgnoreExceptionHandler -DzookeeperUrl=zookeeper.c1-in.unicommerce.infra:2181 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DtenantSpecificMongoHosts=mongo1.c5-in.unicommerce.infra:27017,mongo2.c5-in.unicommerce.infra:27017 -DactiveMQBrokerUrl=failover:tcp://activemq.c1-in.unicommerce.infra:61616,tcp://activemq.c1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=Cloud27 -DserverName=Cloud27 -Ds3bucketSuffix=-in -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/cloud27#activeMQBrokerPassword>  -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DactiveMQBrokerPassword=<path:kv/data/uniware/cloud27#activeMQBrokerPassword>  -DquartzThreadPoolSize=50 -DAsyncLogger.ThreadNameStrategy=UNCACHED -DappIdentifier=app2 -DexecuteJobs=true -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DactiveMQ2BrokerUrl=failover:tcp://activemq.c2-in.unicommerce.infra:61616,tcp://activemq.c2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/cloud27#activeMQBrokerPassword>  -DclusterName=Cloud27 -XX:OnOutOfMemoryError=/bin/HeapDump -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -XX:+UseG1GC -XX:+UseStringDeduplication -XX:StringDeduplicationAgeThreshold=15 -DdataSource=UniwareHikariDataSource -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xms2000M -Xmx15100M -Dhazelcast.local.publicAddress=${HOSTNAME}.cloud27-uniware-task.prod.svc.cluster.local:5701 -DhazelcastMembers=cloud27-uniware-api-0.prod.svc.cluster.local:5701,cloud27-uniware-task-0.cloud27-uniware-task.prod.svc.cluster.local:5701 -DrecoSpecificCommonMongoConnectionsMaxSize=50"
  volumeMounts:
    - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
      subPath: vault.properties
      name: uniware-config
    - mountPath: /usr/local/tomcat/logs/vaultConfig
      name: uniware-vault-config
    - mountPath: /usr/local/tomcat/conf/context.xml
      subPath: context.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/conf/server.xml
      subPath: server.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/catalina.sh
      subPath: catalina.sh
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/hazelcast.yml
      subPath: hazelcast.yml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
      subPath: gc_monitor.sh
      name: uniware-config
  volumes:
    - name: uniware-config
      configMap:
        name: cloud27-uniware
        defaultMode: 0777
    - name: uniware-vault-config
      configMap:
        name: cloud27-vault-uniware
        defaultMode: 0777
  service:
    hazelCastport: 5701
    type: ClusterIP
    headless:
      enabled: true
  spec:
    consolidation:
      enabled: true
global:
  filebeat:
    config:
      name: ${PODNAME}.cloud27-in.unicommerce.infra
      hosts:
        - "logstash1-in.unicommerce.infra:5044"
        - "logstash2-in.unicommerce.infra:5044"
  promtail:
    config:
      clients:
        - url: http://loki-gateway.monitoring.svc.cluster.local:80/loki/api/v1/push
          tenant_id: 'cloud27'
  efs:
    enabled: false
