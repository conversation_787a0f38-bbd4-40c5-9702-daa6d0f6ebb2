enabled: true
replicaCount: 0
podSecurityContext:
  fsGroup: 1000
pdb:
  enabled: true
image:
  repository: nexus-docker.unicommerce.infra:8123/uniware
  pullPolicy: Always
  tag: 1714.2-k8s
imagePullSecrets:
  - name: nexus-secrets
service:
  type: ClusterIP
  hazelCastport: 5701
  headless:
    enabled: true
arguments:
  - "sed \"/$HOSTNAME/d\" /usr/local/tomcat/logs/server.xml >  /usr/local/tomcat/conf/server.xml && catalina.sh run"
resources:
  limits:
    cpu: 2
    memory: 10.3Gi
  requests:
    cpu: 2
    memory: 10.3Gi
nodeSelector:
  karpenter.sh/nodepool: clouds-ondemand-pool
tolerations:
  - key: "clouds/ondemand"
    operator: "Exists"
    effect: "NoSchedule"
init:
  arguments:
    - mkdir -p /tmp/RotatedAppLogs
    - /tmp/files/customizedTemplates
    - /tmp/files/reconciliationInvoices
    - /tmp/files/saleOrderReturn
    - /tmp/files/import/logs
    - /tmp/files/saleOrder
    - /tmp/files/shippingLabel
livenessProbe: {}
readinessProbe: {}
env:
  JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms512M -Xmx7400M -XX:MaxPermSize=256M -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -DtenantSpecificMongoConnectionsMaxSize=10 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/cloud15#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616  -DactiveMQMaxConnections=50 -DserverName=Cloud15 -Ds3bucketSuffix=-in -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/cloud15#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DactiveMQQueuePrefetch=1 -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DAsyncLoggerConfig.ExceptionHandler=com.lmax.disruptor.IgnoreExceptionHandler -DzookeeperUrl=zookeeper.c1-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo3.c2-in.unicommerce.infra:27017,mongo4.c2-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DactiveMQBrokerUrl=failover:tcp://activemq.c1-in.unicommerce.infra:61616,tcp://activemq.c1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=Cloud15 -DactiveMQBrokerPassword=<path:kv/data/uniware/cloud15#activeMQBrokerPassword> -DquartzThreadPoolSize=50 -DappIdentifier=app2 -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DactiveMQ2BrokerUrl=failover:tcp://activemq.c2-in.unicommerce.infra:61616,tcp://activemq.c2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/cloud15#activeMQBrokerPassword> -DclusterName=Cloud15 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -XX:+UseG1GC -XX:+UseStringDeduplication -XX:StringDeduplicationAgeThreshold=15 -DdataSource=UniwareHikariDataSource -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DexecuteJobs=false -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xms512M -Xmx7960M -Dhazelcast.local.publicAddress=${HOSTNAME}.prod.svc.cluster.local:5701 -DhazelcastMembers=cloud15-uniware-api-0.prod.svc.cluster.local:5701,cloud15-uniware-task-0.cloud15-uniware-task.prod.svc.cluster.local:5701"
volumeMounts:
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
    subPath: vault.properties
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/vaultConfig
    name: uniware-vault-config
  - mountPath: /usr/local/tomcat/conf/context.xml
    subPath: context.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/server.xml
    subPath: server.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/catalina.sh
    subPath: catalina.sh
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/hazelcast.yml
    subPath: hazelcast.yml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
    subPath: gc_monitor.sh
    name: uniware-config
volumes:
  - name: uniware-config
    configMap:
      name: cloud15-uniware
      defaultMode: 0777
  - name: uniware-vault-config
    configMap:
      name: cloud15-vault-uniware
      defaultMode: 0777
ingress:
  enabled: false
  className: "haproxy-public"
  annotations:
    haproxy-ingress.github.io/config-backend: |
      http-request set-header X-Forwarded-Proto https
  hosts:
    - host: 3gsafetech.unicommerce.com
    - host: santkrupaart1.unicommerce.com
    - host: babyguru.unicommerce.com
    - host: naranshahealthcareprivatelimited.unicommerce.com
    - host: milanevents.unicommerce.com
    - host: dresico.unicommerce.com
    - host: healthforest.unicommerce.com
    - host: millethouse.unicommerce.com
    - host: confidoindiasurat.unicommerce.com
    - host: labelshauryasanadhya.unicommerce.com
    - host: filliandme.unicommerce.com
    - host: faboilsllp.unicommerce.com
    - host: johnsumbrellamart.unicommerce.com
    - host: aones.unicommerce.com
    - host: gottago1.unicommerce.com
    - host: agarsan.unicommerce.com
    - host: uniponkids.unicommerce.com
    - host: weezyinnovationsprivatelimited.unicommerce.com
    - host: varniexim1.unicommerce.com
    - host: trivediexim.unicommerce.com
    - host: denimlook.unicommerce.com
    - host: catepultventures.unicommerce.com
    - host: showoff-unireco.unicommerce.com
    - host: wisconindustrieslimited.unicommerce.com
    - host: organicstorein.unicommerce.com
    - host: getgolife.unicommerce.com
    - host: suavebags.unicommerce.com
    - host: aaranyaprints1.unicommerce.com
    - host: drzamanindustries.unicommerce.com
    - host: gullaksurat1.unicommerce.com
    - host: ojascorporatesolutions.unicommerce.com
    - host: silkhouse1111.unicommerce.com
    - host: aaina.unicommerce.com
    - host: vsareesurat.unicommerce.com
    - host: valiantglassworksprivatelimited.unicommerce.com
    - host: shreekrishnaexim1.unicommerce.com
    - host: minesole.unicommerce.com
    - host: harikrushnafashion1.unicommerce.com
    - host: physil.unicommerce.com
    - host: gopaltex.unicommerce.com
    - host: shivfashion001.unicommerce.com
    - host: tophillpharmalimited.unicommerce.com
    - host: coppertino.unicommerce.com
    - host: satyayfashion.unicommerce.com
    - host: truefitt.unicommerce.com
    - host: jsclothingco.unicommerce.com
    - host: dnaventuresprivatelimited.unicommerce.com
    - host: dishaenterprises2.unicommerce.com
    - host: skinvest.unicommerce.com
    - host: oriventurespvtltd.unicommerce.com
    - host: bareilyy.unicommerce.com
    - host: labellibra1.unicommerce.com
    - host: medfashionsprivatelimited.unicommerce.com
    - host: shopekart.unicommerce.com
    - host: vnenterprise1.unicommerce.com
    - host: nestatoysprivatelimited.unicommerce.com
    - host: trendyculture.unicommerce.com
    - host: delemiinternationalllp.unicommerce.com
    - host: pratapsons.unicommerce.com
    - host: loleyretail1.unicommerce.com
    - host: by2men.unicommerce.com
    - host: gardenparty1.unicommerce.com
    - host: nutrivate.unicommerce.com
    - host: houseofsarso.unicommerce.com
    - host: gardenparty.unicommerce.com
    - host: durior.unicommerce.com
    - host: urbanice1.unicommerce.com
    - host: saanvi1.unicommerce.com
    - host: saanvi.unicommerce.com
    - host: baraqahventure.unicommerce.com
    - host: sjlttextilesprivatelimited.unicommerce.com
    - host: netpill24x7servicesprivatelimited.unicommerce.com
    - host: seeaash.unicommerce.com
    - host: litetestacc2356.unicommerce.com
    - host: litetest8986e767.unicommerce.com
    - host: adityatradingcompany.unicommerce.com
    - host: litetest98292.unicommerce.com
    - host: fitright.unicommerce.com
    - host: totalsportingfitnesssolutionsprivatelimited.unicommerce.com
    - host: litetest98291.unicommerce.com
    - host: ecstacbeauty.unicommerce.com
    - host: manishfashionworldprivatelimited.unicommerce.com
    - host: shribalajegarments1.unicommerce.com
    - host: anmolmarketing.unicommerce.com
    - host: shivtextilesurat.unicommerce.com
    - host: mishreetradelink.unicommerce.com
    - host: bminternationalpvtltd.unicommerce.com
    - host: kriyaenterprise.unicommerce.com
    - host: nud.unicommerce.com
    - host: jaitraapparels.unicommerce.com
    - host: coreasana.unicommerce.com
    - host: emporiumfootwear.unicommerce.com
    - host: prernaimpex.unicommerce.com
    - host: pearl7industries.unicommerce.com
    - host: gkclassiccollections.unicommerce.com
    - host: vinitenterprisesurat.unicommerce.com
    - host: vmenterprises1.unicommerce.com
    - host: shawcreation.unicommerce.com
    - host: tiaraworld.unicommerce.com
    - host: bebebum.unicommerce.com
    - host: s4foryou.unicommerce.com
    - host: jmilanenterprise.unicommerce.com
    - host: theecocraft.unicommerce.com
    - host: steppingstoneretailprivatelimited.unicommerce.com
    - host: vmenterprises.unicommerce.com
    - host: mummamitra.unicommerce.com
    - host: brokememers.unicommerce.com
    - host: olefialimited.unicommerce.com
    - host: thebeanco.unicommerce.com
    - host: ltwt.unicommerce.com
    - host: impetusconsulting.unicommerce.com
    - host: studiob1.unicommerce.com
    - host: navkarcrafts.unicommerce.com
    - host: misswish01.unicommerce.com
    - host: 5thviewretail.unicommerce.com
    - host: limracreations.unicommerce.com
    - host: maxcommerce001.unicommerce.com
    - host: shaziacreation001.unicommerce.com
    - host: anantanandeximsllp.unicommerce.com
    - host: foozglobal.unicommerce.com
    - host: gardenvareli.unicommerce.com
    - host: bapnadigitalservicesopcpvtltd.unicommerce.com
    - host: bapnadigitalservices1.unicommerce.com
    - host: sufiza.unicommerce.com
    - host: bapnadigitalservices.unicommerce.com
    - host: bapnadigitalservicesopcprivatelimited.unicommerce.com
    - host: octavianenterprise0007.unicommerce.com
    - host: sairam001.unicommerce.com
    - host: epicandflair.unicommerce.com
    - host: rivrshoes.unicommerce.com
    - host: umadaivaantraders.unicommerce.com
    - host: globalmed.unicommerce.com
    - host: heeraindustries.unicommerce.com
    - host: asenggproducts007.unicommerce.com
    - host: pramukhcreation0007.unicommerce.com
    - host: amwoodoeco.unicommerce.com
    - host: saicommercial.unicommerce.com
    - host: indrasugandh.unicommerce.com
    - host: lorithglobalprivatelimited.unicommerce.com
    - host: discoverdiamonds.unicommerce.com
    - host: vidyasagarayurvedapharmacy.unicommerce.com
    - host: fashionbucket1.unicommerce.com
    - host: nijenterprise001.unicommerce.com
    - host: kynexx.unicommerce.com
    - host: shopifyapptest2.unicommerce.com
    - host: chintamanicovers.unicommerce.com
    - host: androclothingcompany.unicommerce.com
    - host: agarwalandsons.unicommerce.com
    - host: anokh.unicommerce.com
    - host: armaanethnic.unicommerce.com
    - host: ashniapparel.unicommerce.com
    - host: autumnhues.unicommerce.com
    - host: ayushveda.unicommerce.com
    - host: bailnaturals.unicommerce.com
    - host: balkeshwar.unicommerce.com
    - host: ballerathletik.unicommerce.com
    - host: baseprofessional15.unicommerce.com
    - host: bhpc.unicommerce.com
    - host: biglilpeople.unicommerce.com
    - host: binniswardrobellp.unicommerce.com
    - host: carusoitaly.unicommerce.com
    - host: charliecarlos.unicommerce.com
    - host: chuimuienterprise.unicommerce.com
    - host: completeclothingsolutions.unicommerce.com
    - host: conekt.unicommerce.com
    - host: cpindustries.unicommerce.com
    - host: deepaliunderrootllp.unicommerce.com
    - host: dhanharproductsllp.unicommerce.com
    - host: effectsunicommerce.unicommerce.com
    - host: enbeetretailllp.unicommerce.com
    - host: epsilon.unicommerce.com
    - host: eshopajio.unicommerce.com
    - host: ethnava.unicommerce.com
    - host: faccefelici.unicommerce.com
    - host: firstworld.unicommerce.com
    - host: fityogi.unicommerce.com
    - host: funkforhire.unicommerce.com
    - host: gillori.unicommerce.com
    - host: glamaze.unicommerce.com
    - host: godfreyglobal.unicommerce.com
    - host: happyheartscare.unicommerce.com
    - host: happykhajana.unicommerce.com
    - host: harshittradingco.unicommerce.com
    - host: heelium.unicommerce.com
    - host: hiphopskincare.unicommerce.com
    - host: jewelfuel.unicommerce.com
    - host: jjaagg.unicommerce.com
    - host: joyoplastics.unicommerce.com
    - host: k11retails.unicommerce.com
    - host: kafi.unicommerce.com
    - host: kravika.unicommerce.com
    - host: kunnskaptechnologies.unicommerce.com
    - host: lltworld.unicommerce.com
    - host: maabhagwati.unicommerce.com
    - host: mahavirenterprises.unicommerce.com
    - host: manlino.unicommerce.com
    - host: mkh.unicommerce.com
    - host: nagpaltraders.unicommerce.com
    - host: nishika.unicommerce.com
    - host: nvmaterials.unicommerce.com
    - host: pangolin.unicommerce.com
    - host: parallelkart.unicommerce.com
    - host: picotsale.unicommerce.com
    - host: plg.unicommerce.com
    - host: psdesign01.unicommerce.com
    - host: qassumenterprises.unicommerce.com
    - host: qkcreation.unicommerce.com
    - host: rageenterprise.unicommerce.com
    - host: rainacreation.unicommerce.com
    - host: revexo.unicommerce.com
    - host: ruvifashionllp.unicommerce.com
    - host: sanaa.unicommerce.com
    - host: showoff.unicommerce.com
    - host: shwetacreations.unicommerce.com
    - host: skinhabits.unicommerce.com
    - host: sstrader.unicommerce.com
    - host: susajjit.unicommerce.com
    - host: svnenterprises.unicommerce.com
    - host: topluck.unicommerce.com
    - host: tripleplaytechnologiespvtltd.unicommerce.com
    - host: tromboocrafts.unicommerce.com
    - host: trustedpartners.unicommerce.com
    - host: unistarshoes.unicommerce.com
    - host: veenuenterprises.unicommerce.com
    - host: vitaminhaat.unicommerce.com
    - host: vstraders.unicommerce.com
    - host: wisstler.unicommerce.com
  tls: []
istio:
  enabled: true
autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 75
uniware-task:
  enabled: true
  podSecurityContext:
    fsGroup: 1000
  resources:
    limits:
      cpu: 3.0
      memory: 12.0Gi
    requests:
      cpu: 3.0
      memory: 12.0Gi
  nodeSelector:
    karpenter.sh/nodepool: clouds-spot-pool
  tolerations:
    - key: "clouds/spot"
      operator: "Exists"
      effect: "NoSchedule"
  image:
    repository: nexus-docker.unicommerce.infra:8123/uniware
    pullPolicy: Always
    tag: 1714.2-k8s
  imagePullSecrets:
    - name: nexus-secrets
  init:
    arguments:
      - mkdir -p /tmp/RotatedAppLogs
      - /tmp/files/customizedTemplates
      - /tmp/files/reconciliationInvoices
      - /tmp/files/saleOrderReturn
      - /tmp/files/import/logs
      - /tmp/files/saleOrder
      - /tmp/files/shippingLabel
  livenessProbe: {}
  # exec:
  #   command:
  #   - /bin/bash
  #   - -c
  #   - |-
  #     health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/health")
  #     if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  # failureThreshold: 3
  # periodSeconds: 30
  # successThreshold: 1
  # timeoutSeconds: 10
  # initialDelaySeconds: 600
  readinessProbe: {}
  # exec:
  #   command:
  #   - /bin/bash
  #   - -c
  #   - |-
  #     health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/health")
  #     if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  # failureThreshold: 3
  # periodSeconds: 30
  # successThreshold: 1
  # timeoutSeconds: 10
  # initialDelaySeconds: 180
  env:
    JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -XX:MaxPermSize=256M -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -DtenantSpecificMongoConnectionsMaxSize=10 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/cloud15#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616  -DactiveMQMaxConnections=50 -DserverName=Cloud15 -Ds3bucketSuffix=-in -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/cloud15#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DactiveMQQueuePrefetch=1 -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DAsyncLoggerConfig.ExceptionHandler=com.lmax.disruptor.IgnoreExceptionHandler -DzookeeperUrl=zookeeper.c1-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo3.c2-in.unicommerce.infra:27017,mongo4.c2-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DactiveMQBrokerUrl=failover:tcp://activemq.c1-in.unicommerce.infra:61616,tcp://activemq.c1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=Cloud15 -DactiveMQBrokerPassword=<path:kv/data/uniware/cloud15#activeMQBrokerPassword> -DquartzThreadPoolSize=50 -DappIdentifier=app2 -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DactiveMQ2BrokerUrl=failover:tcp://activemq.c2-in.unicommerce.infra:61616,tcp://activemq.c2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/cloud15#activeMQBrokerPassword> -DclusterName=Cloud15 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -XX:+UseG1GC -XX:+UseStringDeduplication -XX:StringDeduplicationAgeThreshold=15 -DdataSource=UniwareHikariDataSource -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DexecuteJobs=true -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xms6000M -Xmx9000M -Dhazelcast.local.publicAddress=${HOSTNAME}.cloud15-uniware-task.prod.svc.cluster.local:5701 -DhazelcastMembers=cloud15-uniware-api-0.prod.svc.cluster.local:5701,cloud15-uniware-task-0.cloud15-uniware-task.prod.svc.cluster.local:5701 -DrecoSpecificCommonMongoConnectionsMaxSize=50"
  volumeMounts:
    - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
      subPath: vault.properties
      name: uniware-config
    - mountPath: /usr/local/tomcat/logs/vaultConfig
      name: uniware-vault-config
    - mountPath: /usr/local/tomcat/conf/context.xml
      subPath: context.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/conf/server.xml
      subPath: server.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/catalina.sh
      subPath: catalina.sh
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/hazelcast.yml
      subPath: hazelcast.yml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
      subPath: gc_monitor.sh
      name: uniware-config
  volumes:
    - name: uniware-config
      configMap:
        name: cloud15-uniware
        defaultMode: 0777
    - name: uniware-vault-config
      configMap:
        name: cloud15-vault-uniware
        defaultMode: 0777
  service:
    hazelCastport: 5701
    type: ClusterIP
    headless:
      enabled: true
  spec:
    consolidation:
      enabled: true
global:
  filebeat:
    config:
      name: ${PODNAME}.cloud15-in.unicommerce.infra
      hosts:
        - "logstash1-in.unicommerce.infra:5044"
        - "logstash2-in.unicommerce.infra:5044"
  promtail:
    config:
      clients:
        - url: http://loki-gateway.monitoring.svc.cluster.local:80/loki/api/v1/push
          tenant_id: 'cloud15'
  efs:
    enabled: false
