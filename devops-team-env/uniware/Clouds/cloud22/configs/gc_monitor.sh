#!/bin/bash

LOG_FILE=$(ls -t /usr/local/apache-tomcat/logs/gc.log.* | head -n 1)
START_TIME=$(date -d '4 minutes ago' +'%Y-%m-%dT%H:%M:%S')
END_TIME=$(date +'%Y-%m-%dT%H:%M:%S')

ALL_GC_COUNT=$(awk -v start="$START_TIME" -v end="$END_TIME" '$0 > start && $0 <= end' "$LOG_FILE" | grep -oE "\[GC.*\]" | wc -l)

FULL_GC_COUNT=$(awk -v start="$START_TIME" -v end="$END_TIME" '$0 > start && $0 <= end' "$LOG_FILE" | grep -oE "\[Full GC.*\]" | wc -l)
if [ "$ALL_GC_COUNT" -ne 0 ]; then
    FULL_GC_PERCENT=$((FULL_GC_COUNT * 100 / ALL_GC_COUNT))
else
    FULL_GC_PERCENT=0
fi


if [ "$FULL_GC_COUNT" -gt 1 ]; then
     status1="critical"
     
fi

echo "fullgc_count=${FULL_GC_COUNT}"

if [ "$status1" == "critical" ] ; then
exit 2
fi
