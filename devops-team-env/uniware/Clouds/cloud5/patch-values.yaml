enabled: true
replicaCount: 0
podSecurityContext:
  fsGroup: 1000
pdb:
  enabled: true
image:
  repository: nexus-docker.unicommerce.infra:8123/uniware
  pullPolicy: IfNotPresent
  tag: 1714.2-k8s
imagePullSecrets:
  - name: nexus-secrets
service:
  type: ClusterIP
  hazelCastport: 5701
  headless:
    enabled: true
arguments:
  - "sed \"/$HOSTNAME/d\" /usr/local/tomcat/logs/server.xml >  /usr/local/tomcat/conf/server.xml && catalina.sh run"
resources:
  limits:
    cpu: 0.8
    memory: 4.0Gi
  requests:
    cpu: 0.8
    memory: 4.0Gi
nodeSelector:
  karpenter.sh/nodepool: clouds-ondemand-pool
affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app.kubernetes.io/instance: cloud5
            app.kubernetes.io/name: uniware-api
        topologyKey: kubernetes.io/hostname
tolerations:
  - key: "clouds/ondemand"
    operator: "Exists"
    effect: "NoSchedule"
init:
  arguments:
    - mkdir -p /tmp/RotatedAppLogs
    - /tmp/files/customizedTemplates
    - /tmp/files/reconciliationInvoices
    - /tmp/files/saleOrderReturn
    - /tmp/files/import/logs
    - /tmp/files/saleOrder
    - /tmp/files/shippingLabel
livenessProbe:
  exec:
    command:
      - /bin/bash
      - -c
      - |-
        health_status=$(curl --write-out "%{http_code}\n" -H "Host: baseprofessional5.unicommerce.com" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/live")
        if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  failureThreshold: 5
  periodSeconds: 60
  successThreshold: 1
  timeoutSeconds: 30
  initialDelaySeconds: 300
readinessProbe:
  exec:
    command:
      - /bin/bash
      - -c
      - |-
        health_status=$(curl --write-out "%{http_code}\n" -H "Host: baseprofessional5.unicommerce.com" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/live")
        if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  failureThreshold: 5
  periodSeconds: 60
  successThreshold: 1
  timeoutSeconds: 30
  initialDelaySeconds: 180
env:
  JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -XX:MetaspaceSize=512M  -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -DtenantSpecificMongoConnectionsMaxSize=10 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/cloud5#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DactiveMQMaxConnections=5 -DAsyncLoggerConfig.ExceptionHandler=com.lmax.disruptor.IgnoreExceptionHandler -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/cloud5#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DactiveMQQueuePrefetch=1 -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.c1-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo5.c1-in.unicommerce.infra:27017,mongo6.c1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DactiveMQBrokerUrl=failover:tcp://activemq.c1-in.unicommerce.infra:61616,tcp://activemq.c1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -Ds3bucketSuffix=-in -DserverNames=Cloud5 -DserverName=Cloud5 -DactiveMQBrokerPassword=<path:kv/data/uniware/cloud5#activeMQBrokerPassword> -DquartzThreadPoolSize=500 -DexecuteJobs=false -DappIdentifier=${HOSTNAME} -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DactiveMQ2BrokerUrl=failover:tcp://activemq.c2-in.unicommerce.infra:61616,tcp://activemq.c2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/cloud5#activeMQBrokerPassword> -DclusterName=Cloud5 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xms512M -Xmx2900M -Dhazelcast.local.publicAddress=${HOSTNAME}.prod.svc.cluster.local:5701 -DhazelcastMembers=cloud5-uniware-api-0.prod.svc.cluster.local:5701,cloud5-uniware-api-1.prod.svc.cluster.local:5701,cloud5-uniware-task-0.cloud5-uniware-task.prod.svc.cluster.local:5701"
volumeMounts:
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
    subPath: vault.properties
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/vaultConfig
    name: uniware-vault-config
  - mountPath: /usr/local/tomcat/conf/context.xml
    subPath: context.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/server.xml
    subPath: server.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/catalina.sh
    subPath: catalina.sh
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/hazelcast.yml
    subPath: hazelcast.yml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
    subPath: gc_monitor.sh
    name: uniware-config
volumes:
  - name: uniware-config
    configMap:
      name: cloud5-uniware
      defaultMode: 0777
  - name: uniware-vault-config
    configMap:
      name: cloud5-vault-uniware
      defaultMode: 0777
ingress:
  enabled: false
  className: "haproxy-public"
  annotations:
    haproxy-ingress.github.io/config-backend: |
      http-request set-header X-Forwarded-Proto https
  hosts:
    - host: cablesetc.unicommerce.com
    - host: georgianexims.unicommerce.com
    - host: highlife.unicommerce.com
    - host: looks21.unicommerce.com
    - host: hshsnutra-unireco.unicommerce.com
    - host: purys.unicommerce.com
    - host: rl.unicommerce.com
    - host: sanddune.unicommerce.com
    - host: sports365.unicommerce.com
    - host: taurus.unicommerce.com
    - host: texperts.unicommerce.com
    - host: tirupatienterprise.unicommerce.com
    - host: wasans.unicommerce.com
    - host: baseprofessional5.unicommerce.com
    - host: basestandard5.unicommerce.com
  tls: []
istio:
  enabled: true
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 2
  targetCPUUtilizationPercentage: 75
uniware-task:
  enabled: true
  podSecurityContext:
    fsGroup: 1000
  resources:
    limits:
      cpu: 1.5
      memory: 5.2Gi
    requests:
      cpu: 1.5
      memory: 5.2Gi
  nodeSelector:
    karpenter.sh/nodepool: clouds-spot-pool
  tolerations:
    - key: "clouds/spot"
      operator: "Exists"
      effect: "NoSchedule"
  image:
    repository: nexus-docker.unicommerce.infra:8123/uniware
    pullPolicy: Always
    tag: 1714.2-k8s
  imagePullSecrets:
    - name: nexus-secrets
  init:
    arguments:
      - mkdir -p /tmp/RotatedAppLogs
      - /tmp/files/customizedTemplates
      - /tmp/files/reconciliationInvoices
      - /tmp/files/saleOrderReturn
      - /tmp/files/import/logs
      - /tmp/files/saleOrder
      - /tmp/files/shippingLabel
  livenessProbe: {}
  #exec:
  #  command:
  #  - /bin/bash
  #  - -c
  #  - |-
  #    health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/health")
  #    if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  #failureThreshold: 3
  #periodSeconds: 30
  #successThreshold: 1
  #timeoutSeconds: 10
  #initialDelaySeconds: 1000
  readinessProbe: {}
  #exec:
  # command:
  # - /bin/bash
  # - -c
  # - |-
  #   health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/health")
  #   if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  #failureThreshold: 3
  #periodSeconds: 30
  #successThreshold: 1
  #timeoutSeconds: 10
  #initialDelaySeconds: 180
  env:
    JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -XX:MetaspaceSize=512M  -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -DtenantSpecificMongoConnectionsMaxSize=10 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/cloud5#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DactiveMQMaxConnections=5 -DAsyncLoggerConfig.ExceptionHandler=com.lmax.disruptor.IgnoreExceptionHandler -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/cloud5#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DactiveMQQueuePrefetch=1 -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.c1-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo5.c1-in.unicommerce.infra:27017,mongo6.c1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DactiveMQBrokerUrl=failover:tcp://activemq.c1-in.unicommerce.infra:61616,tcp://activemq.c1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -Ds3bucketSuffix=-in -DserverNames=Cloud5 -DserverName=Cloud5 -DactiveMQBrokerPassword=<path:kv/data/uniware/cloud5#activeMQBrokerPassword> -DquartzThreadPoolSize=500 -DexecuteJobs=true -DappIdentifier=app2 -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DactiveMQ2BrokerUrl=failover:tcp://activemq.c2-in.unicommerce.infra:61616,tcp://activemq.c2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/cloud5#activeMQBrokerPassword> -DclusterName=Cloud5 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xms2120M -Xmx4200M -Dhazelcast.local.publicAddress=cloud5-uniware-task-0.cloud5-uniware-task.prod.svc.cluster.local:5701 -DhazelcastMembers=cloud5-uniware-api-0.prod.svc.cluster.local:5701,cloud5-uniware-api-1.prod.svc.cluster.local:5701,cloud5-uniware-task-0.cloud5-uniware-task.prod.svc.cluster.local:5701 -DrecoSpecificCommonMongoConnectionsMaxSize=50"
  volumeMounts:
    - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
      subPath: vault.properties
      name: uniware-config
    - mountPath: /usr/local/tomcat/logs/vaultConfig
      name: uniware-vault-config
    - mountPath: /usr/local/tomcat/conf/context.xml
      subPath: context.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/conf/server.xml
      subPath: server.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/catalina.sh
      subPath: catalina.sh
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/hazelcast.yml
      subPath: hazelcast.yml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
      subPath: gc_monitor.sh
      name: uniware-config
  volumes:
    - name: uniware-config
      configMap:
        name: cloud5-uniware
        defaultMode: 0777
    - name: uniware-vault-config
      configMap:
        name: cloud5-vault-uniware
        defaultMode: 0777
  service:
    hazelCastport: 5701
    type: ClusterIP
    headless:
      enabled: true
  spec:
    consolidation:
      enabled: true
global:
  filebeat:
    config:
      name: ${PODNAME}.cloud5-in.unicommerce.infra
      hosts:
        - "logstash1-in.unicommerce.infra:5044"
        - "logstash2-in.unicommerce.infra:5044"
  promtail:
    config:
      clients:
        - url: http://loki-gateway.monitoring.svc.cluster.local:80/loki/api/v1/push
          tenant_id: 'cloud5'
  efs:
    enabled: false
