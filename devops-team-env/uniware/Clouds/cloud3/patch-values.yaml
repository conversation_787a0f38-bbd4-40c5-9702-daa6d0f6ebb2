enabled: true
replicaCount: 0
podSecurityContext:
  fsGroup: 1000
pdb:
  enabled: true
image:
  repository: nexus-docker.unicommerce.infra:8123/uniware
  pullPolicy: IfNotPresent
  tag: 1714.2-k8s
imagePullSecrets:
  - name: nexus-secrets
service:
  type: ClusterIP
  hazelCastport: 5701
  headless:
    enabled: true
arguments:
  - "sed \"/$HOSTNAME/d\" /usr/local/tomcat/logs/server.xml >  /usr/local/tomcat/conf/server.xml && catalina.sh run"
resources:
  limits:
    cpu: 1.5
    memory: 6.4Gi
  requests:
    cpu: 1.5
    memory: 6.4Gi
nodeSelector:
  karpenter.sh/nodepool: clouds-ondemand-pool
affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app.kubernetes.io/instance: cloud3
            app.kubernetes.io/name: uniware-api
        topologyKey: kubernetes.io/hostname
tolerations:
  - key: "clouds/ondemand"
    operator: "Exists"
    effect: "NoSchedule"
init:
  arguments:
    - mkdir -p /tmp/RotatedAppLogs
    - /tmp/files/customizedTemplates
    - /tmp/files/reconciliationInvoices
    - /tmp/files/saleOrderReturn
    - /tmp/files/import/logs
    - /tmp/files/saleOrder
    - /tmp/files/shippingLabel
livenessProbe: {}
readinessProbe:
  exec:
    command:
      - /bin/bash
      - -c
      - |-
        health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/live")
        if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  failureThreshold: 5
  periodSeconds: 60
  successThreshold: 1
  timeoutSeconds: 30
  initialDelaySeconds: 180
env:
  JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms512M -Xmx4500M -XX:MetaspaceSize=512M  -DhibernateEnversAudit=true -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -DtenantSpecificMongoConnectionsMaxSize=10 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/cloud3#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616  -DactiveMQMaxConnections=5 -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DAsyncLoggerConfig.ExceptionHandler=com.lmax.disruptor.IgnoreExceptionHandler -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/cloud3#activeMQBrokerPassword>  -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DactiveMQQueuePrefetch=1 -Ds3bucketSuffix=-in -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.c1-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo5.c1-in.unicommerce.infra:27017,mongo6.c1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DactiveMQBrokerUrl=failover:tcp://activemq.c1-in.unicommerce.infra:61616,tcp://activemq.c1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=Cloud3 -DserverName=Cloud3 -DactiveMQBrokerPassword=<path:kv/data/uniware/cloud3#activeMQBrokerPassword> -DquartzThreadPoolSize=500 -DexecuteJobs=false -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -DappIdentifier=${HOSTNAME} -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DactiveMQ2BrokerUrl=failover:tcp://activemq.c2-in.unicommerce.infra:61616,tcp://activemq.c2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/cloud3#activeMQBrokerPassword>  -DclusterName=Cloud3 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017  -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource -XX:+UseG1GC -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -XX:+UseStringDeduplication -XX:StringDeduplicationAgeThreshold=15 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Dhazelcast.local.publicAddress=${HOSTNAME}.prod.svc.cluster.local:5701 -DhazelcastMembers=cloud3-uniware-api-0.prod.svc.cluster.local:5701,cloud3-uniware-api-1.prod.svc.cluster.local:5701,cloud3-uniware-task-0.cloud3-uniware-task.prod.svc.cluster.local:5701"
volumeMounts:
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
    subPath: vault.properties
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/vaultConfig
    name: uniware-vault-config
  - mountPath: /usr/local/tomcat/conf/context.xml
    subPath: context.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/server.xml
    subPath: server.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/catalina.sh
    subPath: catalina.sh
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/hazelcast.yml
    subPath: hazelcast.yml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
    subPath: gc_monitor.sh
    name: uniware-config
volumes:
  - name: uniware-config
    configMap:
      name: cloud3-uniware
      defaultMode: 0777
  - name: uniware-vault-config
    configMap:
      name: cloud3-vault-uniware
      defaultMode: 0777
ingress:
  enabled: false
  className: "haproxy-public"
  annotations:
    haproxy-ingress.github.io/config-backend: |
      http-request set-header X-Forwarded-Proto https
  hosts:
    - host: abitibella.unicommerce.com
    - host: himalayanaesthetics.unicommerce.com
    - host: thakorjicreation1.unicommerce.com
    - host: thehouseofindu.unicommerce.com
    - host: cosisiaeasternlooms.unicommerce.com
    - host: leichieclothingco.unicommerce.com
    - host: kkcollection.unicommerce.com
    - host: silvertouch.unicommerce.com
    - host: celtic.unicommerce.com
    - host: bohonest.unicommerce.com
    - host: bhagwanienterprise.unicommerce.com
    - host: doubleone.unicommerce.com
    - host: trishlaknitwears.unicommerce.com
    - host: needleworkclothing.unicommerce.com
    - host: uniwaredevstore18.unicommerce.com
    - host: impulseinternationalprivatelimited.unicommerce.com
    - host: uniwaredevstore17.unicommerce.com
    - host: testt11.unicommerce.com
    - host: toshpunj.unicommerce.com
    - host: diyaapparel.unicommerce.com
    - host: krishnacreations.unicommerce.com
    - host: rpelectricals.unicommerce.com
    - host: svatifitnessandfurnishings.unicommerce.com
    - host: denvokcycles.unicommerce.com
    - host: nanaskarfabrics.unicommerce.com
    - host: koswaltextiles001.unicommerce.com
    - host: surindercycles.unicommerce.com
    - host: khizrajannatenterprises.unicommerce.com
    - host: hichasi.unicommerce.com
    - host: njfashionsurat.unicommerce.com
    - host: kreativeupisolutions.unicommerce.com
    - host: rudhvayindustriesprivatelimited.unicommerce.com
    - host: evolvesourcing.unicommerce.com
    - host: globalfashionhouse.unicommerce.com
    - host: khemchandhandicraftslimited.unicommerce.com
    - host: ssinsantradingcompany.unicommerce.com
    - host: nitzanabakers.unicommerce.com
    - host: testwms12.unicommerce.com
    - host: ezotechnologies.unicommerce.com
    - host: zestmelange.unicommerce.com
    - host: naturemaniaindiaprivatelimited.unicommerce.com
    - host: sakshioverseas.unicommerce.com
    - host: macmerise.unicommerce.com
    - host: avjewels.unicommerce.com
    - host: bigfive.unicommerce.com
    - host: bookmywish.unicommerce.com
    - host: canarylondonknp.unicommerce.com
    - host: chumbak.unicommerce.com
    - host: crosscreek.unicommerce.com
    - host: espresso.unicommerce.com
    - host: indianweddingsaree.unicommerce.com
    - host: jpearls.unicommerce.com
    - host: kawachigroup.unicommerce.com
    - host: meltingpot.unicommerce.com
    - host: nanglok.unicommerce.com
    - host: northmarketing.unicommerce.com
    - host: punk.unicommerce.com
    - host: rfashions.unicommerce.com
    - host: rkautoaccessories.unicommerce.com
    - host: trendybaubles.unicommerce.com
    - host: baseprofessional3.unicommerce.com
    - host: basestandard3.unicommerce.com
    - host: kalazone.unicommerce.com
    - host: jpearls-unireco.unicommerce.com
    - host: hardollenterprisesllp1.unicommerce.com
    - host: testsam.unicommerce.com
    - host: navkarapparels.unicommerce.com
    - host: kadritraders.unicommerce.com
    - host: hirparashraddhabennarshibhai.unicommerce.com
    - host: adithyaastores.unicommerce.com
    - host: testac.unicommerce.com
    - host: testac1.unicommerce.com
    - host: testac2.unicommerce.com
    - host: testac3.unicommerce.com
    - host: jmenterprises1.unicommerce.com
    - host: testac4.unicommerce.com
    - host: keshavcreations.unicommerce.com
    - host: darshuenterprise.unicommerce.com
    - host: chamberofgods.unicommerce.com
    - host: aariro.unicommerce.com
    - host: godchoiceorganicfarms.unicommerce.com
    - host: ambeenterprise.unicommerce.com
    - host: chavitraders.unicommerce.com
    - host: technoworld.unicommerce.com
    - host: technoworld1.unicommerce.com
    - host: crisscrossretailprivatelimited.unicommerce.com
    - host: kattagemsandjewellery.unicommerce.com
    - host: shrishakti.unicommerce.com
    - host: adequatesteel.unicommerce.com
  tls: []
istio:
  enabled: true
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 2
  targetCPUUtilizationPercentage: 75
uniware-task:
  enabled: true
  podSecurityContext:
    fsGroup: 1000
  resources:
    limits:
      cpu: 3.0
      memory: 15.1Gi
    requests:
      cpu: 3.0
      memory: 15.1Gi
  nodeSelector:
    karpenter.sh/nodepool: clouds-spot-pool
  tolerations:
    - key: "clouds/spot"
      operator: "Exists"
      effect: "NoSchedule"
  image:
    repository: nexus-docker.unicommerce.infra:8123/uniware
    pullPolicy: Always
    tag: 1714.2-k8s
  imagePullSecrets:
    - name: nexus-secrets
  init:
    arguments:
      - mkdir -p /tmp/RotatedAppLogs
      - /tmp/files/customizedTemplates
      - /tmp/files/reconciliationInvoices
      - /tmp/files/saleOrderReturn
      - /tmp/files/import/logs
      - /tmp/files/saleOrder
      - /tmp/files/shippingLabel
  livenessProbe: {}
  # exec:
  #   command:
  #   - /bin/bash
  #   - -c
  #   - |-
  #     health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/health")
  #     if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  # failureThreshold: 3
  # periodSeconds: 30
  # successThreshold: 1
  # timeoutSeconds: 10
  # initialDelaySeconds: 600
  readinessProbe: {}
  # exec:
  #   command:
  #   - /bin/bash
  #   - -c
  #   - |-
  #     health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/health")
  #     if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  # failureThreshold: 3
  # periodSeconds: 30
  # successThreshold: 1
  # timeoutSeconds: 10
  # initialDelaySeconds: 180
  env:
    JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -XX:MetaspaceSize=512M  -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -DtenantSpecificMongoConnectionsMaxSize=10 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/cloud3#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DactiveMQMaxConnections=50 -Ds3bucketSuffix=-in -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/cloud3#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DactiveMQQueuePrefetch=1 -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DAsyncLoggerConfig.ExceptionHandler=com.lmax.disruptor.IgnoreExceptionHandler -DzookeeperUrl=zookeeper.c1-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo5.c1-in.unicommerce.infra:27017,mongo6.c1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DactiveMQBrokerUrl=failover:tcp://activemq.c1-in.unicommerce.infra:61616,tcp://activemq.c1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DclusterName=Cloud3 -DserverNames=Cloud3 -DserverName=Cloud3 -DactiveMQBrokerPassword=<path:kv/data/uniware/cloud3#activeMQBrokerPassword> -DquartzThreadPoolSize=80 -DAsyncLogger.ThreadNameStrategy=UNCACHED -DappIdentifier=app2 -DexecuteJobs=true -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DactiveMQ2BrokerUrl=failover:tcp://activemq.c2-in.unicommerce.infra:61616,tcp://activemq.c2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/cloud3#activeMQBrokerPassword> -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -XX:+UseG1GC -XX:+UseStringDeduplication -XX:StringDeduplicationAgeThreshold=15 -DdataSource=UniwareHikariDataSource -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Dhazelcast.local.publicAddress=${HOSTNAME}.cloud3-uniware-task.prod.svc.cluster.local:5701 -DhazelcastMembers=cloud3-uniware-api-0.prod.svc.cluster.local:5701,cloud3-uniware-api-1.prod.svc.cluster.local:5701,cloud3-uniware-task-0.cloud3-uniware-task.prod.svc.cluster.local:5701 -Xms7200M -Xmx12500M -DrecoSpecificCommonMongoConnectionsMaxSize=50"
  volumeMounts:
    - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
      subPath: vault.properties
      name: uniware-config
    - mountPath: /usr/local/tomcat/logs/vaultConfig
      name: uniware-vault-config
    - mountPath: /usr/local/tomcat/conf/context.xml
      subPath: context.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/conf/server.xml
      subPath: server.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/catalina.sh
      subPath: catalina.sh
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/hazelcast.yml
      subPath: hazelcast.yml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
      subPath: gc_monitor.sh
      name: uniware-config
  volumes:
    - name: uniware-config
      configMap:
        name: cloud3-uniware
        defaultMode: 0777
    - name: uniware-vault-config
      configMap:
        name: cloud3-vault-uniware
        defaultMode: 0777
  service:
    hazelCastport: 5701
    type: ClusterIP
    headless:
      enabled: true
  spec:
    consolidation:
      enabled: true
global:
  filebeat:
    config:
      name: ${PODNAME}.cloud3-in.unicommerce.infra
      hosts:
        - "logstash1-in.unicommerce.infra:5044"
        - "logstash2-in.unicommerce.infra:5044"
  promtail:
    config:
      clients:
        - url: http://loki-gateway.monitoring.svc.cluster.local:80/loki/api/v1/push
          tenant_id: 'cloud3'
  efs:
    enabled: false
