enabled: true
replicaCount: 0
podSecurityContext:
  fsGroup: 1000
pdb:
  enabled: true
image:
  repository: nexus-docker.unicommerce.infra:8123/uniware
  pullPolicy: Always
  tag: 1714.2-k8s
imagePullSecrets:
  - name: nexus-secrets
service:
  type: ClusterIP
  hazelCastport: 5701
  headless:
    enabled: true
arguments:
  - "sed \"/$HOSTNAME/d\" /usr/local/tomcat/logs/server.xml >  /usr/local/tomcat/conf/server.xml && catalina.sh run"
resources:
  limits:
    cpu: 1.3
    memory: 6.4Gi
  requests:
    cpu: 1.3
    memory: 6.4Gi
nodeSelector:
  karpenter.sh/nodepool: clouds-ondemand-pool
affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app.kubernetes.io/instance: cloud8
            app.kubernetes.io/name: uniware-api
        topologyKey: kubernetes.io/hostname
tolerations:
  - key: "clouds/ondemand"
    operator: "Exists"
    effect: "NoSchedule"
init:
  arguments:
    - mkdir -p /tmp/RotatedAppLogs
    - /tmp/files/customizedTemplates
    - /tmp/files/reconciliationInvoices
    - /tmp/files/saleOrderReturn
    - /tmp/files/import/logs
    - /tmp/files/saleOrder
    - /tmp/files/shippingLabel
livenessProbe: {}
readinessProbe:
  exec:
    command:
      - /bin/bash
      - -c
      - |-
        health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/live")
        if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  failureThreshold: 5
  periodSeconds: 60
  successThreshold: 1
  timeoutSeconds: 30
  initialDelaySeconds: 180
env:
  JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms512M -Xmx4500M -XX:MaxPermSize=512M -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -DtenantSpecificMongoConnectionsMaxSize=10 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/cloud8#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616  -DactiveMQMaxConnections=5 -DserverName=Cloud8 -Ds3bucketSuffix=-in -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DAsyncLoggerConfig.ExceptionHandler=com.lmax.disruptor.IgnoreExceptionHandler -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/cloud8#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616  -DactiveMQQueuePrefetch=1 -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.c1-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo5.c1-in.unicommerce.infra:27017,mongo6.c1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DactiveMQBrokerUrl=failover:tcp://activemq.c1-in.unicommerce.infra:61616,tcp://activemq.c1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=Cloud8 -DactiveMQBrokerPassword=<path:kv/data/uniware/cloud8#activeMQBrokerPassword> -DquartzThreadPoolSize=500 -DexecuteJobs=false -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr  -DquartzThreadPoolSize=10 -DappIdentifier=${HOSTNAME} -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DactiveMQ2BrokerUrl=failover:tcp://activemq.c2-in.unicommerce.infra:61616,tcp://activemq.c2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/cloud8#activeMQBrokerPassword> -DclusterName=Cloud8 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource -XX:+UseG1GC -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -XX:+UseStringDeduplication -XX:StringDeduplicationAgeThreshold=15 -DinventoryDebugLoggingEnabled=true -DlogHibernateMemoryUsage=false -DhibernateGenerateStatistics=false -javaagent:/usr/local/tomcat/conf/uniwareConfig/pinpoint-agent-2.5.2/pinpoint-bootstrap-2.5.2.jar -Dpinpoint.agentId=${HOSTNAME} -Dpinpoint.applicationName=Cloud8 -Dhazelcast.local.publicAddress=${HOSTNAME}.prod.svc.cluster.local:5701 -DhazelcastMembers=cloud8-uniware-api-0.prod.svc.cluster.local:5701,cloud8-uniware-api-1.prod.svc.cluster.local:5701,cloud8-uniware-task-0.cloud8-uniware-task.prod.svc.cluster.local:5701"
volumeMounts:
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
    subPath: vault.properties
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/vaultConfig
    name: uniware-vault-config
  - mountPath: /usr/local/tomcat/conf/context.xml
    subPath: context.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/server.xml
    subPath: server.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/catalina.sh
    subPath: catalina.sh
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/hazelcast.yml
    subPath: hazelcast.yml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
    subPath: gc_monitor.sh
    name: uniware-config
volumes:
  - name: uniware-config
    configMap:
      name: cloud8-uniware
      defaultMode: 0777
  - name: uniware-vault-config
    configMap:
      name: cloud8-vault-uniware
      defaultMode: 0777
ingress:
  enabled: false
  className: "haproxy-public"
  annotations:
    haproxy-ingress.github.io/config-backend: |
      http-request set-header X-Forwarded-Proto https
  hosts:
    - host: aaliyacorp.unicommerce.com
    - host: bernina.unicommerce.com
    - host: beyou.unicommerce.com
    - host: bigtree.unicommerce.com
    - host: eavanfashion.unicommerce.com
    - host: ecustomerfirst.unicommerce.com
    - host: elcincoinc.unicommerce.com
    - host: fairdealtraders.unicommerce.com
    - host: inoxjewelry.unicommerce.com
    - host: libertina.unicommerce.com
    - host: pipabella.unicommerce.com
    - host: richlookindia.unicommerce.com
    - host: saenterprises.unicommerce.com
    - host: secretwish.unicommerce.com
    - host: snp.unicommerce.com
    - host: sstrading.unicommerce.com
    - host: thestiffcollar.unicommerce.com
    - host: varnifabrics.unicommerce.com
    - host: varshatraders.unicommerce.com
    - host: baseprofessional8.unicommerce.com
    - host: basestandard8.unicommerce.com
    - host: astrock.unicommerce.com
    - host: elcincoinc-unireco.unicommerce.com
  tls: []
istio:
  enabled: true
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 2
  targetCPUUtilizationPercentage: 75
uniware-task:
  enabled: true
  podSecurityContext:
    fsGroup: 1000
  resources:
    limits:
      cpu: 2.5
      memory: 14.1Gi
    requests:
      cpu: 2.5
      memory: 14.1Gi
  nodeSelector:
    karpenter.sh/nodepool: clouds-spot-pool
  tolerations:
    - key: "clouds/spot"
      operator: "Exists"
      effect: "NoSchedule"
  image:
    repository: nexus-docker.unicommerce.infra:8123/uniware
    pullPolicy: Always
    tag: 1714.2-k8s
  imagePullSecrets:
    - name: nexus-secrets
  init:
    arguments:
      - mkdir -p /tmp/RotatedAppLogs
      - /tmp/files/customizedTemplates
      - /tmp/files/reconciliationInvoices
      - /tmp/files/saleOrderReturn
      - /tmp/files/import/logs
      - /tmp/files/saleOrder
      - /tmp/files/shippingLabel
  livenessProbe: {}
  # exec:
  #   command:
  #   - /bin/bash
  #   - -c
  #   - |-
  #     health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/health")
  #     if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  # failureThreshold: 3
  # periodSeconds: 30
  # successThreshold: 1
  # timeoutSeconds: 10
  # initialDelaySeconds: 600
  readinessProbe: {}
  # exec:
  #   command:
  #   - /bin/bash
  #   - -c
  #   - |-
  #     health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/health")
  #     if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  # failureThreshold: 3
  # periodSeconds: 30
  # successThreshold: 1
  # timeoutSeconds: 10
  # initialDelaySeconds: 180
  env:
    JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -XX:MaxPermSize=512M -Ds3bucketSuffix=-in -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -DtenantSpecificMongoConnectionsMaxSize=10 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/cloud8#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DactiveMQMaxConnections=50 -DserverName=Cloud8 -Ds3bucketSuffix=-in -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/cloud8#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DactiveMQQueuePrefetch=1 -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DAsyncLoggerConfig.ExceptionHandler=com.lmax.disruptor.IgnoreExceptionHandler -DzookeeperUrl=zookeeper.c1-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo5.c1-in.unicommerce.infra:27017,mongo6.c1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DactiveMQBrokerUrl=failover:tcp://activemq.c1-in.unicommerce.infra:61616,tcp://activemq.c1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=Cloud8 -DactiveMQBrokerPassword=<path:kv/data/uniware/cloud8#activeMQBrokerPassword> -DquartzThreadPoolSize=50 -DAsyncLogger.ThreadNameStrategy=UNCACHED -DappIdentifier=app2 -DexecuteJobs=true -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DactiveMQ2BrokerUrl=failover:tcp://activemq.c2-in.unicommerce.infra:61616,tcp://activemq.c2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/cloud8#activeMQBrokerPassword> -DclusterName=Cloud8 -DlogHibernateMemoryUsage=false -DhibernateGenerateStatistics=false -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -XX:+UseG1GC -XX:+UseStringDeduplication -XX:StringDeduplicationAgeThreshold=15 -DdataSource=UniwareHikariDataSource -DinventoryDebugLoggingEnabled=true -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Dhazelcast.local.publicAddress=${HOSTNAME}.cloud8-uniware-task.prod.svc.cluster.local:5701 -DhazelcastMembers=cloud8-uniware-api-0.prod.svc.cluster.local:5701,cloud8-uniware-api-1.prod.svc.cluster.local:5701,cloud8-uniware-task-0.cloud8-uniware-task.prod.svc.cluster.local:5701 -Xms7120M -Xmx11500M -DrecoSpecificCommonMongoConnectionsMaxSize=50"
  volumeMounts:
    - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
      subPath: vault.properties
      name: uniware-config
    - mountPath: /usr/local/tomcat/logs/vaultConfig
      name: uniware-vault-config
    - mountPath: /usr/local/tomcat/conf/context.xml
      subPath: context.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/conf/server.xml
      subPath: server.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/catalina.sh
      subPath: catalina.sh
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/hazelcast.yml
      subPath: hazelcast.yml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
      subPath: gc_monitor.sh
      name: uniware-config
  volumes:
    - name: uniware-config
      configMap:
        name: cloud8-uniware
        defaultMode: 0777
    - name: uniware-vault-config
      configMap:
        name: cloud8-vault-uniware
        defaultMode: 0777
  service:
    hazelCastport: 5701
    type: ClusterIP
    headless:
      enabled: true
  spec:
    consolidation:
      enabled: true
global:
  filebeat:
    config:
      name: ${PODNAME}.cloud8-in.unicommerce.infra
      hosts:
        - "logstash1-in.unicommerce.infra:5044"
        - "logstash2-in.unicommerce.infra:5044"
  promtail:
    config:
      clients:
        - url: http://loki-gateway.monitoring.svc.cluster.local:80/loki/api/v1/push
          tenant_id: 'cloud8'
  efs:
    enabled: false
