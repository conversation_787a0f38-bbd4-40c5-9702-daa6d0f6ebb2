enabled: true
replicaCount: 0
podSecurityContext:
  fsGroup: 1000
pdb:
  enabled: true
  minAvailable: 3
image:
  repository: nexus-docker.unicommerce.infra:8123/uniware
  pullPolicy: Always
  tag: 1714.2-k8s
imagePullSecrets:
  - name: nexus-secrets
service:
  type: ClusterIP
  hazelCastport: 5701
  headless:
    enabled: true
arguments:
  - "sed \"/$HOSTNAME/d\" /usr/local/tomcat/logs/server.xml >  /usr/local/tomcat/conf/server.xml && catalina.sh run"
resources:
  limits:
    cpu: 6
    memory: 16Gi
  requests:
    cpu: 6
    memory: 16Gi
nodeSelector:
  karpenter.sh/nodepool: eclouds-ondemand-pool
affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app.kubernetes.io/instance: ecloud3
            app.kubernetes.io/name: uniware-api
        topologyKey: kubernetes.io/hostname
tolerations:
  - key: "eclouds/ondemand"
    operator: "Exists"
    effect: "NoSchedule"
init:
  arguments:
    - mkdir -p /tmp/RotatedAppLogs
    - /tmp/files/customizedTemplates
    - /tmp/files/reconciliationInvoices
    - /tmp/files/saleOrderReturn
    - /tmp/files/import/logs
    - /tmp/files/saleOrder
    - /tmp/files/shippingLabel
livenessProbe: {}
readinessProbe:
  exec:
    command:
      - /bin/bash
      - -c
      - |-
        health_status=$(curl --write-out "%{http_code}\n" --max-time 3 -H "Host: baseenterprise3.unicommerce.com" --silent --output /dev/null "http://localhost:8080/open/live")
        if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  failureThreshold: 5
  periodSeconds: 60
  successThreshold: 1
  timeoutSeconds: 30
  initialDelaySeconds: 180
env:
  JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms6000M -Xmx12000M -XX:MetaspaceSize=512M -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -DtenantSpecificMongoConnectionsMaxSize=150 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud3#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616  -DactiveMQMaxConnections=50 -DserverName=ECloud3 -Ds3bucketSuffix=-in -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/ecloud3#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DactiveMQQueuePrefetch=1 -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DAsyncLoggerConfig.ExceptionHandler=com.lmax.disruptor.IgnoreExceptionHandler -DzookeeperUrl=zookeeper.ecloud3-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo1.ecloud1-in.unicommerce.infra:27017,mongo2.ecloud1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DactiveMQBrokerUrl=failover:tcp://activemq.ec1-in.unicommerce.infra:61616,tcp://activemq.ec1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=ECloud3 -DactiveMQBrokerPassword=<path:kv/data/uniware/ecloud3#activeMQBrokerPassword> -DquartzThreadPoolSize=50 -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr  -DappIdentifier=${HOSTNAME} -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DactiveMQ2BrokerUrl=failover:tcp://activemq.ec2-in.unicommerce.infra:61616,tcp://activemq.ec2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud3#activeMQBrokerPassword> -DexecuteJobs=false -DclusterName=ECloud3 -DinventoryDebugLoggingEnabled=true -DlogHibernateMemoryUsage=false -DhibernateGenerateStatistics=false -javaagent:/usr/local/tomcat/conf/uniwareConfig/pinpoint-agent-2.5.2/pinpoint-bootstrap-2.5.2.jar -Dpinpoint.agentId=EC3 -Dpinpoint.applicationName=ECloud3 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d)  -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching  -DdataSource=UniwareHikariDataSource -XX:+UseG1GC -XX:+UseStringDeduplication -XX:StringDeduplicationAgeThreshold=15 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Dhazelcast.local.publicAddress=${HOSTNAME}.prod.svc.cluster.local:5701 -DhazelcastMembers=ecloud3-uniware-api-0.prod.svc.cluster.local:5701,ecloud3-uniware-api-1.prod.svc.cluster.local:5701,ecloud3-uniware-api-2.prod.svc.cluster.local:5701,ecloud3-uniware-api-3.prod.svc.cluster.local:5701,ecloud3-uniware-task-0.ecloud3-uniware-task.prod.svc.cluster.local:5701"
volumeMounts:
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
    subPath: vault.properties
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/vaultConfig
    name: uniware-vault-config
  - mountPath: /usr/local/tomcat/conf/context.xml
    subPath: context.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/server.xml
    subPath: server.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/catalina.sh
    subPath: catalina.sh
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/hazelcast.yml
    subPath: hazelcast.yml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
    subPath: gc_monitor.sh
    name: uniware-config
volumes:
  - name: uniware-config
    configMap:
      name: ecloud3-uniware
      defaultMode: 0777
  - name: uniware-vault-config
    configMap:
      name: ecloud3-vault-uniware
      defaultMode: 0777
volumeClaimTemplates:
  - name: logs
    accessModes: 
      - ReadWriteOnce
    resources:
      requests:
        storage: 20Gi
    storageClassName: gp3
ingress:
  enabled: false
  className: "haproxy-public"
  annotations:
    haproxy-ingress.github.io/config-backend: |
      http-request set-header X-Forwarded-Proto https
  hosts:
    - host: 4mclothing.unicommerce.com
    - host: handfulofhealth.unicommerce.com
    - host: routetomarket.unicommerce.com
    - host: mattlook.unicommerce.com
    - host: peppyforall.unicommerce.com
    - host: sarascreations.unicommerce.com
    - host: tryecloud91.unicommerce.com
    - host: actiongroup.unicommerce.com
    - host: aeronutrix.unicommerce.com
    - host: allen.unicommerce.com
    - host: alpino.unicommerce.com
    - host: amrutam.unicommerce.com
    - host: arata.unicommerce.com
    - host: articles.unicommerce.com
    - host: arune3test.unicommerce.com
    - host: astrotalk.unicommerce.com
    - host: athena.unicommerce.com
    - host: atmosphere.unicommerce.com
    - host: baglineindia.unicommerce.com
    - host: baglineindia-unireco.unicommerce.com
    - host: bandidos.unicommerce.com
    - host: bansifashion.unicommerce.com
    - host: baseenterprise3.unicommerce.com
    - host: bataindialtd.unicommerce.com
    - host: beato.unicommerce.com
    - host: beautypalace.unicommerce.com
    - host: bellavitaorganic.unicommerce.com
    - host: belora.unicommerce.com
    - host: benchmark.unicommerce.com
    - host: bibosaretails.unicommerce.com
    - host: bigkarthomeware.unicommerce.com
    - host: bizztm.unicommerce.com
    - host: bryanandcandy.unicommerce.com
    - host: carltonretail.unicommerce.com
    - host: carrydreams.unicommerce.com
    - host: charactercosmetics.unicommerce.com
    - host: cittabeauty.unicommerce.com
    - host: citykart.unicommerce.com
    - host: clai.unicommerce.com
    - host: cliftonexport.unicommerce.com
    - host: corahealthcare.unicommerce.com
    - host: cosmic.unicommerce.com
    - host: cozyworld.unicommerce.com
    - host: deyann.unicommerce.com
    - host: dmpl.unicommerce.com
    - host: docare.unicommerce.com
    - host: eatopia.unicommerce.com
    - host: efashion.unicommerce.com
    - host: emount.unicommerce.com
    - host: envent.unicommerce.com
    - host: ethnovogue.unicommerce.com
    - host: ezmallonline.unicommerce.com
    - host: fdclub.unicommerce.com
    - host: feier.unicommerce.com
    - host: firstcareindia.unicommerce.com
    - host: futureforward.unicommerce.com
    - host: ghc.unicommerce.com
    - host: goldfishh.unicommerce.com
    - host: grasperglobal.unicommerce.com
    - host: growsimplee.unicommerce.com
    - host: gynoveda.unicommerce.com
    - host: habbit.unicommerce.com
    - host: harparetails.unicommerce.com
    - host: harpa.unicommerce.com
    - host: helioslifestyle.unicommerce.com
    - host: hoteon.unicommerce.com
    - host: huetrap.unicommerce.com
    - host: ifc.unicommerce.com
    - host: indiejewelfashion.unicommerce.com
    - host: inkkr.unicommerce.com
    - host: intergrow.unicommerce.com
    - host: intimi.unicommerce.com
    - host: jalsupplychain.unicommerce.com
    - host: jkspices.unicommerce.com
    - host: johars.unicommerce.com
    - host: justdogs.unicommerce.com
    - host: kalamandir.unicommerce.com
    - host: keshvifashionllp.unicommerce.com
    - host: kompanero.unicommerce.com
    - host: kushals.unicommerce.com
    - host: lagom.unicommerce.com
    - host: lahejafashion.unicommerce.com
    - host: lincpen.unicommerce.com
    - host: makerbazar.unicommerce.com
    - host: mariconew.unicommerce.com
    - host: meenabazaar.unicommerce.com
    - host: mithilaemiza.unicommerce.com
    - host: mokobarastaging.unicommerce.com
    - host: mokobara.unicommerce.com
    - host: mrbuttonpvtltd.unicommerce.com
    - host: msr.unicommerce.com
    - host: namhah.unicommerce.com
    - host: nandu.unicommerce.com
    - host: nathabit.unicommerce.com
    - host: neerus.unicommerce.com
    - host: nehacraft.unicommerce.com
    - host: nextgapex.unicommerce.com
    - host: nzeel.unicommerce.com
    - host: obsessions.unicommerce.com
    - host: ogaan.unicommerce.com
    - host: omvedtherapies.unicommerce.com
    - host: ossility.unicommerce.com
    - host: parfait.unicommerce.com
    - host: paversengland.unicommerce.com
    - host: phool.unicommerce.com
    - host: plantex.unicommerce.com
    - host: plix.unicommerce.com
    - host: powergummies.unicommerce.com
    - host: prayosha.unicommerce.com
    - host: prozodistribution.unicommerce.com
    - host: prozo.unicommerce.com
    - host: ramkrishnabasak.unicommerce.com
    - host: reshamandi.unicommerce.com
    - host: rforrabbitbaby.unicommerce.com
    - host: rjcorp.unicommerce.com
    - host: rodretail.unicommerce.com
    - host: saadgionline.unicommerce.com
    - host: satrani.unicommerce.com
    - host: scspl.unicommerce.com
    - host: sevenrocksinternational.unicommerce.com
    - host: shakedeal.unicommerce.com
    - host: shibumifashion.unicommerce.com
    - host: silkzone.unicommerce.com
    - host: skillmatics.unicommerce.com
    - host: smartees2.unicommerce.com
    - host: stagemami.unicommerce.com
    - host: stgarticles.unicommerce.com
    - host: stgboatlifestyle.unicommerce.com
    - host: stgemami.unicommerce.com
    - host: stghuetrap.unicommerce.com
    - host: stgkalamandir.unicommerce.com
    - host: stgmamaearth.unicommerce.com
    - host: stgmensabrands.unicommerce.com
    - host: stgplumgoodness.unicommerce.com
    - host: stgpmcg.unicommerce.com
    - host: stori.unicommerce.com
    - host: sujika.unicommerce.com
    - host: sukkhionline.unicommerce.com
    - host: summersalt.unicommerce.com
    - host: sunlighthyd.unicommerce.com
    - host: suta.unicommerce.com
    - host: suta-unireco.unicommerce.com
    - host: suta.unireco.unicommerce.com
    - host: tcl.unicommerce.com
    - host: testscs.unicommerce.com
    - host: thepillowcompany.unicommerce.com
    - host: toonyport.unicommerce.com
    - host: tresmode25.unicommerce.com
    - host: tresmode25-unireco.unicommerce.com
    - host: triumph.unicommerce.com
    - host: ultron.unicommerce.com
    - host: unionventures.unicommerce.com
    - host: uppercircuittrading.unicommerce.com
    - host: urbanmonkey.unicommerce.com
    - host: vanaura.unicommerce.com
    - host: vasustore.unicommerce.com
    - host: vasu.unicommerce.com
    - host: ventureinnovations.unicommerce.com
    - host: ventureinnovations-unireco.unicommerce.com
    - host: vff.unicommerce.com
    - host: vff-unireco.unicommerce.com
    - host: vguard.unicommerce.com
    - host: walkaroo.unicommerce.com
    - host: wbn.unicommerce.com
    - host: williampenn1.unicommerce.com
    - host: williampenn.unicommerce.com
    - host: wonder.unicommerce.com
    - host: wunderkind.unicommerce.com
    - host: zeyo.unicommerce.com
    - host: zoiro.unicommerce.com
  tls: []

istio:
  enabled: true

autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 4
  targetCPUUtilizationPercentage: 75
uniware-task:
  enabled: true
  podSecurityContext:
    fsGroup: 1000
  resources:
    limits:
      cpu: 12
      memory: 45Gi
    requests:
      cpu: 12
      memory: 45Gi
  nodeSelector:
    karpenter.sh/nodepool: eclouds-spot-pool
  tolerations:
    - key: "eclouds/spot"
      operator: "Exists"
      effect: "NoSchedule"
  image:
    repository: nexus-docker.unicommerce.infra:8123/uniware
    pullPolicy: Always
    tag: 1714.2-k8s
  imagePullSecrets:
    - name: nexus-secrets
  init:
    arguments:
      - mkdir -p /tmp/RotatedAppLogs
      - /tmp/files/customizedTemplates
      - /tmp/files/reconciliationInvoices
      - /tmp/files/saleOrderReturn
      - /tmp/files/import/logs
      - /tmp/files/saleOrder
      - /tmp/files/shippingLabel
  livenessProbe: {}
  readinessProbe: {}
  env:
    JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms16250M -Xmx32500M -XX:MetaspaceSize=512M -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -DcommonMongoConnectionsMaxSize=1000 -DtenantSpecificMongoConnectionsMaxSize=300 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud3#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DappIdentifier=app2 -DserverName=ECloud3 -Ds3bucketSuffix=-in -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.ecloud3-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo1.ecloud1-in.unicommerce.infra:27017,mongo2.ecloud1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/ecloud3#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DclusterName=ECloud3 -DactiveMQBrokerUrl=failover:tcp://activemq.ec1-in.unicommerce.infra:61616,tcp://activemq.ec1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=ECloud3 -DactiveMQBrokerPassword=<path:kv/data/uniware/ecloud3#activeMQBrokerPassword> -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -Dcom.sun.management.jmxremote -DAsyncLogger.ThreadNameStrategy=UNCACHED -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -DactiveMQMaxConnections=5 -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 -DactiveMQ2BrokerUrl=failover:tcp://activemq.ec2-in.unicommerce.infra:61616,tcp://activemq.ec2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud3#activeMQBrokerPassword> -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DexecuteJobs=true -javaagent:/usr/local/tomcat/conf/uniwareConfig/pinpoint-agent-2.5.2/pinpoint-bootstrap-2.5.2.jar -Dpinpoint.agentId=4 -Dpinpoint.applicationName=ECloud3_2 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource -DlogHibernateMemoryUsage=false -DhibernateGenerateStatistics=false -DrecoSpecificCommonMongoConnectionsMaxSize=50 -Dhazelcast.local.publicAddress=ecloud3-uniware-task-0.ecloud3-uniware-task.prod.svc.cluster.local:5701 -DhazelcastMembers=ecloud3-uniware-api-0.prod.svc.cluster.local:5701,ecloud3-uniware-api-1.prod.svc.cluster.local:5701,ecloud3-uniware-api-2.prod.svc.cluster.local:5701,ecloud3-uniware-api-3.prod.svc.cluster.local:5701,ecloud3-uniware-task-0.ecloud3-uniware-task.prod.svc.cluster.local:5701"
  volumeMounts:
    - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
      subPath: vault.properties
      name: uniware-config
    - mountPath: /usr/local/tomcat/logs/vaultConfig
      name: uniware-vault-config
    - mountPath: /usr/local/tomcat/conf/context.xml
      subPath: context.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/conf/server.xml
      subPath: server.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/catalina.sh
      subPath: catalina.sh
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/hazelcast.yml
      subPath: hazelcast.yml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
      subPath: gc_monitor.sh
      name: uniware-config
  volumes:
    - name: uniware-config
      configMap:
        name: ecloud3-uniware
        defaultMode: 0777
    - name: uniware-vault-config
      configMap:
        name: ecloud3-vault-uniware
        defaultMode: 0777
  service:
    hazelCastport: 5701
    type: ClusterIP
    headless:
      enabled: true
global:
  filebeat:
    config:
      name: ${PODNAME}.ecloud3-in.unicommerce.infra
      hosts:
        - "logstash1-in.unicommerce.infra:5044"
        - "logstash2-in.unicommerce.infra:5044"
  promtail:
    config:
      clients:
        - url: http://loki-gateway.monitoring.svc.cluster.local:80/loki/api/v1/push
          tenant_id: 'ecloud3'
  efs:
    enabled: false
