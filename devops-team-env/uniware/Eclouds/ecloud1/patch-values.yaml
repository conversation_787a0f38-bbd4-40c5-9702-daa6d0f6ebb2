enabled: true
replicaCount: 0
podSecurityContext:
  fsGroup: 1000
pdb:
  enabled: true
  minAvailable: 3
image:
  repository: nexus-docker.unicommerce.infra:8123/uniware
  pullPolicy: Always
  tag: 1712.9-k8s
imagePullSecrets:
  - name: nexus-secrets
service:
  type: ClusterIP
  hazelCastport: 5701
  headless:
    enabled: true
arguments:
  - "sed \"/$HOSTNAME/d\" /usr/local/tomcat/logs/server.xml >  /usr/local/tomcat/conf/server.xml && catalina.sh run"
resources:
  limits:
    cpu: 5
    memory: 50Gi
  requests:
    cpu: 5
    memory: 50Gi
nodeSelector:
  karpenter.sh/nodepool: eclouds-ondemand-pool
affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app.kubernetes.io/instance: ecloud1
            app.kubernetes.io/name: uniware-api
        topologyKey: kubernetes.io/hostname
tolerations:
  - key: "eclouds/ondemand"
    operator: "Exists"
    effect: "NoSchedule"
init:
  arguments:
    - mkdir -p /tmp/RotatedAppLogs
    - /tmp/files/customizedTemplates
    - /tmp/files/reconciliationInvoices
    - /tmp/files/saleOrderReturn
    - /tmp/files/import/logs
    - /tmp/files/saleOrder
    - /tmp/files/shippingLabel
livenessProbe: {}
readinessProbe:
  exec:
    command:
      - /bin/bash
      - -c
      - |-
        health_status=$(curl --head --write-out "%{http_code}\n" --max-time 3 --silent --output /dev/null "http://localhost:8080/open/live")
        if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  failureThreshold: 5
  periodSeconds: 60
  successThreshold: 1
  timeoutSeconds: 30
  initialDelaySeconds: 180
env:
  JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms23000M -Xmx45000M -XX:MetaspaceSize=512M -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -DtenantSpecificMongoConnectionsMaxSize=150 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud1#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DactiveMQMaxConnections=50 -DserverName=ECloud1 -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/ecloud1#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -Ds3bucketSuffix=-in -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DactiveMQQueuePrefetch=1 -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DAsyncLoggerConfig.ExceptionHandler=com.lmax.disruptor.IgnoreExceptionHandler -DzookeeperUrl=zookeeper.ecloud1-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo1.ecloud1-in.unicommerce.infra:27017,mongo2.ecloud1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DactiveMQBrokerUrl=failover:tcp://activemq.ec1-in.unicommerce.infra:61616,tcp://activemq.ec1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=ECloud1 -DactiveMQBrokerPassword=<path:kv/data/uniware/ecloud1#activeMQBrokerPassword> -DquartzThreadPoolSize=50 -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr  -DappIdentifier=${HOSTNAME} -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017  -DactiveMQ2BrokerUrl=failover:tcp://activemq.ec2-in.unicommerce.infra:61616,tcp://activemq.ec2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud1#activeMQBrokerPassword> -DexecuteJobs=false -DclusterName=ECloud1 -DlogHibernateMemoryUsage=false -DhibernateGenerateStatistics=false -javaagent:/usr/local/tomcat/conf/uniwareConfig/pinpoint-agent-2.5.2/pinpoint-bootstrap-2.5.2.jar -Dpinpoint.agentId=EC1 -Dpinpoint.applicationName=ECloud1 -DhibernateEnversAudit=true -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d)  -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching  -DdataSource=UniwareHikariDataSource -XX:+UseG1GC -XX:+UseStringDeduplication -XX:StringDeduplicationAgeThreshold=15 -DinventoryDebugLoggingEnabled=false -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Dhazelcast.local.publicAddress=${HOSTNAME}.prod.svc.cluster.local:5701 -DhazelcastMembers=ecloud1-uniware-api-0.prod.svc.cluster.local:5701,ecloud1-uniware-api-1.prod.svc.cluster.local:5701,ecloud1-uniware-api-2.prod.svc.cluster.local:5701,ecloud1-uniware-api-3.prod.svc.cluster.local:5701,ecloud1-uniware-task-0.ecloud1-uniware-task.prod.svc.cluster.local:5701"
volumeMounts:
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
    subPath: vault.properties
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/vaultConfig
    name: uniware-vault-config
  - mountPath: /usr/local/tomcat/conf/context.xml
    subPath: context.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/server.xml
    subPath: server.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/catalina.sh
    subPath: catalina.sh
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/hazelcast.yml
    subPath: hazelcast.yml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
    subPath: gc_monitor.sh
    name: uniware-config
volumes:
  - name: uniware-config
    configMap:
      name: ecloud1-uniware
      defaultMode: 0777
  - name: uniware-vault-config
    configMap:
      name: ecloud1-vault-uniware
      defaultMode: 0777
ingress:
  enabled: true
  className: "haproxy-public"
  annotations:
    haproxy-ingress.github.io/config-backend: |
      http-request set-header X-Forwarded-Proto https
  hosts:
    - host: aadretail.unicommerce.com
    - host: achint.unicommerce.com
    - host: aei.unicommerce.com
    - host: ahc.unicommerce.com
    - host: alcis.unicommerce.com
    - host: ankurpratik.unicommerce.com
    - host: ankurp.unicommerce.com
    - host: asg.unicommerce.com
    - host: baseenterprise1.unicommerce.com
    - host: bil.unicommerce.com
    - host: blackscissors.unicommerce.com
    - host: bls.unicommerce.com
    - host: bluesaint.unicommerce.com
    - host: boatlifestyle.unicommerce.com
    - host: breya.unicommerce.com
    - host: brihat.unicommerce.com
    - host: buyblynk.unicommerce.com
    - host: cavinkare.unicommerce.com
    - host: chkokko.unicommerce.com
    - host: clinikally.unicommerce.com
    - host: csrpl.unicommerce.com
    - host: cvmenterprises.unicommerce.com
    - host: daaginey.unicommerce.com
    - host: damenschapparel.unicommerce.com
    - host: deoleoemiza.unicommerce.com
    - host: dollarindustries.unicommerce.com
    - host: dotandkey.unicommerce.com
    - host: ecloud1.unicommerce.com
    - host: edpl.unicommerce.com
    - host: elite.unicommerce.com
    - host: enamorecom.unicommerce.com
    - host: enamorecom-unireco.unicommerce.com
    - host: fabcraftint.unicommerce.com
    - host: fashionistaindia555.unicommerce.com
    - host: fashionista.unicommerce.com
    - host: ficosa.unicommerce.com
    - host: fipl.unicommerce.com
    - host: flomattresses.unicommerce.com
    - host: floraware.unicommerce.com
    - host: futurewagon1.unicommerce.com
    - host: gaiia.unicommerce.com
    - host: gaurik.unicommerce.com
    - host: gini.unicommerce.com
    - host: gocolors.unicommerce.com
    - host: goflipside.unicommerce.com
    - host: golisoda.unicommerce.com
    - host: gtne.unicommerce.com
    - host: hannuknitters.unicommerce.com
    - host: happilo.unicommerce.com
    - host: hemb.unicommerce.com
    - host: hopscotch.unicommerce.com
    - host: inyash.unicommerce.com
    - host: jpearlsdemo1.unicommerce.com
    - host: jpearlsdemo.unicommerce.com
    - host: june7.unicommerce.com
    - host: kapaas.unicommerce.com
    - host: kidcity.unicommerce.com
    - host: klenvorbeauty.unicommerce.com
    - host: koskii.unicommerce.com
    - host: kpa.unicommerce.com
    - host: laya.unicommerce.com
    - host: lifelongonline1.unicommerce.com
    - host: lifelongonline.unicommerce.com
    - host: m2all.unicommerce.com
    - host: madame.unicommerce.com
    - host: marketingking.unicommerce.com
    - host: morataara.unicommerce.com
    - host: mosaicwellnesspvtlmt.unicommerce.com
    - host: mosaicwellness.unicommerce.com
    - host: naosskincare.unicommerce.com
    - host: nariyalcosmetics.unicommerce.com
    - host: neuherbs.unicommerce.com
    - host: nishanttest.unicommerce.com
    - host: nnpl.unicommerce.com
    - host: oddityhealthcare.unicommerce.com
    - host: offdutyindia.unicommerce.com
    - host: oipl.unicommerce.com
    - host: okhai.unicommerce.com
    - host: paragon.unicommerce.com
    - host: paversengalnd.unicommerce.com
    - host: paycheque.unicommerce.com
    - host: pipabellaent.unicommerce.com
    - host: pngadgil.unicommerce.com
    - host: prozo2.unicommerce.com
    - host: psh.unicommerce.com
    - host: ptinvent.unicommerce.com
    - host: purpleunited.unicommerce.com
    - host: purpleunited-unireco.unicommerce.com
    - host: relentless.unicommerce.com
    - host: rhpl.unicommerce.com
    - host: scaleglobal.unicommerce.com
    - host: scarters.unicommerce.com
    - host: senrysa.unicommerce.com
    - host: shailyretails.unicommerce.com
    - host: shaily.unicommerce.com
    - host: shopper.unicommerce.com
    - host: shreeamartextiles.unicommerce.com
    - host: shreeamartextile.unicommerce.com
    - host: silveredge.unicommerce.com
    - host: silverpush.unicommerce.com
    - host: spoyl.unicommerce.com
    - host: stghopscotch.unicommerce.com
    - host: street9fashions.unicommerce.com
    - host: street9fashions-unireco.unicommerce.com
    - host: superbottoms.unicommerce.com
    - host: superkicks.unicommerce.com
    - host: swissbeauty.unicommerce.com
    - host: tahq.unicommerce.com
    - host: test1ecloudnewte.unicommerce.com
    - host: test1ecloudnew.unicommerce.com
    - host: test1ecloud.unicommerce.com
    - host: testent.unicommerce.com
    - host: testing1.unicommerce.com
    - host: thejuneshop.unicommerce.com
    - host: tiedribbons.unicommerce.com
    - host: tig.unicommerce.com
    - host: tns.unicommerce.com
    - host: uapl.unicommerce.com
    - host: ugaoo.unicommerce.com
    - host: urbanshore.unicommerce.com
    - host: v2retail.unicommerce.com
    - host: ventrag.unicommerce.com
    - host: ventra.unicommerce.com
    - host: vilvah.unicommerce.com
    - host: wasteaid.unicommerce.com
    - host: wellversedhealth.unicommerce.com
    - host: whitewillow.unicommerce.com
    - host: wizbiz.unicommerce.com
    - host: xyxx.unicommerce.com
    - host: yourdesign.unicommerce.com
    - host: yufta.unicommerce.com
    - host: yufta-unireco.unicommerce.com
    - host: zimaleto.unicommerce.com
  tls: []
istio:
  enabled: true
autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 4
  targetCPUUtilizationPercentage: 75
uniware-task:
  enabled: true
  podSecurityContext:
    fsGroup: 1000
  resources:
    limits:
      cpu: 13
      memory: 40Gi
    requests:
      cpu: 13
      memory: 40Gi
  nodeSelector:
    karpenter.sh/nodepool: eclouds-spot-pool
  tolerations:
    - key: "eclouds/spot"
      operator: "Exists"
      effect: "NoSchedule"
  image:
    repository: nexus-docker.unicommerce.infra:8123/uniware
    pullPolicy: Always
    tag: 1712.9-k8s
  imagePullSecrets:
    - name: nexus-secrets
  init:
    arguments:
      - mkdir -p /tmp/RotatedAppLogs
      - /tmp/files/customizedTemplates
      - /tmp/files/reconciliationInvoices
      - /tmp/files/saleOrderReturn
      - /tmp/files/import/logs
      - /tmp/files/saleOrder
      - /tmp/files/shippingLabel
  livenessProbe: {}
  readinessProbe: {}
  env:
    JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms25000M -Xmx30500M -XX:MetaspaceSize=512M -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -DtenantSpecificMongoConnectionsMaxSize=300 -DcommonMongoConnectionsMaxSize=1000 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud1#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DappIdentifier=app2 -DserverName=ECloud1 -Ds3bucketSuffix=-in -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.ecloud1-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo1.ecloud1-in.unicommerce.infra:27017,mongo2.ecloud1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/ecloud1#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DclusterName=ECloud1 -DactiveMQBrokerUrl=failover:tcp://activemq.ec1-in.unicommerce.infra:61616,tcp://activemq.ec1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=ECloud1 -DactiveMQBrokerPassword=<path:kv/data/uniware/ecloud1#activeMQBrokerPassword> -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -Dcom.sun.management.jmxremote -DAsyncLogger.ThreadNameStrategy=UNCACHED -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -DactiveMQMaxConnections=5 -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 -DactiveMQ2BrokerUrl=failover:tcp://activemq.ec2-in.unicommerce.infra:61616,tcp://activemq.ec2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud1#activeMQBrokerPassword> -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DexecuteJobs=true -javaagent:/usr/local/tomcat/conf/uniwareConfig/pinpoint-agent-2.5.2/pinpoint-bootstrap-2.5.2.jar -Dpinpoint.agentId=4 -Dpinpoint.applicationName=ECloud1_2 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource -DlogHibernateMemoryUsage=false -DhibernateGenerateStatistics=false -DrecoSpecificCommonMongoConnectionsMaxSize=50 -Dhazelcast.local.publicAddress=ecloud1-uniware-task-0.ecloud1-uniware-task.prod.svc.cluster.local:5701 -DhazelcastMembers=ecloud1-uniware-api-0.prod.svc.cluster.local:5701,ecloud1-uniware-api-1.prod.svc.cluster.local:5701,ecloud1-uniware-api-2.prod.svc.cluster.local:5701,ecloud1-uniware-task-0.ecloud1-uniware-task.prod.svc.cluster.local:5701 -DlogHibernateMemoryUsage=false -DhibernateGenerateStatistics=false -DrecoSpecificCommonMongoConnectionsMaxSize=50"
  volumeMounts:
    - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
      subPath: vault.properties
      name: uniware-config
    - mountPath: /usr/local/tomcat/logs/vaultConfig
      name: uniware-vault-config
    - mountPath: /usr/local/tomcat/conf/context.xml
      subPath: context.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/conf/server.xml
      subPath: server.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/catalina.sh
      subPath: catalina.sh
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/hazelcast.yml
      subPath: hazelcast.yml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
      subPath: gc_monitor.sh
      name: uniware-config
  volumes:
    - name: uniware-config
      configMap:
        name: ecloud1-uniware
        defaultMode: 0777
    - name: uniware-vault-config
      configMap:
        name: ecloud1-vault-uniware
        defaultMode: 0777
  service:
    hazelCastport: 5701
    type: ClusterIP
    headless:
      enabled: true
global:
  filebeat:
    config:
      name: ${PODNAME}.ecloud1-in.unicommerce.infra
      hosts:
        - "logstash1-in.unicommerce.infra:5044"
        - "logstash2-in.unicommerce.infra:5044"
  promtail:
    config:
      clients:
        - url: http://loki-gateway.monitoring.svc.cluster.local:80/loki/api/v1/push
          tenant_id: 'ecloud1'
  efs:
    enabled: false
