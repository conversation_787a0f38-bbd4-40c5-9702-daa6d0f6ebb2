enabled: true
replicaCount: 0
podSecurityContext:
  fsGroup: 1000
pdb:
  enabled: true
  minAvailable: 3
image:
  repository: nexus-docker.unicommerce.infra:8123/uniware
  pullPolicy: Always
  tag: 1710.9-k8s
imagePullSecrets:
  - name: nexus-secrets
service:
  type: ClusterIP
  hazelCastport: 5701
  headless:
    enabled: true
arguments:
  - "sed \"/$HOSTNAME/d\" /usr/local/tomcat/logs/server.xml >  /usr/local/tomcat/conf/server.xml && catalina.sh run"
resources:
  limits:
    cpu: 4
    memory: 16Gi
  requests:
    cpu: 4
    memory: 16Gi
nodeSelector:
  karpenter.sh/nodepool: eclouds-ondemand-pool
affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app.kubernetes.io/instance: ecloud4
            app.kubernetes.io/name: uniware-api
        topologyKey: kubernetes.io/hostname
tolerations:
  - key: "eclouds/ondemand"
    operator: "Exists"
    effect: "NoSchedule"
init:
  arguments:
    - mkdir -p /tmp/RotatedAppLogs
    - /tmp/files/customizedTemplates
    - /tmp/files/reconciliationInvoices
    - /tmp/files/saleOrderReturn
    - /tmp/files/import/logs
    - /tmp/files/saleOrder
    - /tmp/files/shippingLabel
livenessProbe: {}
readinessProbe:
  exec:
    command:
      - /bin/bash
      - -c
      - |-
        health_status=$(curl --write-out "%{http_code}\n" --max-time 3 -H "Host: baseprofessionale4.unicommerce.com" --silent --output /dev/null "http://localhost:8080/open/live")
        if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  failureThreshold: 5
  periodSeconds: 60
  successThreshold: 1
  timeoutSeconds: 30
  initialDelaySeconds: 180
env:
  JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms6000M -Xmx12000M -XX:MetaspaceSize=512M -DappIdentifier=${HOSTNAME} -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -DconfigMongoConnectionsMaxSize=30 -DcommonMongoConnectionsMaxSize=30 -DtenantSpecificMongoConnectionsMaxSize=150 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud4#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616  -DserverName=ECloud4 -Ds3bucketSuffix=-in -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.ecloud4-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo1.ecloud1-in.unicommerce.infra:27017,mongo2.ecloud1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/ecloud4#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DclusterName=ECloud4 -DactiveMQBrokerUrl=failover:tcp://activemq.ec1-in.unicommerce.infra:61616,tcp://activemq.ec1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=ECloud4 -DactiveMQBrokerPassword=<path:kv/data/uniware/ecloud4#activeMQBrokerPassword> -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -Dcom.sun.management.jmxremote -DAsyncLogger.ThreadNameStrategy=UNCACHED -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -DactiveMQMaxConnections=5 -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 -DactiveMQ2BrokerUrl=failover:tcp://activemq.ec2-in.unicommerce.infra:61616,tcp://activemq.ec2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud4#activeMQBrokerPassword> -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DmongoMaxWaitTime=5000 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017  -DexecuteJobs=false -DlogHibernateMemoryUsage=false -DhibernateGenerateStatistics=false -javaagent:/usr/local/tomcat/conf/uniwareConfig/pinpoint-agent-2.5.2/pinpoint-bootstrap-2.5.2.jar -Dpinpoint.agentId=${HOSTNAME} -Dpinpoint.applicationName=ECloud4 -DhibernateEnversAudit=true -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:+PrintGCDetails -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Dhazelcast.local.publicAddress=${HOSTNAME}.prod.svc.cluster.local:5701 -DhazelcastMembers=ecloud4-uniware-api-0.prod.svc.cluster.local:5701,ecloud4-uniware-api-1.prod.svc.cluster.local:5701,ecloud4-uniware-api-2.prod.svc.cluster.local:5701,ecloud4-uniware-api-3.prod.svc.cluster.local:5701,ecloud4-uniware-task-0.ecloud4-uniware-task.prod.svc.cluster.local:5701"
volumeMounts:
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
    subPath: vault.properties
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/vaultConfig
    name: uniware-vault-config
  - mountPath: /usr/local/tomcat/conf/context.xml
    subPath: context.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/server.xml
    subPath: server.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/catalina.sh
    subPath: catalina.sh
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/hazelcast.yml
    subPath: hazelcast.yml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
    subPath: gc_monitor.sh
    name: uniware-config
volumes:
  - name: uniware-config
    configMap:
      name: ecloud4-uniware
      defaultMode: 0777
  - name: uniware-vault-config
    configMap:
      name: ecloud4-vault-uniware
      defaultMode: 0777
volumeClaimTemplates:
  - name: logs
    accessModes:
      - ReadWriteOnce
    resources:
      requests:
        storage: 20Gi
    storageClassName: gp3
ingress:
  enabled: false
  className: "haproxy-public"
  annotations:
    haproxy-ingress.github.io/config-backend: |
      http-request set-header X-Forwarded-Proto https
  hosts:
    - host: 4mclothingllp.unicommerce.com
    - host: rimashilifestyle.unicommerce.com
    - host: rimashifliestyle.unicommerce.com
    - host: ddecor.unicommerce.com
    - host: kult.unicommerce.com
    - host: temptindia.unicommerce.com
    - host: labisa.unicommerce.com
    - host: 6degreeomni.unicommerce.com
    - host: aadvikfoods.unicommerce.com
    - host: ajantafootcareindia.unicommerce.com
    - host: allindiaaccess.unicommerce.com
    - host: almostgods.unicommerce.com
    - host: alphanso.unicommerce.com
    - host: alphanso-unireco.unicommerce.com
    - host: anthrilo.unicommerce.com
    - host: arun1.unicommerce.com
    - host: arun2.unicommerce.com
    - host: arun3.unicommerce.com
    - host: arun4.unicommerce.com
    - host: arun5.unicommerce.com
    - host: arune4test.unicommerce.com
    - host: aruntest.unicommerce.com
    - host: astitvacreations2021.unicommerce.com
    - host: astrid.unicommerce.com
    - host: atun.unicommerce.com
    - host: axio.unicommerce.com
    - host: axys.unicommerce.com
    - host: bagzone.unicommerce.com
    - host: balwaan.unicommerce.com
    - host: balwan.unicommerce.com
    - host: baseprofessionale4.unicommerce.com
    - host: bbe.unicommerce.com
    - host: beatitude.unicommerce.com
    - host: beatxp.unicommerce.com
    - host: bigflex.unicommerce.com
    - host: bodyglove.unicommerce.com
    - host: bonkerscorner.unicommerce.com
    - host: bonkerscorner-unireco.unicommerce.com
    - host: brauch.unicommerce.com
    - host: bulfyss2.unicommerce.com
    - host: bulfyss.unicommerce.com
    - host: casajoya.unicommerce.com
    - host: casa.unicommerce.com
    - host: cello.unicommerce.com
    - host: chemistrydesign.unicommerce.com
    - host: citylink.unicommerce.com
    - host: combonation.unicommerce.com
    - host: cottonculture23.unicommerce.com
    - host: cottonculture23-unireco.unicommerce.com
    - host: cottonculture.unicommerce.com
    - host: crazymonk.unicommerce.com
    - host: crazymonk-unireco.unicommerce.com
    - host: daughterearth.unicommerce.com
    - host: decathlon.unicommerce.com
    - host: demeter.unicommerce.com
    - host: dennislingo.unicommerce.com
    - host: donoshop.unicommerce.com
    - host: doorsdaily.unicommerce.com
    - host: dpac.unicommerce.com
    - host: dummytest.unicommerce.com
    - host: earthrhythm.unicommerce.com
    - host: easymom.unicommerce.com
    - host: eauto.unicommerce.com
    - host: ecloud4.unicommerce.com
    - host: ecloud4limitcheck70.unicommerce.com
    - host: elcinco.unicommerce.com
    - host: emiza.unicommerce.com
    - host: enn.unicommerce.com
    - host: epic.unicommerce.com
    - host: eshopboxflex.unicommerce.com
    - host: estheticinsights.unicommerce.com
    - host: evenflow.unicommerce.com
    - host: exclusiva.unicommerce.com
    - host: fabindialimited.unicommerce.com
    - host: fablestreet.unicommerce.com
    - host: fabricpandit.unicommerce.com
    - host: fashsun.unicommerce.com
    - host: fausto.unicommerce.com
    - host: flyberry.unicommerce.com
    - host: freecultr.unicommerce.com
    - host: ftx.unicommerce.com
    - host: funkykalakar.unicommerce.com
    - host: funkykalakar-unireco.unicommerce.com
    - host: fytika.unicommerce.com
    - host: geox.unicommerce.com
    - host: gladpack.unicommerce.com
    - host: globalbees.unicommerce.com
    - host: gritstones.unicommerce.com
    - host: hardtraccomputer.unicommerce.com
    - host: hausandkinder.unicommerce.com
    - host: healofy.unicommerce.com
    - host: healthunbox.unicommerce.com
    - host: helea.unicommerce.com
    - host: hidesign.unicommerce.com
    - host: hotpackindia.unicommerce.com
    - host: houseofmasabastging.unicommerce.com
    - host: houseofmasaba.unicommerce.com
    - host: hubberholme.unicommerce.com
    - host: ibacosmetics.unicommerce.com
    - host: imagepak.unicommerce.com
    - host: imaragbl.unicommerce.com
    - host: imfireflystg.unicommerce.com
    - host: incolorcosmetics.unicommerce.com
    - host: jamsticks.unicommerce.com
    - host: javtakurtis.unicommerce.com
    - host: jkfootwear.unicommerce.com
    - host: kaffappliancesindia.unicommerce.com
    - host: kaffapp.unicommerce.com
    - host: karagiri.unicommerce.com
    - host: kasthbhanjan.unicommerce.com
    - host: kazarmax.unicommerce.com
    - host: khanalfoods.unicommerce.com
    - host: kisahonline.unicommerce.com
    - host: kottylifestyle.unicommerce.com
    - host: lakshitafashions.unicommerce.com
    - host: laxmiinternational.unicommerce.com
    - host: leopax.unicommerce.com
    - host: lpanache.unicommerce.com
    - host: maate.unicommerce.com
    - host: maisondauraine.unicommerce.com
    - host: meateorite.unicommerce.com
    - host: misskurtijaipur.unicommerce.com
    - host: mokobaranew.unicommerce.com
    - host: montecarlofashions.unicommerce.com
    - host: montecarlofashions-unireco.unicommerce.com
    - host: montrez.unicommerce.com
    - host: mozafia.unicommerce.com
    - host: mylofamily.unicommerce.com
    - host: naiduhall.unicommerce.com
    - host: napsa.unicommerce.com
    - host: naturup.unicommerce.com
    - host: neemanspvtltd.unicommerce.com
    - host: nestasia.unicommerce.com
    - host: nlppl.unicommerce.com
    - host: nudge.unicommerce.com
    - host: openfields.unicommerce.com
    - host: optimas.unicommerce.com
    - host: organicharvest.unicommerce.com
    - host: palsonsderma.unicommerce.com
    - host: panit5171.unicommerce.com
    - host: perusal.unicommerce.com
    - host: petedge.unicommerce.com
    - host: pfipl.unicommerce.com
    - host: plantculture.unicommerce.com
    - host: powerhouse91.unicommerce.com
    - host: practicalstory.unicommerce.com
    - host: praushbeauty.unicommerce.com
    - host: primebrands.unicommerce.com
    - host: puramio.unicommerce.com
    - host: qurez.unicommerce.com
    - host: radicura.unicommerce.com
    - host: randomtest7861.unicommerce.com
    - host: randomtest786.unicommerce.com
    - host: rapidbox.unicommerce.com
    - host: rcasa.unicommerce.com
    - host: reflekt.unicommerce.com
    - host: rohitest1.unicommerce.com
    - host: royaldryfruits.unicommerce.com
    - host: rubix.unicommerce.com
    - host: ruheindia.unicommerce.com
    - host: rustorange.unicommerce.com
    - host: rustorange-unireco.unicommerce.com
    - host: saarya.unicommerce.com
    - host: sabhyataclothing.unicommerce.com
    - host: savaliya.unicommerce.com
    - host: selectbrands.unicommerce.com
    - host: senco.unicommerce.com
    - host: shananexport.unicommerce.com
    - host: sheetallogistics.unicommerce.com
    - host: shoetree.unicommerce.com
    - host: shpl.unicommerce.com
    - host: spykar.unicommerce.com
    - host: stagingchumabk.unicommerce.com
    - host: stgkapiva.unicommerce.com
    - host: stgnuawoman.unicommerce.com
    - host: stgtrell.unicommerce.com
    - host: stgtrurcarekidz.unicommerce.com
    - host: sumangalam.unicommerce.com
    - host: sunwoollen.unicommerce.com
    - host: svish.unicommerce.com
    - host: taniyatradingco.unicommerce.com
    - host: teabox.unicommerce.com
    - host: tealandterra.unicommerce.com
    - host: thecaistore.unicommerce.com
    - host: thesirona.unicommerce.com
    - host: thewhitepole.unicommerce.com
    - host: toddlers.unicommerce.com
    - host: tomburg.unicommerce.com
    - host: torquepharma.unicommerce.com
    - host: touchstone.unicommerce.com
    - host: toyshine.unicommerce.com
    - host: tradegenic.unicommerce.com
    - host: truweight.unicommerce.com
    - host: trylo.unicommerce.com
    - host: urabanspacestore.unicommerce.com
    - host: urbanspacestore.unicommerce.com
    - host: vamas.unicommerce.com
    - host: vbazaar.unicommerce.com
    - host: villain.unicommerce.com
    - host: vivaantafashion.unicommerce.com
    - host: warehousenow.unicommerce.com
    - host: wareiq.unicommerce.com
    - host: webkraft.unicommerce.com
    - host: whitehouseapparelspvtltd.unicommerce.com
    - host: whitehouseapparels.unicommerce.com
    - host: xtracover.unicommerce.com
    - host: yourspex.unicommerce.com
    - host: youthfirst.unicommerce.com
    - host: zeresouq.unicommerce.com
    - host: zoukst.unicommerce.com
    - host: zoukst-unireco.unicommerce.com
    - host: zunroof.unicommerce.com
  tls: []
istio:
  enabled: true
autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 4
  targetCPUUtilizationPercentage: 75
uniware-task:
  enabled: true
  podSecurityContext:
    fsGroup: 1000
  resources:
    limits:
      cpu: 20.0
      memory: 54Gi
    requests:
      cpu: 20.0
      memory: 54Gi
  nodeSelector:
    karpenter.sh/nodepool: eclouds-spot-pool
  tolerations:
    - key: "eclouds/spot"
      operator: "Exists"
      effect: "NoSchedule"
  image:
    repository: nexus-docker.unicommerce.infra:8123/uniware
    pullPolicy: Always
    tag: 1710.9-k8s
  imagePullSecrets:
    - name: nexus-secrets
  init:
    arguments:
      - mkdir -p /tmp/RotatedAppLogs
      - /tmp/files/customizedTemplates
      - /tmp/files/reconciliationInvoices
      - /tmp/files/saleOrderReturn
      - /tmp/files/import/logs
      - /tmp/files/saleOrder
      - /tmp/files/shippingLabel
  livenessProbe: {}
  readinessProbe: {}
  env:
    JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms29250M -Xmx41500M -XX:MetaspaceSize=512M -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -DtenantSpecificMongoConnectionsMaxSize=300 -DcommonMongoConnectionsMaxSize=1000 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud4#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud4#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DappIdentifier=app2 -DserverName=ECloud4 -Ds3bucketSuffix=-in -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.ecloud4-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo1.ecloud1-in.unicommerce.infra:27017,mongo2.ecloud1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/ecloud4#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DclusterName=ECloud4 -DactiveMQBrokerUrl=failover:tcp://activemq.ec1-in.unicommerce.infra:61616,tcp://activemq.ec1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=ECloud4 -DactiveMQBrokerPassword=<path:kv/data/uniware/ecloud4#activeMQBrokerPassword> -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -Dcom.sun.management.jmxremote -DAsyncLogger.ThreadNameStrategy=UNCACHED -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -DactiveMQMaxConnections=5 -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 -DactiveMQ2BrokerUrl=failover:tcp://activemq.ec2-in.unicommerce.infra:61616,tcp://activemq.ec2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud4#activeMQBrokerPassword> -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017  -DexecuteJobs=true -javaagent:/usr/local/tomcat/conf/uniwareConfig/pinpoint-agent-2.5.2/pinpoint-bootstrap-2.5.2.jar -Dpinpoint.agentId=4 -Dpinpoint.applicationName=ECloud4_2 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource -DlogHibernateMemoryUsage=false -DhibernateGenerateStatistics=false -DrecoSpecificCommonMongoConnectionsMaxSize=50 -Dhazelcast.local.publicAddress=ecloud4-uniware-task-0.ecloud4-uniware-task.prod.svc.cluster.local:5701 -DhazelcastMembers=ecloud4-uniware-api-0.prod.svc.cluster.local:5701,ecloud4-uniware-api-1.prod.svc.cluster.local:5701,ecloud4-uniware-api-2.prod.svc.cluster.local:5701,ecloud4-uniware-api-3.prod.svc.cluster.local:5701,ecloud4-uniware-task-0.ecloud4-uniware-task.prod.svc.cluster.local:5701"
  volumeMounts:
    - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
      subPath: vault.properties
      name: uniware-config
    - mountPath: /usr/local/tomcat/logs/vaultConfig
      name: uniware-vault-config
    - mountPath: /usr/local/tomcat/conf/context.xml
      subPath: context.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/conf/server.xml
      subPath: server.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/catalina.sh
      subPath: catalina.sh
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/hazelcast.yml
      subPath: hazelcast.yml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
      subPath: gc_monitor.sh
      name: uniware-config
  volumes:
    - name: uniware-config
      configMap:
        name: ecloud4-uniware
        defaultMode: 0777
    - name: uniware-vault-config
      configMap:
        name: ecloud4-vault-uniware
        defaultMode: 0777
  service:
    hazelCastport: 5701
    type: ClusterIP
    headless:
      enabled: true
global:
  filebeat:
    config:
      name: ${PODNAME}.ecloud4-in.unicommerce.infra
      hosts:
        - "logstash1-in.unicommerce.infra:5044"
        - "logstash2-in.unicommerce.infra:5044"
  promtail:
    config:
      clients:
        - url: http://loki-gateway.monitoring.svc.cluster.local:80/loki/api/v1/push
          tenant_id: 'ecloud4'
  efs:
    enabled: false
