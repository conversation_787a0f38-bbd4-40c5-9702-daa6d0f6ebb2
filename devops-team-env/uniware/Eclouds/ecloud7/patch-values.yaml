enabled: true
replicaCount: 0
podSecurityContext:
  fsGroup: 1000
pdb:
  enabled: true
  minAvailable: 3
image:
  repository: nexus-docker.unicommerce.infra:8123/uniware
  pullPolicy: Always
  tag: 1714.2-k8s
imagePullSecrets:
  - name: nexus-secrets
service:
  type: ClusterIP
  hazelCastport: 5701
  headless:
    enabled: true
arguments:
  - "sed \"/$HOSTNAME/d\" /usr/local/tomcat/logs/server.xml >  /usr/local/tomcat/conf/server.xml && catalina.sh run"
resources:
  limits:
    cpu: 5.0
    memory: 25Gi
  requests:
    cpu: 5.0
    memory: 25Gi
nodeSelector:
  karpenter.sh/nodepool: eclouds-ondemand-pool
affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app.kubernetes.io/instance: ecloud7
            app.kubernetes.io/name: uniware-api
        topologyKey: kubernetes.io/hostname
tolerations:
  - key: "eclouds/ondemand"
    operator: "Exists"
    effect: "NoSchedule"
init:
  arguments:
    - mkdir -p /tmp/RotatedAppLogs
    - /tmp/files/customizedTemplates
    - /tmp/files/reconciliationInvoices
    - /tmp/files/saleOrderReturn
    - /tmp/files/import/logs
    - /tmp/files/saleOrder
    - /tmp/files/shippingLabel
livenessProbe: {}
readinessProbe:
  exec:
    command:
      - /bin/bash
      - -c
      - |-
        health_status=$(curl --write-out "%{http_code}\n" --max-time 3 -H "Host: baseenterprise7.unicommerce.com" --silent --output /dev/null "http://localhost:8080/open/live")
        if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  failureThreshold: 5
  periodSeconds: 60
  successThreshold: 1
  timeoutSeconds: 30
  initialDelaySeconds: 180
env:
  JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms11000M -Xmx21000M -XX:MetaspaceSize=512M -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud7#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DtenantSpecificMongoConnectionsMaxSize=150 -DserverName=ECloud7 -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.ecloud7-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo1.ecloud1-in.unicommerce.infra:27017,mongo2.ecloud1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/ecloud7#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DclusterName=ECloud7 -DactiveMQBrokerUrl=failover:tcp://activemq.ec1-in.unicommerce.infra:61616,tcp://activemq.ec1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=ECloud7 -DactiveMQBrokerPassword=<path:kv/data/uniware/ecloud7#activeMQBrokerPassword> -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -Dcom.sun.management.jmxremote -DappIdentifier=${HOSTNAME} -DAsyncLogger.ThreadNameStrategy=UNCACHED -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -DactiveMQMaxConnections=5 -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 -DactiveMQ2BrokerUrl=failover:tcp://activemq.ec2-in.unicommerce.infra:61616,tcp://activemq.ec2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud7#activeMQBrokerPassword> -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017  -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DexecuteJobs=false -javaagent:/usr/local/tomcat/conf/uniwareConfig/pinpoint-agent-2.5.2/pinpoint-bootstrap-2.5.2.jar -Dpinpoint.agentId=${HOSTNAME} -Dpinpoint.applicationName=ECloud7 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Dhazelcast.local.publicAddress=${HOSTNAME}.prod.svc.cluster.local:5701 -DhazelcastMembers=ecloud7-uniware-api-0.prod.svc.cluster.local:5701,ecloud7-uniware-api-1.prod.svc.cluster.local:5701,ecloud7-uniware-api-2.prod.svc.cluster.local:5701,ecloud7-uniware-api-3.prod.svc.cluster.local:5701,ecloud7-uniware-task-0.ecloud7-uniware-task.prod.svc.cluster.local:5701"
volumeMounts:
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
    subPath: vault.properties
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/vaultConfig
    name: uniware-vault-config
  - mountPath: /usr/local/tomcat/conf/context.xml
    subPath: context.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/server.xml
    subPath: server.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/catalina.sh
    subPath: catalina.sh
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/hazelcast.yml
    subPath: hazelcast.yml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
    subPath: gc_monitor.sh
    name: uniware-config
volumes:
  - name: uniware-config
    configMap:
      name: ecloud7-uniware
      defaultMode: 0777
  - name: uniware-vault-config
    configMap:
      name: ecloud7-vault-uniware
      defaultMode: 0777
ingress:
  enabled: false
  className: "haproxy-public"
  annotations:
    haproxy-ingress.github.io/config-backend: |
      http-request set-header X-Forwarded-Proto https
  hosts:
    - host: 30shadeshouse.unicommerce.com
    - host: doubletwo.unicommerce.com
    - host: anantenterprise.unicommerce.com
    - host: aachho.unicommerce.com
    - host: aashigifts.unicommerce.com
    - host: abrossports.unicommerce.com
    - host: abrossports-unireco.unicommerce.com
    - host: absolutebrands.unicommerce.com
    - host: acevector.unicommerce.co.in
    - host: acevector.unicommerce.com
    - host: adroindia.unicommerce.com
    - host: ajananta.unicommerce.com
    - host: alamodelabel.unicommerce.com
    - host: alayaonline.unicommerce.com
    - host: amalaearthstore.unicommerce.com
    - host: amartextiles.unicommerce.com
    - host: amazewish.unicommerce.com
    - host: amour.unicommerce.com
    - host: ampleretail.unicommerce.com
    - host: anilakh.unicommerce.com
    - host: annpurnaexim.unicommerce.com
    - host: ardeur.unicommerce.com
    - host: artsanaindia.unicommerce.com
    - host: atoddlerthing.unicommerce.com
    - host: babyeli.unicommerce.com
    - host: baggit.unicommerce.com
    - host: baseenterprise7.unicommerce.com
    - host: beastlife.unicommerce.com
    - host: believecosmetics.unicommerce.com
    - host: beu.unicommerce.com
    - host: beyoung.unicommerce.com
    - host: blendjet.unicommerce.com
    - host: bluetokai.unicommerce.com
    - host: bluetyga.unicommerce.com
    - host: bonn.unicommerce.com
    - host: carbontree.unicommerce.com
    - host: careduraall.unicommerce.com
    - host: careduraall-unireco.unicommerce.com
    - host: charmacymilano.unicommerce.com
    - host: citrusclothing.unicommerce.com
    - host: coolcherrys.unicommerce.com
    - host: craftvatika.unicommerce.com
    - host: creandoassociates.unicommerce.com
    - host: currentbodyemiza.unicommerce.com
    - host: dalco.unicommerce.com
    - host: dalco-unireco.unicommerce.com
    - host: deoleo.unicommerce.com
    - host: devgange.unicommerce.com
    - host: dronalogitech.unicommerce.com
    - host: dscorp.unicommerce.com
    - host: dummyecloud7.unicommerce.com
    - host: eatbeter.unicommerce.com
    - host: ecloud7test01.unicommerce.com
    - host: ecloud7test1.unicommerce.com
    - host: ecloud7test2.unicommerce.com
    - host: ecloud7test3.unicommerce.com
    - host: ecloudtest14.unicommerce.com
    - host: ekickpvtltd.unicommerce.com
    - host: emcure.unicommerce.com
    - host: emizaseller.unicommerce.com
    - host: emizaurbanglobal.unicommerce.com
    - host: eternz.unicommerce.com
    - host: eximinternational.unicommerce.com
    - host: faithandpatience.unicommerce.co.in
    - host: featherscloset.unicommerce.com
    - host: fidelis.unicommerce.com
    - host: finerichknitwear.unicommerce.com
    - host: fireball.unicommerce.com
    - host: flaerhomes.unicommerce.com
    - host: fourbrown.unicommerce.com
    - host: foxtaleconsumer.unicommerce.com
    - host: fuaark.unicommerce.com
    - host: gadgetwagon.unicommerce.com
    - host: glamveda1.unicommerce.com
    - host: goodgudi.unicommerce.com
    - host: goodhomeskitchen.unicommerce.com
    - host: greenheirloom.unicommerce.com
    - host: gulikaapparelpvtltd.unicommerce.com
    - host: hairoriginals.unicommerce.com
    - host: halos.unicommerce.com
    - host: halos-unireco.unicommerce.com
    - host: hamdard.unicommerce.com
    - host: hampawellness.unicommerce.com
    - host: haproxytest.unicommerce.com
    - host: herbalchakra.unicommerce.com
    - host: hexagonnutrition.unicommerce.com
    - host: hexagonnutrition-unireco.unicommerce.com
    - host: highspirit.unicommerce.com
    - host: homecandy.unicommerce.com
    - host: honesthill.unicommerce.com
    - host: hsl.unicommerce.com
    - host: hydraswitch.unicommerce.com
    - host: ikid.unicommerce.com
    - host: instaplay.unicommerce.com
    - host: jaiganapati.unicommerce.com
    - host: jassventures
    - host: jassventures.unicommerce.com
    - host: jassventures-unireco.unicommerce.com
    - host: jrenterprise.unicommerce.com
    - host: juggjuggjeeyo.unicommerce.com
    - host: justdogsspalons.unicommerce.com
    - host: kadamhaat1.unicommerce.com
    - host: kamaayurveda.unicommerce.co.in
    - host: kanine.unicommerce.com
    - host: kannan.unicommerce.com
    - host: kannan-unireco.unicommerce.com
    - host: kcpc.unicommerce.com
    - host: knyamed.unicommerce.com
    - host: krishnaayurved.unicommerce.com
    - host: krishnaayurved-unireco.unicommerce.com
    - host: krishnatextiles.unicommerce.com
    - host: lazywindow.unicommerce.com
    - host: lifekrafts.unicommerce.com
    - host: lyskraft.unicommerce.com
    - host: madesa.unicommerce.com
    - host: mahina.unicommerce.com
    - host: maniexports.unicommerce.com
    - host: manogyamin.unicommerce.com
    - host: marketeers.unicommerce.com
    - host: marsinfiniti.unicommerce.com
    - host: masalakitchenandspices.unicommerce.com
    - host: medikart.unicommerce.com
    - host: medmongers.unicommerce.com
    - host: medmongers-unireco.unicommerce.com
    - host: mideltainternational.unicommerce.com
    - host: mojilaa.unicommerce.com
    - host: molarvalues.unicommerce.com
    - host: myfrido.unicommerce.com
    - host: nayam.unicommerce.com
    - host: ndpl.unicommerce.com
    - host: neudeskin.unicommerce.com
    - host: nokia.unicommerce.com
    - host: notcoy.unicommerce.com
    - host: nuegofashion.unicommerce.com
    - host: nuevotech.unicommerce.com
    - host: nutriherbs.unicommerce.com
    - host: nutrova.unicommerce.com
    - host: nutsdelish.unicommerce.com
    - host: ojasonline.unicommerce.com
    - host: ph91azah.unicommerce.com
    - host: phoniexshoes.unicommerce.com
    - host: phoniexshoes-unireco.unicommerce.com
    - host: pineandpetal.unicommerce.com
    - host: playr.unicommerce.com
    - host: prameshuniversal.unicommerce.com
    - host: pristinearoma.unicommerce.com
    - host: purplepandafashions.unicommerce.com
    - host: pyrops.unicommerce.com
    - host: radiohead.unicommerce.com
    - host: rasoishop.unicommerce.com
    - host: redcliffe.unicommerce.com
    - host: relaxofootwear.unicommerce.com
    - host: relaxo.unicommerce.com
    - host: renlife1.unicommerce.com
    - host: renlife.unicommerce.com
    - host: resil.unicommerce.com
    - host: saggian.unicommerce.com
    - host: saundh.unicommerce.com
    - host: scalebusinessservices.unicommerce.com
    - host: shimmerscosmetics.unicommerce.com
    - host: shimmerscosmetics-unireco.unicommerce.com
    - host: shimmerscosmetic.unicommerce.com
    - host: shivgangaent.unicommerce.com
    - host: shreebaidyanath.unicommerce.com
    - host: skr.unicommerce.com
    - host: skyseller.unicommerce.com
    - host: skyseller-unireco.unicommerce.com
    - host: smartveda.unicommerce.com
    - host: smowkly.unicommerce.com
    - host: sneakare.unicommerce.com
    - host: sockstreat.unicommerce.com
    - host: somersaultiopl.unicommerce.com
    - host: southernlabs.unicommerce.com
    - host: sploot.unicommerce.com
    - host: ssipl.unicommerce.com
    - host: starbust.unicommerce.com
    - host: stelcore.unicommerce.com
    - host: studioindigene1.unicommerce.com
    - host: stuffie.unicommerce.com
    - host: sumitsu.unicommerce.com
    - host: sunshinelifestyle.unicommerce.com
    - host: sunshinetrends.unicommerce.com
    - host: sutiindia.unicommerce.com
    - host: synthitepvt.unicommerce.com
    - host: syvo.unicommerce.com
    - host: test1ecloud7.unicommerce.com
    - host: testdomainj.unicommerce.co.in
    - host: testdomainnew1.unicommerce.co.in
    - host: testdomainnew.unicommerce.co.in
    - host: testecloud7instance.unicommerce.com
    - host: testingec7.unicommerce.com
    - host: testinstancedfgdddd.unicommerce.com
    - host: testtenant8.unicommerce.com
    - host: tewi.unicommerce.com
    - host: theanbac.unicommerce.com
    - host: thegoodbug.unicommerce.com
    - host: themarketeers.unicommerce.com
    - host: thewalletstore.unicommerce.com
    - host: tiedribbonsindia.unicommerce.com
    - host: trase.unicommerce.com
    - host: trayahealth.unicommerce.com
    - host: trevi.unicommerce.com
    - host: trinitieyewear.unicommerce.com
    - host: tsil.unicommerce.com
    - host: tsil-unireco.unicommerce.com
    - host: turritopsis.unicommerce.com
    - host: uniclan.unicommerce.com
    - host: uniclan-unireco.unicommerce.com
    - host: untest101.unicommerce.com
    - host: vaareeretail.unicommerce.com
    - host: vebrokfashion.unicommerce.com
    - host: vescocycles2.unicommerce.com
    - host: wakefitindia.unicommerce.com
    - host: wakefitindia-unireco.unicommerce.com
    - host: warriorlibertyshoes.unicommerce.com
    - host: wealdheritage.unicommerce.com
    - host: whiskers.unicommerce.com
    - host: wimplast.unicommerce.com
    - host: wings.unicommerce.com
    - host: xylem.unicommerce.com
    - host: yoho.unicommerce.com
    - host: ysf.unicommerce.co.in
    - host: zirise.unicommerce.com
    - host: zros.unicommerce.com
  tls: []

istio:
  enabled: true
  
autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 4
  targetCPUUtilizationPercentage: 75
uniware-task:
  enabled: true
  podSecurityContext:
    fsGroup: 1000
  resources:
    limits:
      cpu: 10
      memory: 45Gi
    requests:
      cpu: 10
      memory: 45Gi
  nodeSelector:
    karpenter.sh/nodepool: eclouds-spot-pool
  tolerations:
    - key: "eclouds/spot"
      operator: "Exists"
      effect: "NoSchedule"
  image:
    repository: nexus-docker.unicommerce.infra:8123/uniware
    pullPolicy: Always
    tag: 1714.2-k8s
  imagePullSecrets:
    - name: nexus-secrets
  init:
    arguments:
      - mkdir -p /tmp/RotatedAppLogs
      - /tmp/files/customizedTemplates
      - /tmp/files/reconciliationInvoices
      - /tmp/files/saleOrderReturn
      - /tmp/files/import/logs
      - /tmp/files/saleOrder
      - /tmp/files/shippingLabel
  livenessProbe: {}
  readinessProbe: {}
  env:
    JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -XX:MetaspaceSize=512M -Denv=c1 -DtenantSpecificMongoConnectionsMaxSize=300 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud7#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DserverName=ECloud7  -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DzookeeperConnectionTimeout=15000 -DcommonMongoConnectionsMaxSize=1000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.ecloud7-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo1.ecloud1-in.unicommerce.infra:27017,mongo2.ecloud1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DappIdentifier=app2 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/ecloud7#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DclusterName=ECloud7 -DactiveMQBrokerUrl=failover:tcp://activemq.ec1-in.unicommerce.infra:61616,tcp://activemq.ec1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=ECloud7 -DactiveMQBrokerPassword=<path:kv/data/uniware/ecloud7#activeMQBrokerPassword> -DAsyncLogger.ThreadNameStrategy=UNCACHED  -DactiveMQMaxConnections=5 -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=20 -DactiveMQ2BrokerUrl=failover:tcp://activemq.ec2-in.unicommerce.infra:61616,tcp://activemq.ec2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud7#activeMQBrokerPassword> -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017  -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DexecuteJobs=true -javaagent:/usr/local/tomcat/conf/uniwareConfig/pinpoint-agent-2.5.2/pinpoint-bootstrap-2.5.2.jar -Dpinpoint.agentId=EC7-2 -Dpinpoint.applicationName=ECloud7_2 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource -Xms25750M -Xmx34500M -DrecoSpecificCommonMongoConnectionsMaxSize=50 -Dhazelcast.local.publicAddress=ecloud7-uniware-task-0.ecloud7-uniware-task.prod.svc.cluster.local:5701 -DhazelcastMembers=ecloud7-uniware-api-0.prod.svc.cluster.local:5701,ecloud7-uniware-api-1.prod.svc.cluster.local:5701,ecloud7-uniware-api-2.prod.svc.cluster.local:5701,ecloud7-uniware-api-3.prod.svc.cluster.local:5701,ecloud7-uniware-task-0.ecloud7-uniware-task.prod.svc.cluster.local:5701"
  volumeMounts:
    - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
      subPath: vault.properties
      name: uniware-config
    - mountPath: /usr/local/tomcat/logs/vaultConfig
      name: uniware-vault-config
    - mountPath: /usr/local/tomcat/conf/context.xml
      subPath: context.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/conf/server.xml
      subPath: server.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/catalina.sh
      subPath: catalina.sh
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/hazelcast.yml
      subPath: hazelcast.yml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
      subPath: gc_monitor.sh
      name: uniware-config
  volumes:
    - name: uniware-config
      configMap:
        name: ecloud7-uniware
        defaultMode: 0777
    - name: uniware-vault-config
      configMap:
        name: ecloud7-vault-uniware
        defaultMode: 0777
  service:
    hazelCastport: 5701
    type: ClusterIP
    headless:
      enabled: true
global:
  filebeat:
    config:
      name: ${PODNAME}.ecloud7-in.unicommerce.infra
      hosts:
        - "logstash1-in.unicommerce.infra:5044"
        - "logstash2-in.unicommerce.infra:5044"
  promtail:
    config:
      clients:
        - url: http://loki-gateway.monitoring.svc.cluster.local:80/loki/api/v1/push
          tenant_id: 'ecloud7'
  efs:
    enabled: false


