enabled: true
replicaCount: 0
podSecurityContext:
  fsGroup: 1000
pdb:
  enabled: true
  minAvailable: 3
image:
  repository: nexus-docker.unicommerce.infra:8123/uniware
  pullPolicy: Always
  tag: 1710.9-k8s
imagePullSecrets:
  - name: nexus-secrets
service:
  type: ClusterIP
  hazelCastport: 5701
  headless:
    enabled: true
arguments:
  - "sed \"/$HOSTNAME/d\" /usr/local/tomcat/logs/server.xml >  /usr/local/tomcat/conf/server.xml && catalina.sh run"
resources:
  limits:
    cpu: 5
    memory: 40Gi
  requests:
    cpu: 5
    memory: 40Gi
nodeSelector:
  karpenter.sh/nodepool: eclouds-ondemand-pool
affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app.kubernetes.io/instance: ecloud2
            app.kubernetes.io/name: uniware-api
        topologyKey: kubernetes.io/hostname
tolerations:
  - key: "eclouds/ondemand"
    operator: "Exists"
    effect: "NoSchedule"
init:
  arguments:
    - mkdir -p /tmp/RotatedAppLogs
    - /tmp/files/customizedTemplates
    - /tmp/files/reconciliationInvoices
    - /tmp/files/saleOrderReturn
    - /tmp/files/import/logs
    - /tmp/files/saleOrder
    - /tmp/files/shippingLabel
livenessProbe: {}
readinessProbe:
  exec:
    command:
      - /bin/bash
      - -c
      - |-
        health_status=$(curl --write-out "%{http_code}\n" --max-time 3 -H "Host: baseenterprise2.unicommerce.com" --silent --output /dev/null "http://localhost:8080/open/live")
        if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  failureThreshold: 5
  periodSeconds: 60
  successThreshold: 1
  timeoutSeconds: 30
  initialDelaySeconds: 350
env:
  JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms20000M -Xmx32000M -XX:MetaspaceSize=512M -DinventoryDebugLoggingEnabled=true -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -DtenantSpecificMongoConnectionsMaxSize=150 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud2#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616  -DactiveMQMaxConnections=50 -DserverName=ECloud2 -Ds3bucketSuffix=-in -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/ecloud2#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DactiveMQQueuePrefetch=1 -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DAsyncLoggerConfig.ExceptionHandler=com.lmax.disruptor.IgnoreExceptionHandler -DzookeeperUrl=zookeeper.ecloud2-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo1.ecloud1-in.unicommerce.infra:27017,mongo2.ecloud1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DactiveMQBrokerUrl=failover:tcp://activemq.ec1-in.unicommerce.infra:61616,tcp://activemq.ec1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=ECloud2 -DactiveMQBrokerPassword=<path:kv/data/uniware/ecloud2#activeMQBrokerPassword> -DquartzThreadPoolSize=50 -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr  -DappIdentifier=${HOSTNAME} -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DactiveMQ2BrokerUrl=failover:tcp://activemq.ec2-in.unicommerce.infra:61616,tcp://activemq.ec2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud2#activeMQBrokerPassword> -DexecuteJobs=false -DclusterName=ECloud2 -DlogHibernateMemoryUsage=false -DhibernateGenerateStatistics=false -javaagent:/usr/local/tomcat/conf/uniwareConfig/pinpoint-agent-2.5.2/pinpoint-bootstrap-2.5.2.jar -Dpinpoint.agentId=${HOSTNAME} -Dpinpoint.applicationName=ECloud2 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d)  -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching  -DdataSource=UniwareHikariDataSource -DdataSource=UniwareHikariDataSource -XX:+UseG1GC -XX:+UseStringDeduplication -XX:StringDeduplicationAgeThreshold=15 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Dhazelcast.local.publicAddress=${HOSTNAME}.prod.svc.cluster.local:5701 -DhazelcastMembers=ecloud2-uniware-api-0.prod.svc.cluster.local:5701,ecloud2-uniware-api-1.prod.svc.cluster.local:5701,ecloud2-uniware-api-2.prod.svc.cluster.local:5701,ecloud2-uniware-api-3.prod.svc.cluster.local:5701,ecloud2-uniware-task-0.ecloud2-uniware-task.prod.svc.cluster.local:5701"
volumeMounts:
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
    subPath: vault.properties
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/vaultConfig
    name: uniware-vault-config
  - mountPath: /usr/local/tomcat/conf/context.xml
    subPath: context.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/server.xml
    subPath: server.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/catalina.sh
    subPath: catalina.sh
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/hazelcast.yml
    subPath: hazelcast.yml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
    subPath: gc_monitor.sh
    name: uniware-config
volumes:
  - name: uniware-config
    configMap:
      name: ecloud2-uniware
      defaultMode: 0777
  - name: uniware-vault-config
    configMap:
      name: ecloud2-vault-uniware
      defaultMode: 0777
volumeClaimTemplates:
  - name: logs
    accessModes: 
      - ReadWriteOnce
    resources:
      requests:
        storage: 20Gi
    storageClassName: gp3
ingress:
  enabled: false
  className: "haproxy-public"
  annotations:
    haproxy-ingress.github.io/config-backend: |
      http-request set-header X-Forwarded-Proto https
  hosts:
    - host: 2bme.unicommerce.com
    - host: stalwart.unicommerce.com
    - host: ullas.unicommerce.com
    - host: kngagrofood.unicommerce.com
    - host: xech.unicommerce.com
    - host: sonamenterprises.unicommerce.com
    - host: 6degree.unicommerce.com
    - host: a3e.unicommerce.com
    - host: abfashion.unicommerce.com
    - host: adisports.unicommerce.com
    - host: advancecomputersent.unicommerce.com
    - host: advancecomputers.unicommerce.com
    - host: advance.unicommerce.com
    - host: aent.unicommerce.com
    - host: amntea.unicommerce.com
    - host: annexcreation.unicommerce.com
    - host: annexcreation-unireco.unicommerce.com
    - host: anveshan.unicommerce.com
    - host: araviorganic1.unicommerce.com
    - host: aromee.unicommerce.com
    - host: artlounge.unicommerce.com
    - host: ashishsharmatest.unicommerce.com
    - host: bagadiya.unicommerce.com
    - host: bakersville.unicommerce.com
    - host: basantenterprises.unicommerce.co.in
    - host: baseenterprise2.unicommerce.com
    - host: bbpl.unicommerce.com
    - host: bewakoof.unicommerce.com
    - host: bhamadesigns.unicommerce.com
    - host: bigmusclesnutrition.unicommerce.com
    - host: bioworld.unicommerce.com
    - host: bira91.unicommerce.com
    - host: birkenstock.unicommerce.com
    - host: bloomexim.unicommerce.com
    - host: boxport.unicommerce.com
    - host: brandbox.unicommerce.com
    - host: branddeals.unicommerce.com
    - host: catcub.unicommerce.com
    - host: centro.unicommerce.com
    - host: charakweb.unicommerce.com
    - host: chogori.unicommerce.com
    - host: classicemporium.unicommerce.com
    - host: clicq.unicommerce.com
    - host: colorbar.unicommerce.com
    - host: columbussports.unicommerce.com
    - host: curefitstaging.unicommerce.com
    - host: curefit.unicommerce.com
    - host: curefit-unireco.unicommerce.com
    - host: darshanglobals.unicommerce.co.in
    - host: demotesting54541.unicommerce.com
    - host: demotesting5454.unicommerce.com
    - host: duke.unicommerce.com
    - host: emami.unicommerce.com
    - host: emazing.unicommerce.com
    - host: ergolab.unicommerce.com
    - host: ethoslimited.unicommerce.com
    - host: ethos.unicommerce.com
    - host: faces.unicommerce.com
    - host: fafh.unicommerce.com
    - host: fashorlifestyle.unicommerce.com
    - host: fashorlifestyle-unireco.unicommerce.com
    - host: fdinnocence.unicommerce.com
    - host: femella.unicommerce.com
    - host: flathead.unicommerce.com
    - host: flyingcolorz.unicommerce.com
    - host: fmcosmetics.unicommerce.com
    - host: fortcollins.unicommerce.com
    - host: freewill.unicommerce.com
    - host: funcorp.unicommerce.com
    - host: gimpex.unicommerce.com
    - host: gomechanic.unicommerce.com
    - host: greenorbit.unicommerce.com
    - host: greenplanet.unicommerce.com
    - host: gulikaapparel.unicommerce.com
    - host: handover.unicommerce.com
    - host: iconic.unicommerce.com
    - host: indethnic.unicommerce.com
    - host: indus.unicommerce.com
    - host: instafab.unicommerce.com
    - host: isha.unicommerce.com
    - host: itsybitsy.unicommerce.com
    - host: ivboc.unicommerce.com
    - host: ivbox.unicommerce.com
    - host: jadeblue.unicommerce.com
    - host: jaipurfabric.unicommerce.com
    - host: janvitha.unicommerce.com
    - host: jcbrothers.unicommerce.com
    - host: jcpl.unicommerce.com
    - host: julynightwear.unicommerce.com
    - host: justintime.unicommerce.com
    - host: kankatala.unicommerce.com
    - host: kapiva.unicommerce.com
    - host: katalysst.unicommerce.com
    - host: kazarmax1.unicommerce.com
    - host: kehpl.unicommerce.com
    - host: keyafoods.unicommerce.com
    - host: keyasethscosmetic.unicommerce.com
    - host: khadim.unicommerce.com
    - host: kundan.unicommerce.com
    - host: kushalretail.unicommerce.com
    - host: lal10.unicommerce.com
    - host: lawellness1.unicommerce.com
    - host: linen.unicommerce.com
    - host: lotuselectronics.unicommerce.com
    - host: lotus.unicommerce.com
    - host: maanja.unicommerce.com
    - host: marcadisati.unicommerce.com
    - host: marico1.unicommerce.com
    - host: meml.unicommerce.com
    - host: mensa.unicommerce.com
    - host: mikli.unicommerce.com
    - host: nebula.unicommerce.com
    - host: neolitemarketing.unicommerce.com
    - host: newu.unicommerce.com
    - host: nirvanaindia.unicommerce.com
    - host: northmist.unicommerce.com
    - host: not.unicommerce.com
    - host: ondesk.unicommerce.com
    - host: outerworld.unicommerce.com
    - host: oziva.unicommerce.com
    - host: pfl.unicommerce.com
    - host: piscestrade.unicommerce.com
    - host: pmexports.unicommerce.com
    - host: prettykrafts2.unicommerce.com
    - host: productdemo.unicommerce.com
    - host: proline.unicommerce.com
    - host: ptinventindia.unicommerce.com
    - host: rarerabbit.unicommerce.com
    - host: rarerabbit-unireco.unicommerce.com
    - host: ravr.unicommerce.com
    - host: reequil.unicommerce.com
    - host: robotbanao.unicommerce.com
    - host: royalclassic.unicommerce.com
    - host: saki.unicommerce.com
    - host: salwarstudio.unicommerce.com
    - host: schoolay.unicommerce.com
    - host: siba.unicommerce.com
    - host: slrpl.unicommerce.com
    - host: soulflower.unicommerce.com
    - host: srinidhi555.unicommerce.com
    - host: stagingzed.unicommerce.com
    - host: stgdhani.unicommerce.com
    - host: stgfaces.unicommerce.com
    - host: stgpickily.unicommerce.com
    - host: stgsleepyhead.unicommerce.com
    - host: stgxpress.unicommerce.com
    - host: suditi.unicommerce.com
    - host: sunrise.unicommerce.com
    - host: surmanjainfotech.unicommerce.com
    - host: tasva.unicommerce.com
    - host: teakwoodindia.unicommerce.com
    - host: teakwoodindia-unireco.unicommerce.com
    - host: teakwood.unicommerce.com
    - host: testenterprise1.unicommerce.com
    - host: testtenantecloud501.unicommerce.com
    - host: thehealthybinge.unicommerce.com
    - host: themomstore1.unicommerce.com
    - host: themomstore1-unireco.unicommerce.com
    - host: themomstore.unicommerce.com
    - host: timehut.unicommerce.com
    - host: toonz.unicommerce.com
    - host: trevifurniture.unicommerce.com
    - host: trucarekidz.unicommerce.com
    - host: trudelfashion.unicommerce.com
    - host: truhair.unicommerce.com
    - host: turmswearstaging.unicommerce.com
    - host: turmswear.unicommerce.com
    - host: ucstaging.unicommerce.com
    - host: udayfashion.unicommerce.com
    - host: unikuls.unicommerce.com
    - host: unikul.unicommerce.com
    - host: uspl.unicommerce.com
    - host: vkinternational.unicommerce.com
    - host: vki.unicommerce.com
    - host: wakeupinnovation.unicommerce.com
    - host: wickedgud.unicommerce.com
    - host: wishkaro.unicommerce.com
    - host: xtep.unicommerce.com
    - host: yieldplus.unicommerce.com
  tls: []

istio:
  enabled: true

autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 4
  targetCPUUtilizationPercentage: 75
uniware-task:
  enabled: true
  podSecurityContext:
    fsGroup: 1000
  resources:
    limits:
      cpu: 14
      memory: 40Gi
    requests:
      cpu: 14
      memory: 40Gi
  nodeSelector:
    karpenter.sh/nodepool: eclouds-spot-pool
  tolerations:
    - key: "eclouds/spot"
      operator: "Exists"
      effect: "NoSchedule"
  image:
    repository: nexus-docker.unicommerce.infra:8123/uniware
    pullPolicy: Always
    tag: 1710.9-k8s
  imagePullSecrets:
    - name: nexus-secrets
  init:
    arguments:
      - mkdir -p /tmp/RotatedAppLogs
      - /tmp/files/customizedTemplates
      - /tmp/files/reconciliationInvoices
      - /tmp/files/saleOrderReturn
      - /tmp/files/import/logs
      - /tmp/files/saleOrder
      - /tmp/files/shippingLabel
  livenessProbe: {}
  readinessProbe: {}
  env:
    JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms25000M -Xmx30500M -XX:MetaspaceSize=512M -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -DcommonMongoConnectionsMaxSize=1000 -DtenantSpecificMongoConnectionsMaxSize=300 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud2#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DappIdentifier=app2 -DserverName=ECloud2 -Ds3bucketSuffix=-in -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.ecloud2-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo1.ecloud1-in.unicommerce.infra:27017,mongo2.ecloud1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/ecloud2#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DclusterName=ECloud2 -DactiveMQBrokerUrl=failover:tcp://activemq.ec1-in.unicommerce.infra:61616,tcp://activemq.ec1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=ECloud2 -DactiveMQBrokerPassword=<path:kv/data/uniware/ecloud2#activeMQBrokerPassword> -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -Dcom.sun.management.jmxremote -DAsyncLogger.ThreadNameStrategy=UNCACHED -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -DactiveMQMaxConnections=5 -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 -DactiveMQ2BrokerUrl=failover:tcp://activemq.ec2-in.unicommerce.infra:61616,tcp://activemq.ec2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud2#activeMQBrokerPassword> -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DexecuteJobs=true -javaagent:/usr/local/tomcat/conf/uniwareConfig/pinpoint-agent-2.5.2/pinpoint-bootstrap-2.5.2.jar -Dpinpoint.agentId=4 -Dpinpoint.applicationName=ECloud2_2 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource -DlogHibernateMemoryUsage=false -DhibernateGenerateStatistics=false -DrecoSpecificCommonMongoConnectionsMaxSize=50 -Dhazelcast.local.publicAddress=ecloud2-uniware-task-0.ecloud2-uniware-task.prod.svc.cluster.local:5701 -DhazelcastMembers=ecloud2-uniware-api-0.prod.svc.cluster.local:5701,ecloud2-uniware-api-1.prod.svc.cluster.local:5701,ecloud2-uniware-api-2.prod.svc.cluster.local:5701,ecloud2-uniware-api-3.prod.svc.cluster.local:5701,ecloud2-uniware-task-0.ecloud2-uniware-task.prod.svc.cluster.local:5701"
  volumeMounts:
    - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
      subPath: vault.properties
      name: uniware-config
    - mountPath: /usr/local/tomcat/logs/vaultConfig
      name: uniware-vault-config
    - mountPath: /usr/local/tomcat/conf/context.xml
      subPath: context.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/conf/server.xml
      subPath: server.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/catalina.sh
      subPath: catalina.sh
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/hazelcast.yml
      subPath: hazelcast.yml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
      subPath: gc_monitor.sh
      name: uniware-config
  volumes:
    - name: uniware-config
      configMap:
        name: ecloud2-uniware
        defaultMode: 0777
    - name: uniware-vault-config
      configMap:
        name: ecloud2-vault-uniware
        defaultMode: 0777
  service:
    hazelCastport: 5701
    type: ClusterIP
    headless:
      enabled: true
global:
  filebeat:
    config:
      name: ${PODNAME}.ecloud2-in.unicommerce.infra
      hosts:
        - "logstash1-in.unicommerce.infra:5044"
        - "logstash2-in.unicommerce.infra:5044"
  promtail:
    config:
      clients:
        - url: http://loki-gateway.monitoring.svc.cluster.local:80/loki/api/v1/push
          tenant_id: 'ecloud2'
  efs:
    enabled: false
