enabled: true
replicaCount: 0
podSecurityContext:
  fsGroup: 1000
pdb:
  enabled: true
  minAvailable: 3
image:
  repository: nexus-docker.unicommerce.infra:8123/uniware
  pullPolicy: Always
  tag: 1710.9-k8s
imagePullSecrets:
  - name: nexus-secrets
service:
  type: ClusterIP
  hazelCastport: 5701
  headless:
    enabled: true
arguments:
  - "sed \"/$HOSTNAME/d\" /usr/local/tomcat/logs/server.xml >  /usr/local/tomcat/conf/server.xml && catalina.sh run"
resources:
  limits:
    cpu: 5
    memory: 25Gi
  requests:
    cpu: 5
    memory: 25Gi
nodeSelector:
  karpenter.sh/nodepool: eclouds-ondemand-pool
affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app.kubernetes.io/instance: ecloud5
            app.kubernetes.io/name: uniware-api
        topologyKey: kubernetes.io/hostname
tolerations:
  - key: "eclouds/ondemand"
    operator: "Exists"
    effect: "NoSchedule"
init:
  arguments:
    - mkdir -p /tmp/RotatedAppLogs
    - /tmp/files/customizedTemplates
    - /tmp/files/reconciliationInvoices
    - /tmp/files/saleOrderReturn
    - /tmp/files/import/logs
    - /tmp/files/saleOrder
    - /tmp/files/shippingLabel
livenessProbe: {}
readinessProbe:
  exec:
    command:
      - /bin/bash
      - -c
      - |-
        health_status=$(curl --write-out "%{http_code}\n" --max-time 3 -H "Host: baseenterprise5.unicommerce.com" --silent --output /dev/null "http://localhost:8080/open/live")
        if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  failureThreshold: 5
  periodSeconds: 60
  successThreshold: 1
  timeoutSeconds: 30
  initialDelaySeconds: 180
env:
  JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms7000M -Xmx20000M -XX:MetaspaceSize=512M -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -DconfigMongoConnectionsMaxSize=30 -DcommonMongoConnectionsMaxSize=30 -DtenantSpecificMongoConnectionsMaxSize=150 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud5#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DappIdentifier=${HOSTNAME} -DserverName=ECloud5 -Ds3bucketSuffix=-in -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.ecloud5-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo1.ecloud1-in.unicommerce.infra:27017,mongo2.ecloud1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/ecloud5#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DclusterName=ECloud5 -DactiveMQBrokerUrl=failover:tcp://activemq.ec1-in.unicommerce.infra:61616,tcp://activemq.ec1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=ECloud5 -DactiveMQBrokerPassword=<path:kv/data/uniware/ecloud5#activeMQBrokerPassword> -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -Dcom.sun.management.jmxremote -DAsyncLogger.ThreadNameStrategy=UNCACHED -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -DactiveMQMaxConnections=5 -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 -DactiveMQ2BrokerUrl=failover:tcp://activemq.ec2-in.unicommerce.infra:61616,tcp://activemq.ec2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud5#activeMQBrokerPassword> -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017  -DexecuteJobs=false -javaagent:/usr/local/tomcat/conf/uniwareConfig/pinpoint-agent-2.5.2/pinpoint-bootstrap-2.5.2.jar -Dpinpoint.agentId=${HOSTNAME} -Dpinpoint.applicationName=ECloud5 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DlogHibernateMemoryUsage=false -DhibernateGenerateStatistics=false -DhibernateEnversAudit=true -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource -DinventoryDebugLoggingEnabled=true -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Dhazelcast.local.publicAddress=${HOSTNAME}.prod.svc.cluster.local:5701 -DhazelcastMembers=ecloud5-uniware-api-0.prod.svc.cluster.local:5701,ecloud5-uniware-api-1.prod.svc.cluster.local:5701,ecloud5-uniware-api-2.prod.svc.cluster.local:5701,ecloud5-uniware-api-3.prod.svc.cluster.local:5701,ecloud5-uniware-task-0.ecloud5-uniware-task.prod.svc.cluster.local:5701"
volumeMounts:
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
    subPath: vault.properties
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/vaultConfig
    name: uniware-vault-config
  - mountPath: /usr/local/tomcat/conf/context.xml
    subPath: context.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/server.xml
    subPath: server.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/catalina.sh
    subPath: catalina.sh
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/hazelcast.yml
    subPath: hazelcast.yml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
    subPath: gc_monitor.sh
    name: uniware-config
volumes:
  - name: uniware-config
    configMap:
      name: ecloud5-uniware
      defaultMode: 0777
  - name: uniware-vault-config
    configMap:
      name: ecloud5-vault-uniware
      defaultMode: 0777
ingress:
  enabled: false
  className: "haproxy-public"
  annotations:
    haproxy-ingress.github.io/config-backend: |
      http-request set-header X-Forwarded-Proto https
  hosts:
    - host: 10club.unicommerce.com
    - host: himalayawellness.unicommerce.com
    - host: oliveware.unicommerce.com
    - host: astroarunpandit.unicommerce.com
    - host: upscalio-unireco.unicommerce.com
    - host: stgtwt.unicommerce.com
    - host: cvcclothing.unicommerce.com
    - host: cfil.unicommerce.com
    - host: 2020eyecare.unicommerce.com
    - host: aasthanova.unicommerce.com
    - host: accessorizelondon.unicommerce.com
    - host: acefour.unicommerce.com
    - host: acesintact.unicommerce.com
    - host: actionb2c.unicommerce.com
    - host: addvantis.unicommerce.com
    - host: akya.unicommerce.com
    - host: alaya.unicommerce.com
    - host: albertotorresi1.unicommerce.com
    - host: amilo.unicommerce.com
    - host: aphrodite.unicommerce.com
    - host: arcfandb.unicommerce.com
    - host: ashleyandalvis.unicommerce.com
    - host: atpl.unicommerce.com
    - host: automonks.unicommerce.com
    - host: avimeeherbal.unicommerce.com
    - host: azista.unicommerce.com
    - host: banjars.unicommerce.com
    - host: barcelonaclub.unicommerce.com
    - host: baseenterprise5.unicommerce.com
    - host: bearhouse.unicommerce.com
    - host: beelittle.unicommerce.com
    - host: bennysbowl.unicommerce.com
    - host: beyobo.unicommerce.com
    - host: bharatagri.unicommerce.com
    - host: blueisland.unicommerce.com
    - host: bodycupid.unicommerce.com
    - host: boldnelegant.unicommerce.com
    - host: botanybar.unicommerce.com
    - host: bunaai.unicommerce.com
    - host: bunawat.unicommerce.com
    - host: canopyessence.unicommerce.com
    - host: clensta.unicommerce.com
    - host: cocotreats.unicommerce.com
    - host: cookdventures.unicommerce.com
    - host: counfreedise.unicommerce.com
    - host: cureka.unicommerce.com
    - host: deesangourmetexpress.unicommerce.com
    - host: dentista.unicommerce.com
    - host: dionysus.unicommerce.com
    - host: divyakitchenwares.unicommerce.com
    - host: dolsiaretailers.unicommerce.com
    - host: dolsiaretailers-unireco.unicommerce.com
    - host: dragarwals.unicommerce.com
    - host: dslr.unicommerce.com
    - host: ecomexpresslimited.unicommerce.com
    - host: edana.unicommerce.com
    - host: ellementry.unicommerce.com
    - host: emizampseller.unicommerce.com
    - host: eppe.unicommerce.com
    - host: exim.unicommerce.com
    - host: exotic.unicommerce.com
    - host: fashfun.unicommerce.com
    - host: fashiondream.unicommerce.com
    - host: fashionidentity.unicommerce.com
    - host: fds.unicommerce.com
    - host: fido.unicommerce.com
    - host: fifthgear.unicommerce.com
    - host: firkiwholesale.unicommerce.com
    - host: fitfire.unicommerce.com
    - host: frangipanigbl.unicommerce.com
    - host: freecultr1.unicommerce.com
    - host: gblgarden.unicommerce.com
    - host: gettechgo.unicommerce.com
    - host: goldfishhuc.unicommerce.com
    - host: gramiyaa.unicommerce.com
    - host: gritstones01.unicommerce.com
    - host: grow91.unicommerce.com
    - host: gustofoods1.unicommerce.com
    - host: hewo.unicommerce.com
    - host: himaira.unicommerce.com
    - host: houseofchikankari
    - host: houseofchikankari.unicommerce.com
    - host: houseofchikankari-unireco.unicommerce.com
    - host: houseofmakeup.unicommerce.com
    - host: hvglobal.unicommerce.com
    - host: iberia.unicommerce.com
    - host: indifusion.unicommerce.com
    - host: innovist.unicommerce.com
    - host: involveyoursenses.unicommerce.com
    - host: jadebluelifestyle.unicommerce.com
    - host: jaipurkurti1.unicommerce.com
    - host: jazzandsizzle1.unicommerce.com
    - host: jji.unicommerce.com
    - host: jovees.unicommerce.com
    - host: julynehatest.unicommerce.com
    - host: juscorp.unicommerce.com
    - host: justdogsecom.unicommerce.com
    - host: kalpanastores.unicommerce.com
    - host: karwan.unicommerce.com
    - host: kedarfabops.unicommerce.com
    - host: kedarfabops-unireco.unicommerce.com
    - host: khadimauriherbals.unicommerce.com
    - host: khadinaturalhealthcare.unicommerce.com
    - host: kimirica.unicommerce.com
    - host: kipek.unicommerce.com
    - host: kisahapparelsprivatelimited.unicommerce.com
    - host: lapster.unicommerce.com
    - host: lemoizytrading.unicommerce.com
    - host: lifelong.unicommerce.com
    - host: linenclub.unicommerce.com
    - host: longevity.unicommerce.com
    - host: losung360cfa.unicommerce.com
    - host: losung360kenstar.unicommerce.com
    - host: loveoribel.unicommerce.com
    - host: luxindustries.unicommerce.com
    - host: market99.unicommerce.com
    - host: meenabazaarmbz.unicommerce.com
    - host: miarcus.unicommerce.com
    - host: mittalbooks.unicommerce.com
    - host: mokksha.unicommerce.com
    - host: momshome.unicommerce.com
    - host: moonforestindia.unicommerce.com
    - host: musclepro.unicommerce.com
    - host: myaza.unicommerce.com
    - host: myfitness.unicommerce.com
    - host: nandanicreation.unicommerce.com
    - host: nanostuffs.unicommerce.com
    - host: navamcommercials.unicommerce.com
    - host: neemli.unicommerce.com
    - host: newfangled.unicommerce.com
    - host: newfangled-unireco.unicommerce.com
    - host: newme.unicommerce.com
    - host: niine.unicommerce.com
    - host: nirvasahealthcare.unicommerce.com
    - host: nobelhygiene.unicommerce.com
    - host: nobelhygiene-unireco.unicommerce.com
    - host: nocapfoods.unicommerce.com
    - host: nutriglowgbl.unicommerce.com
    - host: ogmart.unicommerce.com
    - host: oilcurehealthandbeauty.unicommerce.com
    - host: onecentre.unicommerce.com
    - host: onefriday.unicommerce.com
    - host: onefriday-unireco.unicommerce.com
    - host: ordinareetribe.unicommerce.com
    - host: orionindia.unicommerce.com
    - host: partypropz.unicommerce.com
    - host: pepeinnerfashion.unicommerce.com
    - host: petsempire.unicommerce.com
    - host: petsy.unicommerce.com
    - host: physicswallah.unicommerce.com
    - host: piitest22.unicommerce.com
    - host: piitest23.unicommerce.com
    - host: piitest.unicommerce.com
    - host: pnrao.unicommerce.com
    - host: prmretail.unicommerce.com
    - host: promunt.unicommerce.com
    - host: pspeaches.unicommerce.com
    - host: purvaja.unicommerce.com
    - host: qone1.unicommerce.com
    - host: quickshift.unicommerce.com
    - host: racquethub.unicommerce.com
    - host: rainberry.unicommerce.com
    - host: reliance.unicommerce.com
    - host: rgb.unicommerce.com
    - host: rhv.unicommerce.com
    - host: saaryalifestyle.unicommerce.com
    - host: sakshilingeries.unicommerce.com
    - host: sakshilingeries-unireco.unicommerce.com
    - host: saptamveda.unicommerce.com
    - host: satyamkraft1.unicommerce.com
    - host: scakhi.unicommerce.com
    - host: sfrfootwear.unicommerce.com
    - host: shoolin.unicommerce.com
    - host: shreetech.unicommerce.com
    - host: silaretail.unicommerce.com
    - host: sixthsense.unicommerce.com
    - host: skandaorganic42.unicommerce.com
    - host: sleepx.unicommerce.com
    - host: sombrerodigital.unicommerce.com
    - host: sprvl.unicommerce.com
    - host: srisritattva.unicommerce.com
    - host: stargate.unicommerce.com
    - host: stgglobalbees.unicommerce.com
    - host: strabo.unicommerce.com
    - host: studdmuffyn.unicommerce.com
    - host: stylogue.unicommerce.com
    - host: swashaa.unicommerce.com
    - host: synthite.unicommerce.com
    - host: tatvarthahealthprivatelimited.unicommerce.com
    - host: testecloudtenant01.unicommerce.com
    - host: testtenantecloud502.unicommerce.com
    - host: testtenantecloud503.unicommerce.com
    - host: tistabeneenterprisespvtltd.unicommerce.com
    - host: trioonline.unicommerce.com
    - host: twinbirds.unicommerce.com
    - host: twinbirds-unireco.unicommerce.com
    - host: twt.unicommerce.com
    - host: uathayam.unicommerce.com
    - host: unicards.unicommerce.com
    - host: unilever.unicommerce.com
    - host: upscalio.unicommerce.com
    - host: vaaree.unicommerce.com
    - host: vecom.unicommerce.com
    - host: vendmartretail.unicommerce.com
    - host: vgcraft.unicommerce.com
    - host: vipclothingltd.unicommerce.com
    - host: vitabiotics.unicommerce.com
    - host: vlado.unicommerce.com
    - host: vooki.unicommerce.com
    - host: voyllagbl.unicommerce.com
    - host: voylla.unicommerce.com
    - host: wareiqretail.unicommerce.com
    - host: wildturmeric.unicommerce.com
    - host: wiremartmsk.unicommerce.com
    - host: wiremart.unicommerce.com
    - host: yogabars.unicommerce.com
  tls: []

istio:
  enabled: true
  
autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 4
  targetCPUUtilizationPercentage: 75
uniware-task:
  enabled: true
  podSecurityContext:
    fsGroup: 1000
  resources:
    limits:
      cpu: 15.5
      memory: 50Gi
    requests:
      cpu: 15.5
      memory: 50Gi
  nodeSelector:
    karpenter.sh/nodepool: eclouds-spot-pool
  tolerations:
    - key: "eclouds/spot"
      operator: "Exists"
      effect: "NoSchedule"
  image:
    repository: nexus-docker.unicommerce.infra:8123/uniware
    pullPolicy: Always
    tag: 1710.9-k8s
  imagePullSecrets:
    - name: nexus-secrets
  init:
    arguments:
      - mkdir -p /tmp/RotatedAppLogs
      - /tmp/files/customizedTemplates
      - /tmp/files/reconciliationInvoices
      - /tmp/files/saleOrderReturn
      - /tmp/files/import/logs
      - /tmp/files/saleOrder
      - /tmp/files/shippingLabel
  livenessProbe: {}
  readinessProbe: {}
  env:
    JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms19750M -Xmx39500M -XX:MetaspaceSize=512M -XX:MaxMetaspaceSize=1024M -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -DcommonMongoConnectionsMaxSize=1000 -DtenantSpecificMongoConnectionsMaxSize=300 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud5#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DappIdentifier=app2 -DserverName=ECloud5 -Ds3bucketSuffix=-in -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.ecloud5-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo1.ecloud1-in.unicommerce.infra:27017,mongo2.ecloud1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/ecloud5#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DclusterName=ECloud5 -DactiveMQBrokerUrl=failover:tcp://activemq.ec1-in.unicommerce.infra:61616,tcp://activemq.ec1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=ECloud5 -DactiveMQBrokerPassword=<path:kv/data/uniware/ecloud5#activeMQBrokerPassword> -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -Dcom.sun.management.jmxremote -DAsyncLogger.ThreadNameStrategy=UNCACHED -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -DactiveMQMaxConnections=5 -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 -DactiveMQ2BrokerUrl=failover:tcp://activemq.ec2-in.unicommerce.infra:61616,tcp://activemq.ec2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud5#activeMQBrokerPassword> -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DexecuteJobs=true -javaagent:/usr/local/tomcat/conf/uniwareConfig/pinpoint-agent-2.5.2/pinpoint-bootstrap-2.5.2.jar -Dpinpoint.agentId=4 -Dpinpoint.applicationName=ECloud5_2 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -DcommonMongoConnectionsMaxSize=100 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource -DlogHibernateMemoryUsage=false -DhibernateGenerateStatistics=false -DrecoSpecificCommonMongoConnectionsMaxSize=50 -Dhazelcast.local.publicAddress=ecloud5-uniware-task-0.ecloud5-uniware-task.prod.svc.cluster.local:5701 -DhazelcastMembers=ecloud5-uniware-api-0.prod.svc.cluster.local:5701,ecloud5-uniware-api-1.prod.svc.cluster.local:5701,ecloud5-uniware-api-2.prod.svc.cluster.local:5701,ecloud5-uniware-api-3.prod.svc.cluster.local:5701,ecloud5-uniware-task-0.ecloud5-uniware-task.prod.svc.cluster.local:5701"
  volumeMounts:
    - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
      subPath: vault.properties
      name: uniware-config
    - mountPath: /usr/local/tomcat/logs/vaultConfig
      name: uniware-vault-config
    - mountPath: /usr/local/tomcat/conf/context.xml
      subPath: context.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/conf/server.xml
      subPath: server.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/catalina.sh
      subPath: catalina.sh
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/hazelcast.yml
      subPath: hazelcast.yml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
      subPath: gc_monitor.sh
      name: uniware-config
  volumes:
    - name: uniware-config
      configMap:
        name: ecloud5-uniware
        defaultMode: 0777
    - name: uniware-vault-config
      configMap:
        name: ecloud5-vault-uniware
        defaultMode: 0777
  service:
    hazelCastport: 5701
    type: ClusterIP
    headless:
      enabled: true
global:
  filebeat:
    config:
      name: ${PODNAME}.ecloud5-in.unicommerce.infra
      hosts:
        - "logstash1-in.unicommerce.infra:5044"
        - "logstash2-in.unicommerce.infra:5044"
  promtail:
    config:
      clients:
        - url: http://loki-gateway.monitoring.svc.cluster.local:80/loki/api/v1/push
          tenant_id: 'ecloud5'
  efs:
    enabled: false


