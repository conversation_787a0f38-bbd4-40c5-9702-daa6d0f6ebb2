helmCharts:
- releaseName: testecloud3
  name: uniware
  valuesMerge: merge
  includeCRDs: true
  version: 2.4.0
  repo: https://chartmuseum.unicommerce.co.in
  valuesFile: patch-values.yaml

configMapGenerator:
- name: testecloud3-uniware
  files:
  - configs/vault.properties
  - configs/server.xml
  - configs/context.xml
  - configs/hazelcast.yml
  - configs/catalina.sh
  - configs/gc_monitor.sh
- name: testecloud3-vault-uniware
  files:
  - configs/vaultCredentials.yml
  Options:
    disableNameSuffixHash: true
