apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: uniware-eclouds
  namespace: argocd
spec:
  syncPolicy:
    preserveResourcesOnDeletion: true
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
  - list:
      elements:
        - name: ecloud9
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: ecloud8
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: ecloud7
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: ecloud6
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: ecloud5
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: ecloud4
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: ecloud3
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: ecloud2
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: ecloud1
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
        - name: testecloud3
          autoSync: true
          prune: true
          project: prod
          namespace: prod
          cluster: prod-cluster
  template:
    metadata:
      name: 'uniware-{{.name}}'
    spec:
      project: '{{.project}}'
      sources:
        - repoURL: 'https://github.com/devops-unicommerce/devops-team-env.git'
          targetRevision: HEAD
          path: uniware/Eclouds/{{.name}}
      destination:
        name: '{{.cluster}}'
        namespace: '{{.namespace}}'
    
  templatePatch: |
    spec:
    {{- if .autoSync }}
      syncPolicy:
        automated:
          prune: {{ .prune }}
          selfHeal: true
        syncOptions:
        - ApplyOutOfSyncOnly=true
    {{- end }}