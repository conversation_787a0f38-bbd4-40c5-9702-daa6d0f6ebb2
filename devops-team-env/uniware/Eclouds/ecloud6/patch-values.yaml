enabled: true
replicaCount: 0
podSecurityContext:
  fsGroup: 1000
pdb:
  enabled: true
  minAvailable: 3
image:
  repository: nexus-docker.unicommerce.infra:8123/uniware
  pullPolicy: Always
  tag: 1710.9-k8s
imagePullSecrets:
  - name: nexus-secrets
service:
  type: ClusterIP
  hazelCastport: 5701
  headless:
    enabled: true
arguments:
  - "sed \"/$HOSTNAME/d\" /usr/local/tomcat/logs/server.xml >  /usr/local/tomcat/conf/server.xml && catalina.sh run"
resources:
  limits:
    cpu: 5.0
    memory: 25Gi
  requests:
    cpu: 5.0
    memory: 25Gi
nodeSelector:
  karpenter.sh/nodepool: eclouds-ondemand-pool
affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app.kubernetes.io/instance: ecloud6
            app.kubernetes.io/name: uniware-api
        topologyKey: kubernetes.io/hostname
tolerations:
  - key: "eclouds/ondemand"
    operator: "Exists"
    effect: "NoSchedule"
init:
  arguments:
    - mkdir -p /tmp/RotatedAppLogs
    - /tmp/files/customizedTemplates
    - /tmp/files/reconciliationInvoices
    - /tmp/files/saleOrderReturn
    - /tmp/files/import/logs
    - /tmp/files/saleOrder
    - /tmp/files/shippingLabel
livenessProbe: {}
readinessProbe:
  exec:
    command:
      - /bin/bash
      - -c
      - |-
        health_status=$(curl --write-out "%{http_code}\n" --max-time 3 -H "Host: baseenterprise6.unicommerce.com" --silent --output /dev/null "http://localhost:8080/open/live")
        if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  failureThreshold: 5
  periodSeconds: 60
  successThreshold: 1
  timeoutSeconds: 30
  initialDelaySeconds: 180
env:
  JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms11000M -Xmx21000M -XX:MetaspaceSize=512M -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -DinventoryDebugLoggingEnabled=true -DtenantSpecificMongoConnectionsMaxSize=150 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud6#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DappIdentifier=${HOSTNAME} -DserverName=ECloud6 -Ds3bucketSuffix=-in -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.ecloud6-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo1.ecloud1-in.unicommerce.infra:27017,mongo2.ecloud1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/ecloud6#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DclusterName=ECloud6 -DactiveMQBrokerUrl=failover:tcp://activemq.ec1-in.unicommerce.infra:61616,tcp://activemq.ec1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=ECloud6 -DactiveMQBrokerPassword=<path:kv/data/uniware/ecloud6#activeMQBrokerPassword> -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -Dcom.sun.management.jmxremote -DAsyncLogger.ThreadNameStrategy=UNCACHED -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -DactiveMQMaxConnections=5 -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 -DactiveMQ2BrokerUrl=failover:tcp://activemq.ec2-in.unicommerce.infra:61616,tcp://activemq.ec2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud6#activeMQBrokerPassword> -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017  -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DexecuteJobs=false -DlogHibernateMemoryUsage=false -DhibernateGenerateStatistics=false -javaagent:/usr/local/tomcat/conf/uniwareConfig/pinpoint-agent-2.5.2/pinpoint-bootstrap-2.5.2.jar -Dpinpoint.agentId=${HOSTNAME} -Dpinpoint.applicationName=ECloud6 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Dhazelcast.local.publicAddress=${HOSTNAME}.prod.svc.cluster.local:5701 -DhazelcastMembers=ecloud6-uniware-api-0.prod.svc.cluster.local:5701,ecloud6-uniware-api-1.prod.svc.cluster.local:5701,ecloud6-uniware-api-2.prod.svc.cluster.local:5701,ecloud6-uniware-api-3.prod.svc.cluster.local:5701,ecloud6-uniware-task-0.ecloud6-uniware-task.prod.svc.cluster.local:5701"
volumeMounts:
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
    subPath: vault.properties
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/vaultConfig
    name: uniware-vault-config
  - mountPath: /usr/local/tomcat/conf/context.xml
    subPath: context.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/server.xml
    subPath: server.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/catalina.sh
    subPath: catalina.sh
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/hazelcast.yml
    subPath: hazelcast.yml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
    subPath: gc_monitor.sh
    name: uniware-config
volumes:
  - name: uniware-config
    configMap:
      name: ecloud6-uniware
      defaultMode: 0777
  - name: uniware-vault-config
    configMap:
      name: ecloud6-vault-uniware
      defaultMode: 0777
ingress:
  enabled: false
  className: "haproxy-public"
  annotations:
    haproxy-ingress.github.io/config-backend: |
      http-request set-header X-Forwarded-Proto https
  hosts:
    - host: aachigroup.unicommerce.com
    - host: futuremakeup.unicommerce.com
    - host: leeza.unicommerce.com
    - host: rentokilpci.unicommerce.com
    - host: dermawear.unicommerce.com
    - host: kidsmandi137.unicommerce.com
    - host: kidsmandi.unicommerce.com
    - host: aarshlifestyle.unicommerce.com
    - host: aarshlifestyle-unireco.unicommerce.com
    - host: aastey.unicommerce.com
    - host: agrictools.unicommerce.com
    - host: ajaykumarkedia.unicommerce.com
    - host: alivewellnessclinics.unicommerce.com
    - host: alluviumbeauty.unicommerce.com
    - host: alpinobutter.unicommerce.com
    - host: alpinohf.unicommerce.com
    - host: alpinohf-unireco.unicommerce.com
    - host: ampletechnologies.unicommerce.com
    - host: ampletech.unicommerce.com
    - host: ampm.unicommerce.com
    - host: anaar.unicommerce.com
    - host: anveshanfarm.unicommerce.com
    - host: apexvitals.unicommerce.com
    - host: aqdus.unicommerce.com
    - host: aroka.unicommerce.com
    - host: aromaindia.unicommerce.com
    - host: artsanaindiapvtltd.unicommerce.com
    - host: astarlanehome.unicommerce.com
    - host: aulilifestyle.unicommerce.com
    - host: bagrrysindia.unicommerce.com
    - host: bagsandbaggage.unicommerce.com
    - host: banjaras.unicommerce.com
    - host: bantomemiza.unicommerce.com
    - host: baseecloud602.unicommerce.com
    - host: baseenterprise6.unicommerce.com
    - host: bcktest11.unicommerce.com
    - host: beautypalaceemiza.unicommerce.com
    - host: biancahome.unicommerce.com
    - host: bianca.unicommerce.com
    - host: bikrikendra.unicommerce.com
    - host: bimalvisions.unicommerce.com
    - host: bioveda.unicommerce.com
    - host: blacktown.unicommerce.com
    - host: blingbrands.unicommerce.com
    - host: blitzlogistics.unicommerce.com
    - host: blitz.unicommerce.com
    - host: bluetea.unicommerce.com
    - host: boycottdenim.unicommerce.com
    - host: brandcraft.unicommerce.com
    - host: brillarescience.unicommerce.com
    - host: bsc.unicommerce.com
    - host: bummer.unicommerce.com
    - host: bushirt24.unicommerce.com
    - host: bushirt24-unireco.unicommerce.com
    - host: cantabilretail.unicommerce.com
    - host: cbpl.unicommerce.com
    - host: cfildronalogitech.unicommerce.com
    - host: citreagrowth.unicommerce.com
    - host: comets.unicommerce.com
    - host: converse.unicommerce.com
    - host: cubeesquire.unicommerce.com
    - host: cultsport.unicommerce.com
    - host: curious.unicommerce.com
    - host: cutiekins.unicommerce.com
    - host: deoleoindia.unicommerce.com
    - host: distributor1.unicommerce.com
    - host: divyakitchenware.unicommerce.com
    - host: dotokids.unicommerce.com
    - host: dreamhomeemart.unicommerce.com
    - host: dsenterprise.unicommerce.com
    - host: duckduckbaby.unicommerce.com
    - host: eaglesupply.unicommerce.com
    - host: ecloud6limitcheck71.unicommerce.com
    - host: ecloud6testnew.unicommerce.com
    - host: ecloud6testtenant2.unicommerce.com
    - host: ecloud6test.unicommerce.com
    - host: elevarsports.unicommerce.com
    - host: emizasellers.unicommerce.com
    - host: emizasupplychain.unicommerce.com
    - host: enventt1.unicommerce.com
    - host: esplorer.unicommerce.com
    - host: eumeworld.unicommerce.com
    - host: fargobags.unicommerce.com
    - host: femisafeindia.unicommerce.com
    - host: flairwriting.unicommerce.com
    - host: flexnest.unicommerce.com
    - host: follicular.unicommerce.com
    - host: forceapparels.unicommerce.com
    - host: fractal.unicommerce.com
    - host: fractal-unireco.unicommerce.com
    - host: gadgetwag.unicommerce.com
    - host: gadgetw.unicommerce.com
    - host: goatfit.unicommerce.com
    - host: goodbrandsindia.unicommerce.com
    - host: gubacci.unicommerce.com
    - host: guiltfree.unicommerce.com
    - host: hangupindia.unicommerce.com
    - host: heranow.unicommerce.com
    - host: hhindia.unicommerce.com
    - host: hikestores.unicommerce.com
    - host: hikestore.unicommerce.com
    - host: hummelindia.unicommerce.com
    - host: hummelindia-unireco.unicommerce.com
    - host: hush.unicommerce.com
    - host: ikc.unicommerce.com
    - host: jagdishtraders.unicommerce.com
    - host: jaipuritecorporation.unicommerce.com
    - host: jcblaccessories.unicommerce.com
    - host: jeyaramabrands.unicommerce.com
    - host: jhakaashul.unicommerce.com
    - host: jimmyluxury.unicommerce.com
    - host: jqrsportsindia.unicommerce.com
    - host: juneshop.unicommerce.com
    - host: kalitrendz.unicommerce.com
    - host: kashe.unicommerce.com
    - host: kayconsumerproducts.unicommerce.com
    - host: kazumilabs.unicommerce.com
    - host: ketofy.unicommerce.com
    - host: khadinatural.unicommerce.com
    - host: kinton.unicommerce.com
    - host: knitkraftimpex.unicommerce.com
    - host: konteraventures.unicommerce.com
    - host: kopojis.unicommerce.com
    - host: kubermart.unicommerce.com
    - host: kurlon.unicommerce.com
    - host: kwe.unicommerce.com
    - host: kwick.unicommerce.com
    - host: laglits.unicommerce.com
    - host: laiqawellness.unicommerce.com
    - host: liningstudiosg.unicommerce.com
    - host: littlebearwellness.unicommerce.com
    - host: logipool.unicommerce.com
    - host: longway.unicommerce.com
    - host: lorenzomoratti.unicommerce.com
    - host: magikal.unicommerce.com
    - host: maharishiayurveda.unicommerce.com
    - host: masonhome.unicommerce.com
    - host: maybell.unicommerce.com
    - host: mcnroe.unicommerce.com
    - host: menmom.unicommerce.com
    - host: monsoonsaloon.unicommerce.com
    - host: naturell.unicommerce.com
    - host: neocor.unicommerce.com
    - host: nestroots.unicommerce.com
    - host: neulookfashion1.unicommerce.com
    - host: nextcareindia.unicommerce.com
    - host: nextenbrands.unicommerce.com
    - host: nobero.unicommerce.com
    - host: nrdconsumers.unicommerce.com
    - host: nutriburst.unicommerce.com
    - host: otrixecom.unicommerce.com
    - host: pakhicommercial.unicommerce.com
    - host: parbhiebeautyllp.unicommerce.com
    - host: pinklay.unicommerce.com
    - host: pinnaclepublications.unicommerce.com
    - host: piramal.unicommerce.com
    - host: protouch.unicommerce.com
    - host: purpledrone.unicommerce.com
    - host: purplepandafashionsltd.unicommerce.com
    - host: purplepompa.unicommerce.com
    - host: quadrant.unicommerce.com
    - host: readiprint.unicommerce.com
    - host: reidandtaylor.unicommerce.com
    - host: retailgrapple.unicommerce.com
    - host: rioebusiness.unicommerce.com
    - host: royalexport.unicommerce.com
    - host: safari.unicommerce.com
    - host: safari-unireco.unicommerce.com
    - host: saltattire.unicommerce.com
    - host: saltattire-unireco.unicommerce.com
    - host: salty.unicommerce.com
    - host: savkventures.unicommerce.com
    - host: savvy.unicommerce.com
    - host: scaleaccessher.unicommerce.com
    - host: scaleluxura.unicommerce.com
    - host: scalepolestar.unicommerce.com
    - host: scalewink.unicommerce.com
    - host: scottinternational.unicommerce.com
    - host: sekhmet.unicommerce.com
    - host: serviceeasy.unicommerce.com
    - host: setunutrition.unicommerce.com
    - host: sfpl.unicommerce.com
    - host: sharktribe.unicommerce.com
    - host: shashank.unicommerce.com
    - host: shivanikafoods.unicommerce.com
    - host: shopbloom.unicommerce.com
    - host: shrikanthart.unicommerce.com
    - host: shruseternity.unicommerce.com
    - host: skinpot.unicommerce.com
    - host: skullcandy.unicommerce.com
    - host: skyinternational.unicommerce.com
    - host: skyland.unicommerce.com
    - host: solarcreations.unicommerce.com
    - host: soothe.unicommerce.com
    - host: sophustechnosol.unicommerce.com
    - host: sprig.unicommerce.com
    - host: stgmyduroflexworld.unicommerce.com
    - host: sukkhifashion.unicommerce.com
    - host: sukkhi.unicommerce.com
    - host: supplyfactory.unicommerce.com
    - host: svminc.unicommerce.com
    - host: swati.unicommerce.com
    - host: tapl.unicommerce.com
    - host: tata1mg.unicommerce.com
    - host: teamonk.unicommerce.com
    - host: tenderskinemiza.unicommerce.com
    - host: theknotcompany.unicommerce.com
    - host: tisyannanaturals.unicommerce.com
    - host: tmrw.unicommerce.com
    - host: tntglobal.unicommerce.com
    - host: todbees.unicommerce.com
    - host: tsf.unicommerce.com
    - host: utkarshclasses.unicommerce.com
    - host: vansaar.unicommerce.com
    - host: varahamurtiflexirub.unicommerce.com
    - host: vedaemiza.unicommerce.com
    - host: venusenterprise.unicommerce.com
    - host: veterisfoods.unicommerce.com
    - host: vipbags.unicommerce.com
    - host: visage.unicommerce.com
    - host: voijeansindia.unicommerce.com
    - host: vrnlifestyle.unicommerce.com
    - host: vtex.unicommerce.com
    - host: wearcomet.unicommerce.com
    - host: wearetto.unicommerce.com
    - host: wellcurve.unicommerce.com
    - host: wellversed.unicommerce.com
    - host: welusa.unicommerce.com
    - host: wildglowcosmetic.unicommerce.com
    - host: wizplexpvtltd.unicommerce.com
    - host: wlthventuresllp.unicommerce.com
    - host: yeshodarosebazaar.unicommerce.com
    - host: zigly.unicommerce.com
    - host: zivafashion.unicommerce.com
  tls: []

istio:
  enabled: true
  
autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 4
  targetCPUUtilizationPercentage: 75
uniware-task:
  enabled: true
  podSecurityContext:
    fsGroup: 1000
  resources:
    limits:
      cpu: 10
      memory: 40.0Gi
    requests:
      cpu: 10
      memory: 40.0Gi
  nodeSelector:
    karpenter.sh/nodepool: eclouds-spot-pool
  tolerations:
    - key: "eclouds/spot"
      operator: "Exists"
      effect: "NoSchedule"
  image:
    repository: nexus-docker.unicommerce.infra:8123/uniware
    pullPolicy: Always
    tag: 1710.9-k8s
  imagePullSecrets:
    - name: nexus-secrets
  init:
    arguments:
      - mkdir -p /tmp/RotatedAppLogs
      - /tmp/files/customizedTemplates
      - /tmp/files/reconciliationInvoices
      - /tmp/files/saleOrderReturn
      - /tmp/files/import/logs
      - /tmp/files/saleOrder
      - /tmp/files/shippingLabel
  livenessProbe: {}
  readinessProbe: {}
  env:
    JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms15300M -Xmx30500M -XX:MetaspaceSize=512M -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -DtenantSpecificMongoConnectionsMaxSize=300 -DcommonMongoConnectionsMaxSize=1000 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud6#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DappIdentifier=app2 -DserverName=ECloud6 -Dhazelcast.local.publicAddress=ecloud6-uniware-task-0.ecloud6-uniware-task.prod.svc.cluster.local:5701 -Ds3bucketSuffix=-in -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DhazelcastMembers=ecloud6-uniware-task-0.ecloud6-uniware-task.prod.svc.cluster.local:5701,ecloud6-uniware-api-0.prod.svc.cluster.local:5701,ecloud6-uniware-api-1.prod.svc.cluster.local:5701,ecloud6-uniware-api-2.prod.svc.cluster.local:5701,ecloud6-uniware-api-3.prod.svc.cluster.local:5701 -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.ecloud6-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo1.ecloud1-in.unicommerce.infra:27017,mongo2.ecloud1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/ecloud6#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DclusterName=ECloud6 -DactiveMQBrokerUrl=failover:tcp://activemq.ec1-in.unicommerce.infra:61616,tcp://activemq.ec1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=ECloud6 -DactiveMQBrokerPassword=<path:kv/data/uniware/ecloud6#activeMQBrokerPassword> -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -Dcom.sun.management.jmxremote -DAsyncLogger.ThreadNameStrategy=UNCACHED -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -DactiveMQMaxConnections=5 -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 -DactiveMQ2BrokerUrl=failover:tcp://activemq.ec2-in.unicommerce.infra:61616,tcp://activemq.ec2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud6#activeMQBrokerPassword> -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DexecuteJobs=true -javaagent:/usr/local/tomcat/conf/uniwareConfig/pinpoint-agent-2.5.2/pinpoint-bootstrap-2.5.2.jar -Dpinpoint.agentId=4 -Dpinpoint.applicationName=ECloud6_2 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource -DlogHibernateMemoryUsage=false -DhibernateGenerateStatistics=false -DrecoSpecificCommonMongoConnectionsMaxSize=50"
  volumeMounts:
    - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
      subPath: vault.properties
      name: uniware-config
    - mountPath: /usr/local/tomcat/logs/vaultConfig
      name: uniware-vault-config
    - mountPath: /usr/local/tomcat/conf/context.xml
      subPath: context.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/conf/server.xml
      subPath: server.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/catalina.sh
      subPath: catalina.sh
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/hazelcast.yml
      subPath: hazelcast.yml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
      subPath: gc_monitor.sh
      name: uniware-config
  volumes:
    - name: uniware-config
      configMap:
        name: ecloud6-uniware
        defaultMode: 0777
    - name: uniware-vault-config
      configMap:
        name: ecloud6-vault-uniware
        defaultMode: 0777
  service:
    hazelCastport: 5701
    type: ClusterIP
    headless:
      enabled: true
global:
  filebeat:
    config:
      name: ${PODNAME}.ecloud6-in.unicommerce.infra
      hosts:
        - "logstash1-in.unicommerce.infra:5044"
        - "logstash2-in.unicommerce.infra:5044"
  promtail:
    config:
      clients:
        - url: http://loki-gateway.monitoring.svc.cluster.local:80/loki/api/v1/push
          tenant_id: 'ecloud6'
  efs:
    enabled: false


