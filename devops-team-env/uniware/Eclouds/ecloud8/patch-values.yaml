enabled: true
replicaCount: 0
podSecurityContext:
  fsGroup: 1000
pdb:
  enabled: true
  minAvailable: 3
image:
  repository: nexus-docker.unicommerce.infra:8123/uniware
  pullPolicy: Always
  tag: 1712.4-k8s
imagePullSecrets:
  - name: nexus-secrets
service:
  type: ClusterIP
  hazelCastport: 5701
  headless:
    enabled: true
arguments:
  - "sed \"/$HOSTNAME/d\" /usr/local/tomcat/logs/server.xml >  /usr/local/tomcat/conf/server.xml && catalina.sh run"
resources:
  limits:
    cpu: 3.4
    memory: 20Gi
  requests:
    cpu: 3.4
    memory: 20Gi
nodeSelector:
  karpenter.sh/nodepool: eclouds-ondemand-pool
affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app.kubernetes.io/instance: ecloud8
            app.kubernetes.io/name: uniware-api
        topologyKey: kubernetes.io/hostname
tolerations:
  - key: "eclouds/ondemand"
    operator: "Exists"
    effect: "NoSchedule"
init:
  arguments:
    - mkdir -p /tmp/RotatedAppLogs
    - /tmp/files/customizedTemplates
    - /tmp/files/reconciliationInvoices
    - /tmp/files/saleOrderReturn
    - /tmp/files/import/logs
    - /tmp/files/saleOrder
    - /tmp/files/shippingLabel
livenessProbe: {}
readinessProbe:
  exec:
    command:
      - /bin/bash
      - -c
      - |-
        health_status=$(curl --write-out "%{http_code}\n" --max-time 3 -H "Host: baseenterprise8.unicommerce.com" --silent --output /dev/null "http://localhost:8080/open/live")
        if [[ ${health_status} -ne 200 ]]; then exit 1; fi
  failureThreshold: 5
  periodSeconds: 60
  successThreshold: 1
  timeoutSeconds: 30
  initialDelaySeconds: 180
env:
  JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms8000M -Xmx16000M -XX:MetaspaceSize=512M -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud8#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DserverName=ECloud8 -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -Ds3bucketSuffix=-in -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.ecloud8-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo1.ecloud1-in.unicommerce.infra:27017,mongo2.ecloud1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/ecloud8#activeMQBrokerPassword> -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DclusterName=ECloud8 -DactiveMQBrokerUrl=failover:tcp://activemq.ec1-in.unicommerce.infra:61616,tcp://activemq.ec1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=ECloud8 -DactiveMQBrokerPassword=<path:kv/data/uniware/ecloud8#activeMQBrokerPassword> -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -Dcom.sun.management.jmxremote -DappIdentifier=${HOSTNAME} -DAsyncLogger.ThreadNameStrategy=UNCACHED -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -DactiveMQMaxConnections=5 -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 -DactiveMQ2BrokerUrl=failover:tcp://activemq.ec2-in.unicommerce.infra:61616,tcp://activemq.ec2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud8#activeMQBrokerPassword> -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -javaagent:/usr/local/tomcat/conf/uniwareConfig/pinpoint-agent-2.5.2/pinpoint-bootstrap-2.5.2.jar -Dpinpoint.agentId=${HOSTNAME} -Dpinpoint.applicationName=ECloud8 -Dpinpoint.applicationName=ECloud8 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -DexecuteJobs=false -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DexecuteJobs=false -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -DtenantSpecificMongoConnectionsMaxSize=50 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Dhazelcast.local.publicAddress=${HOSTNAME}.prod.svc.cluster.local:5701 -DhazelcastMembers=ecloud8-uniware-api-0.prod.svc.cluster.local:5701,ecloud8-uniware-api-1.prod.svc.cluster.local:5701,ecloud8-uniware-api-2.prod.svc.cluster.local:5701,ecloud8-uniware-api-3.prod.svc.cluster.local:5701,ecloud8-uniware-task-0.ecloud8-uniware-task.prod.svc.cluster.local:5701"
volumeMounts:
  - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
    subPath: vault.properties
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/vaultConfig
    name: uniware-vault-config
  - mountPath: /usr/local/tomcat/conf/context.xml
    subPath: context.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/logs/server.xml
    subPath: server.xml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/catalina.sh
    subPath: catalina.sh
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/hazelcast.yml
    subPath: hazelcast.yml
    name: uniware-config
  - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
    subPath: gc_monitor.sh
    name: uniware-config
volumes:
  - name: uniware-config
    configMap:
      name: ecloud8-uniware
      defaultMode: 0777
  - name: uniware-vault-config
    configMap:
      name: ecloud8-vault-uniware
      defaultMode: 0777
ingress:
  enabled: false
  className: "haproxy-public"
  annotations:
    haproxy-ingress.github.io/config-backend: |
      http-request set-header X-Forwarded-Proto https
  hosts:
    - host: airboxindia.unicommerce.co.in
    - host: recodestudio-unireco.unicommerce.co.in
    - host: tryecloud9.unicommerce.co.in
    - host: myhyuman.unicommerce.co.in
    - host: patchuphealth.unicommerce.co.in
    - host: modasinternational.unicommerce.co.in
    - host: zummer.unicommerce.co.in
    - host: demozastores.unicommerce.co.in
    - host: kilrr.unicommerce.co.in
    - host: antara.unicommerce.co.in
    - host: finalmiletechies.unicommerce.co.in
    - host: recodestudio.unicommerce.co.in
    - host: blubein.unicommerce.co.in
    - host: glenappliances.unicommerce.co.in
    - host: turaturibrands.unicommerce.co.in
    - host: karwan1.unicommerce.co.in
    - host: timehousein.unicommerce.co.in
    - host: homemondelifestyle.unicommerce.co.in
    - host: superkicksindia.unicommerce.co.in
    - host: jhakaas2.unicommerce.co.in
    - host: fnp.unicommerce.co.in
    - host: alvami.unicommerce.co.in
    - host: aquilabrands.unicommerce.co.in
    - host: arali.unicommerce.co.in
    - host: arapparels.unicommerce.co.in
    - host: atozbooks.unicommerce.co.in
    - host: aurainebotanicals.unicommerce.co.in
    - host: avainternational.unicommerce.co.in
    - host: babyorganoindia.unicommerce.co.in
    - host: babyorganoin.unicommerce.co.in
    - host: baghindia.unicommerce.co.in
    - host: baidyanathayurved.unicommerce.co.in
    - host: baksondrugs.unicommerce.co.in
    - host: bansalenterprises.unicommerce.co.in
    - host: baseenterprise8.unicommerce.com
    - host: beautyessentials.unicommerce.co.in
    - host: beromt.unicommerce.co.in
    - host: bersache.unicommerce.co.in
    - host: bimlatraders.unicommerce.co.in
    - host: birdsofparadyes.unicommerce.co.in
    - host: bluesquadfashion.unicommerce.co.in
    - host: bodhiecommerce.unicommerce.co.in
    - host: bodhiecommerce-unireco.unicommerce.co.in
    - host: bombaytrooperx.unicommerce.co.in
    - host: bonkaso.unicommerce.co.in
    - host: breathehygiene.unicommerce.co.in
    - host: breretail.unicommerce.co.in
    - host: breretail-unireco.unicommerce.co.in
    - host: bsf.unicommerce.co.in
    - host: bsf-unireco.unicommerce.co.in
    - host: bsf-unireco.unicommerce.com
    - host: bundlejoy.unicommerce.co.in
    - host: buymaxwellness.unicommerce.co.in
    - host: cephus.unicommerce.co.in
    - host: cforcovers.unicommerce.co.in
    - host: checkout24.unicommerce.co.in
    - host: conceptkart.unicommerce.co.in
    - host: cotsandcuddles.unicommerce.co.in
    - host: dcyphr.unicommerce.co.in
    - host: deepam.unicommerce.co.in
    - host: divyaknitwears.unicommerce.co.in
    - host: dronalogitechmulti.unicommerce.co.in
    - host: ecloud8test19apr.unicommerce.co.in
    - host: ecloudtest15.unicommerce.co.in
    - host: ecloudtest16.unicommerce.co.in
    - host: ekhifashions.unicommerce.co.in
    - host: emotorad.unicommerce.co.in
    - host: faebeauty.unicommerce.co.in
    - host: fashioncloset.unicommerce.co.in
    - host: fashioncloset-unireco.unicommerce.co.in
    - host: fausto1.unicommerce.co.in
    - host: fkart.unicommerce.co.in
    - host: francoleonefl.unicommerce.co.in
    - host: francoleone.unicommerce.co.in
    - host: fullfill.unicommerce.co.in
    - host: ganganamstreet.unicommerce.co.in
    - host: grodium.unicommerce.co.in
    - host: habereindia.unicommerce.co.in
    - host: hammondsflycatcher.unicommerce.co.in
    - host: hammondsflycatcher-unireco.unicommerce.co.in
    - host: hammondsflycatcher-unireco.unicommerce.com
    - host: hasanoud.unicommerce.co.in
    - host: hasbro.unicommerce.co.in
    - host: healthnetglobal.unicommerce.co.in
    - host: home360.unicommerce.co.in
    - host: homeessentials.unicommerce.co.in
    - host: homeessentials-unireco.unicommerce.co.in
    - host: homeessentials-unireco.unicommerce.com
    - host: iconlifestyle.unicommerce.co.in
    - host: irishinnerwears.unicommerce.co.in
    - host: jagwonder.unicommerce.co.in
    - host: jamoti.unicommerce.co.in
    - host: jolger.unicommerce.co.in
    - host: jurasourcingindia.unicommerce.co.in
    - host: jwapparel.unicommerce.co.in
    - host: k2ethnic.unicommerce.co.in
    - host: khadisadan.unicommerce.co.in
    - host: kisna.unicommerce.co.in
    - host: klubfox.unicommerce.co.in
    - host: krishaenterprise.unicommerce.co.in
    - host: krisha.unicommerce.co.in
    - host: landmarkenterprisesent.unicommerce.co.in
    - host: lazyshark.unicommerce.co.in
    - host: letsbeco.unicommerce.co.in
    - host: lovechild.unicommerce.co.in
    - host: mallzee.unicommerce.co.in
    - host: massdeals.unicommerce.co.in
    - host: meemansa.unicommerce.co.in
    - host: mobikasa.unicommerce.co.in
    - host: modelink.unicommerce.co.in
    - host: mokobarauae.unicommerce.co.in
    - host: mokostg.unicommerce.co.in
    - host: moonraylifestyle.unicommerce.co.in
    - host: moosegear.unicommerce.co.in
    - host: namanartsin.unicommerce.co.in
    - host: nayasaecom.unicommerce.co.in
    - host: ncc.unicommerce.co.in
    - host: necesera.unicommerce.co.in
    - host: neraglobalstyle.unicommerce.co.in
    - host: newec8tenant.unicommerce.co.in
    - host: nexten.unicommerce.co.in
    - host: neyu.unicommerce.co.in
    - host: nirmita.unicommerce.co.in
    - host: nirmita-unireco.unicommerce.co.in
    - host: nirmita-unireco.unicommerce.com
    - host: ojb.unicommerce.co.in
    - host: okaya.unicommerce.co.in
    - host: omvedlifestyle.unicommerce.co.in
    - host: omved.unicommerce.co.in
    - host: onequince.unicommerce.co.in
    - host: onesky.unicommerce.co.in
    - host: oppdoor.unicommerce.co.in
    - host: orangesugar.unicommerce.co.in
    - host: ozonesecutech.unicommerce.co.in
    - host: paizlienama.unicommerce.co.in
    - host: parasnet.unicommerce.co.in
    - host: peachmode.unicommerce.co.in
    - host: peepultree.unicommerce.co.in
    - host: pharmasource.unicommerce.co.in
    - host: piitest00.unicommerce.co.in
    - host: piitest31.unicommerce.co.in
    - host: piitest32.unicommerce.co.in
    - host: pintolaindia.unicommerce.co.in
    - host: pinza.unicommerce.co.in
    - host: pkdream.unicommerce.co.in
    - host: prashantadvait.unicommerce.co.in
    - host: prmgpni.unicommerce.co.in
    - host: procraft.unicommerce.co.in
    - host: puer.unicommerce.co.in
    - host: rcpl.unicommerce.co.in
    - host: rhardikprint.unicommerce.co.in
    - host: rigsandrags.unicommerce.co.in
    - host: royaloakinc.unicommerce.co.in
    - host: ruhesolutions.unicommerce.co.in
    - host: sachdeva.unicommerce.co.in
    - host: sagedretailprivatelimited.unicommerce.co.in
    - host: salasartextiles.unicommerce.co.in
    - host: salient.unicommerce.co.in
    - host: samayra.unicommerce.co.in
    - host: sammmm.unicommerce.co.in
    - host: sampuranswadeshi.unicommerce.co.in
    - host: sanfe1.unicommerce.co.in
    - host: sdffashionworld.unicommerce.co.in
    - host: sf123.unicommerce.co.in
    - host: shaktibrandz1.unicommerce.co.in
    - host: shaktibrandz1-unireco.unicommerce.co.in
    - host: shifto.unicommerce.co.in
    - host: shivaystyle.unicommerce.co.in
    - host: shopnek.unicommerce.co.in
    - host: shoptrend.unicommerce.co.in
    - host: shreemahalasa.unicommerce.co.in
    - host: singerindia.unicommerce.co.in
    - host: singer.unicommerce.co.in
    - host: skenterprise24.unicommerce.co.in
    - host: sleepyhug.unicommerce.co.in
    - host: slenorapparels.unicommerce.co.in
    - host: spectraacare.unicommerce.co.in
    - host: sportking.unicommerce.co.in
    - host: ssbenterprise8.unicommerce.co.in
    - host: ssonlineservices.unicommerce.co.in
    - host: stellarpd.unicommerce.co.in
    - host: stginfiniversity.unicommerce.co.in
    - host: supplysix.unicommerce.co.in
    - host: surabhit.unicommerce.co.in
    - host: swaggindiaent.unicommerce.co.in
    - host: swaggindia.unicommerce.co.in
    - host: technosports.unicommerce.co.in
    - host: technosports-unireco.unicommerce.co.in
    - host: testtenant10.unicommerce.co.in
    - host: testtenant9.unicommerce.co.in
    - host: thehouseofthings.unicommerce.co.in
    - host: themoonstore.unicommerce.co.in
    - host: tidysleeponline.unicommerce.co.in
    - host: tomtun.unicommerce.co.in
    - host: travozet.unicommerce.co.in
    - host: trendia.unicommerce.co.in
    - host: ugees.unicommerce.co.in
    - host: ultrahuman.unicommerce.co.in
    - host: utlitymall.unicommerce.co.in
    - host: v2kart.unicommerce.co.in
    - host: vegaauto.unicommerce.co.in
    - host: virgio.unicommerce.co.in
    - host: voltedge.unicommerce.co.in
    - host: voyage
    - host: voyage.unicommerce.co.in
    - host: voyage-unireco.unicommerce.co.in
    - host: voyage-unireco.unicommerce.com
    - host: waangoo.unicommerce.co.in
    - host: waaree.unicommerce.co.in
    - host: wellnessvillepvtltd.unicommerce.co.in
    - host: winerd.unicommerce.co.in
    - host: winza.unicommerce.co.in
    - host: woodenstreet.unicommerce.co.in
    - host: yagnikfashion.unicommerce.co.in
    - host: zadley.unicommerce.co.in
  tls: []

istio:
  enabled: true
  
autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 4
  targetCPUUtilizationPercentage: 75
uniware-task:
  enabled: true
  podSecurityContext:
    fsGroup: 1000
  resources:
    limits:
      cpu: 12
      memory: 26.4Gi
    requests:
      cpu: 12
      memory: 26.4Gi
  nodeSelector:
    karpenter.sh/nodepool: eclouds-spot-pool
  tolerations:
    - key: "eclouds/spot"
      operator: "Exists"
      effect: "NoSchedule"
  image:
    repository: nexus-docker.unicommerce.infra:8123/uniware
    pullPolicy: Always
    tag: 1712.4-k8s
  imagePullSecrets:
    - name: nexus-secrets
  init:
    arguments:
      - mkdir -p /tmp/RotatedAppLogs
      - /tmp/files/customizedTemplates
      - /tmp/files/reconciliationInvoices
      - /tmp/files/saleOrderReturn
      - /tmp/files/import/logs
      - /tmp/files/saleOrder
      - /tmp/files/shippingLabel
  livenessProbe: {}
  readinessProbe: {}
  env:
    JAVA_OPTS: "-javaagent:/usr/local/apache-tomcat/UniwareInstrumentation-1.0-jar-with-dependencies.jar -Xms10512M -Xmx18800M -XX:MetaspaceSize=512M -Denv=c1 -DelasticSearchServerUrl=elasticsearch1.unicommerce.infra:9300 -Duser.timezone=Asia/Kolkata -DprefetchActiveMQ2MaxConnections=5 -DprefetchActiveMQ2QueuePrefetch=5 -DprefetchActiveMQ2BrokerUsername=admin -DcommonMongoConnectionsMaxSize=1000 -DtenantSpecificMongoConnectionsMaxSize=300 -DprefetchActiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud8#prefetchActiveMQ2BrokerPassword> -DprefetchActiveMQ2BrokerUrl=failover:tcp://prefetch.unicommerce.infra:61616 -DserverName=ECloud8 -Dhazelcast.config=/usr/local/apache-tomcat/bin/hazelcast.yml -DzookeeperConnectionTimeout=15000 -DzookeeperSessionTimeout=60000 -DzookeeperUrl=zookeeper.ecloud8-in.unicommerce.infra:2181 -DtenantSpecificMongoHosts=mongo1.ecloud1-in.unicommerce.infra:27017,mongo2.ecloud1-in.unicommerce.infra:27017 -DcommonMongoHosts=common1.mongo.unicommerce.infra:27017,common2.mongo.unicommerce.infra:27017 -DtraceLogActiveMQMaxConnections=2 -DtraceLogActiveMQQueuePrefetch=5 -DtraceLogActiveMQBrokerUsername=unicommercemq -DtraceLogActiveMQBrokerPassword=<path:kv/data/uniware/ecloud8#activeMQBrokerPassword>  -DtraceLogActiveMQBrokerUrl=failover:tcp://traceactivemq-in.unicommerce.infra:61616 -DclusterName=ECloud8 -DactiveMQBrokerUrl=failover:tcp://activemq.ec1-in.unicommerce.infra:61616,tcp://activemq.ec1-in.unicommerce.infra:61616 -DactiveMQBrokerUsername=unicommercemq -DserverNames=ECloud8 -DactiveMQBrokerPassword=<path:kv/data/uniware/ecloud8#activeMQBrokerPassword>  -DsolrServerUrl=http://solr1.unicommerce.infra:8983/solr -Dcom.sun.management.jmxremote -DAsyncLogger.ThreadNameStrategy=UNCACHED -Dcom.sun.management.jmxremote.port=6969 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -DactiveMQMaxConnections=5 -Ds3bucketSuffix=-in -DactiveMQQueuePrefetch=1 -DquartzThreadPoolSize=10 -DactiveMQ2BrokerUrl=failover:tcp://activemq.ec2-in.unicommerce.infra:61616,tcp://activemq.ec2-in.unicommerce.infra:61616 -DactiveMQ2BrokerUsername=unicommercemq -DactiveMQ2BrokerPassword=<path:kv/data/uniware/ecloud8#activeMQBrokerPassword>  -DactiveMQ2MaxConnections=5 -DactiveMQ2QueuePrefetch=1 -DinvoiceMQBrokerUrl=failover:tcp://activemq4.unicommerce.infra:61616 -DinvoiceMQBrokerUsername= -DinvoiceMQBrokerPassword -DinvoiceMQMaxConnections=5 -DinvoiceMQQueuePrefetch=1 -javaagent:/usr/local/tomcat/conf/uniwareConfig/pinpoint-agent-2.5.2/pinpoint-bootstrap-2.5.2.jar -Dpinpoint.agentId=EC8-2 -Dpinpoint.applicationName=ECloud8_2 -DrecoSpecificCommonMongoHosts=mongo1.reco-in.unicommerce.infra:27017 -DexecuteJobs=true -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -Xloggc:/usr/local/apache-tomcat/logs/gc.log.$(date +%Y_%m_%d) -XX:OnOutOfMemoryError=/bin/HeapDump -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-LoopUnswitching -DdataSource=UniwareHikariDataSource -DledgerMongoHosts=mongo-ledger.unicommerce.infra:27017 -DrecoSpecificCommonMongoConnectionsMaxSize=50 -Dhazelcast.local.publicAddress=ecloud8-uniware-task-0.ecloud8-uniware-task.prod.svc.cluster.local:5701 -DhazelcastMembers=ecloud8-uniware-api-0.prod.svc.cluster.local:5701,ecloud8-uniware-api-1.prod.svc.cluster.local:5701,ecloud8-uniware-api-2.prod.svc.cluster.local:5701,ecloud8-uniware-api-3.prod.svc.cluster.local:5701,ecloud8-uniware-task-0.ecloud8-uniware-task.prod.svc.cluster.local:5701"
  volumeMounts:
    - mountPath: /usr/local/tomcat/conf/uniwareConfig/vault.properties
      subPath: vault.properties
      name: uniware-config
    - mountPath: /usr/local/tomcat/logs/vaultConfig
      name: uniware-vault-config
    - mountPath: /usr/local/tomcat/conf/context.xml
      subPath: context.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/conf/server.xml
      subPath: server.xml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/catalina.sh
      subPath: catalina.sh
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/hazelcast.yml
      subPath: hazelcast.yml
      name: uniware-config
    - mountPath: /usr/local/tomcat/bin/gc_monitor.sh
      subPath: gc_monitor.sh
      name: uniware-config
  volumes:
    - name: uniware-config
      configMap:
        name: ecloud8-uniware
        defaultMode: 0777
    - name: uniware-vault-config
      configMap:
        name: ecloud8-vault-uniware
        defaultMode: 0777
  service:
    hazelCastport: 5701
    type: ClusterIP
    headless:
      enabled: true
global:
  filebeat:
    config:
      name: ${PODNAME}.ecloud8-in.unicommerce.infra
      hosts:
        - "logstash1-in.unicommerce.infra:5044"
        - "logstash2-in.unicommerce.infra:5044"
  promtail:
    config:
      clients:
        - url: http://loki-gateway.monitoring.svc.cluster.local:80/loki/api/v1/push
          tenant_id: 'ecloud8'
  efs:
    enabled: false
