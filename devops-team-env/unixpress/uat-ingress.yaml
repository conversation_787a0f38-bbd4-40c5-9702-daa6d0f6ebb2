# apiVersion: networking.k8s.io/v1
# kind: Ingress
# metadata:
#   name: uat-seller-xpress-ingress
#   namespace: default
#   annotations:
#     haproxy-ingress.github.io/server-alias-regex: "^[^.]+-uatunixpress\\.unicommerce\\.co\\.in$"
#     haproxy-ingress.github.io/config-backend: |
#       http-request set-header X-Forwarded-Proto https
# spec:
#   ingressClassName: haproxy-public
#   rules:
#   - host: "qa-uatunixpress.unicommerce.co.in"
#     http:
#       paths:

#       - path: /rest
#         pathType: ImplementationSpecific
#         backend:
#           service:
#             name: uat-xpress-sca-service
#             port:
#               number: 8080      
#       - path: /
#         pathType: Prefix
#         backend:
#           service:
#             name: uat-xpress-seller-service
#             port:
#               number: 8080
# ---
# apiVersion: networking.k8s.io/v1
# kind: Ingress
# metadata:
#   name: uat-admin-xpress-ingress
#   namespace: default
# spec:
#   ingressClassName: haproxy-public
#   rules:
#   - host: "admin-uatunixpress.unicommerce.co.in"
#     http:
#       paths:
#       - path: /
#         pathType: Prefix
#         backend:
#           service:
#             name: uat-xpress-admin-service
#             port:
#               number: 8181
---
apiVersion: networking.istio.io/v1
kind: VirtualService
metadata:
  name:  uat-admin-xpress-vs
  namespace: default
spec:
  gateways:
  - istio-system/ingress-gateway
  hosts:
  - admin-uatunixpress.unicommerce.co.in
  http:
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: uat-xpress-admin-service
        port:
          number: 8181
    timeout: 480s
---
apiVersion: networking.istio.io/v1
kind: VirtualService
metadata:
  name: uat-seller-xpress-vs
  namespace: default
spec:
  gateways:
  - istio-system/ingress-gateway
  hosts:
  - '*-uatunixpress.unicommerce.co.in'
  http:
  - headers:
      request:
        set:
          X-Forwarded-Proto: https
    match:
    - uri:
        prefix: /rest
    route:
    - destination:
        host: uat-xpress-sca-service
        port:
          number: 8080
    timeout: 480s
  - headers:
      request:
        set:
          X-Forwarded-Proto: https
    match:
    - uri:
        prefix: /
    route:
    - destination:
        host: uat-xpress-seller-service
        port:
          number: 8080
    timeout: 480s