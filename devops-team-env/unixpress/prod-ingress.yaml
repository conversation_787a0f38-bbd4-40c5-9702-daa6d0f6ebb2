# apiVersion: networking.k8s.io/v1
# kind: Ingress
# metadata:
#   name: prod-seller-xpress-ingress
#   namespace: unixpress-production
#   annotations:
#     haproxy-ingress.github.io/server-alias-regex: "^[^.]+-unixpress\\.unicommerce\\.co\\.in$"
#     haproxy-ingress.github.io/config-backend: |
#       http-request set-header X-Forwarded-Proto https
# spec:
#   ingressClassName: haproxy-public
#   rules:
#   - host: "qa-unixpress.unicommerce.co.in"
#     http:
#       paths:

#       - path: /rest
#         pathType: ImplementationSpecific
#         backend:
#           service:
#             name: prod-unixpress-sca-service
#             port:
#               number: 8080 

#       - path: /
#         pathType: Prefix
#         backend:
#           service:
#             name: prod-unixpress-seller-service
#             port:
#               number: 8080
# ---
# apiVersion: networking.k8s.io/v1
# kind: Ingress
# metadata:
#   name: prod-admin-xpress-ingress
#   namespace: unixpress-production
# spec:
#   ingressClassName: haproxy-public
#   rules:
#   - host: "admin-unixpress.unicommerce.co.in"
#     http:
#       paths:
#       - path: /
#         pathType: Prefix
#         backend:
#           service:
#             name: prod-unixpress-admin-service
#             port:
#               number: 8181
---
apiVersion: networking.istio.io/v1
kind: VirtualService
metadata:
  name:  prod-admin-xpress-vs
  namespace: unixpress-production
spec:
  gateways:
  - istio-system/ingress-gateway
  hosts:
  - admin-unixpress.unicommerce.co.in
  http:
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: prod-unixpress-admin-service
        port:
          number: 8181
    timeout: 480s
---
apiVersion: networking.istio.io/v1
kind: VirtualService
metadata:
  name:  prod-seller-xpress-vs
  namespace: unixpress-production
spec:
  gateways:
  - istio-system/ingress-gateway
  hosts:
  - '*-unixpress.unicommerce.co.in'
  http:
  - headers:
      request:
        set:
          X-Forwarded-Proto: https
    match:
    - uri:
        prefix: /rest
    route:
    - destination:
        host: prod-unixpress-sca-service
        port:
          number: 8080
    timeout: 480s
  - headers:
      request:
        set:
          X-Forwarded-Proto: https
    match:
    - uri:
        prefix: /
    route:
    - destination:
        host: prod-unixpress-seller-service
        port:
          number: 8080
    timeout: 480s