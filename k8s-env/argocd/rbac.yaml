apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-rbac-cm
  namespace: argocd
data:
  policy.csv: |
    p, role:developer, applications, *, staging/*, allow
    p, role:developer, applications, *, dev/*, allow
    p, role:developer, applications, get, prod/*, allow
    p, role:developer, exec, create, *, allow
    p, role:developer, projects, get, *, allow
    g, admins, role:admin
    g, developer, role:developer
  policy.default: deny