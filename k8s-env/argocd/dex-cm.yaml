apiVersion: v1
data:
  resource.customizations.ignoreDifferences.admissionregistration.k8s.io_ValidatingWebhookConfiguration: |
    jqPathExpressions:
    - '.webhooks[]?.failurePolicy'
  application.instanceLabelKey: argocd.argoproj.io/instance
  kustomize.buildOptions: --enable-helm --load-restrictor LoadRestrictionsNone
  exec.enabled: "true"
  repositories: |
    - url: https://github.com/devops-unicommerce/k8s-env.git
      type: git
      name: k8s-env
      usernameSecret:
        name: k8s-env-repo-secret
        key: username
      passwordSecret:
        name: k8s-env-repo-secret
        key: password
    - url: https://github.com/devops-unicommerce/proxy.git
      type: git
      name: k8s-env
      usernameSecret:
        name: k8s-env-repo-secret
        key: username
      passwordSecret:
        name: k8s-env-repo-secret
        key: password
    - url: https://github.com/devops-unicommerce/devops-team-env.git
      type: git
      name: devops-team-env
      usernameSecret:
        name: k8s-env-repo-secret
        key: username
      passwordSecret:
        name: k8s-env-repo-secret
        key: password
    - url: https://github.com/devops-unicommerce/qa-team-env.git
      type: git
      name: qa-team-env
      usernameSecret:
        name: k8s-env-repo-secret
        key: username
      passwordSecret:
        name: k8s-env-repo-secret
        key: password
    - url: https://chartmuseum.unicommerce.co.in
      type: helm
      name: chartmuseum
      usernameSecret:
        name: k8s-env-repo-secret
        key: helm-user
      passwordSecret:
        name: k8s-env-repo-secret
        key: helm-pass
  dex.config: |-
    connectors:
    - type: ldap
      name: SSO login
      id: ad
      config:
        host: ipa.unicommerce.infra:389
        insecureNoSSL: true
        insecureSkipVerify: true
        bindDN: "uid=admin,cn=users,cn=accounts,dc=ipa,dc=unicommerce,dc=infra"
        bindPW: "$dex.ldap.bindPW"
        userSearch:
          baseDN: "cn=users,cn=accounts,dc=ipa,dc=unicommerce,dc=infra"
          filter: ""
          username: uid
          idAttr: uid
          emailAttr: mail
          nameAttr: givenName
          preferredUsernameAttr: uid
        groupSearch:
          baseDN: "cn=groups,cn=compat,dc=ipa,dc=unicommerce,dc=infra"
          filter: "(objectClass=posixGroup)"
          userMatchers:
          - userAttr: uid
            groupAttr: memberUid
          nameAttr: cn
  url: https://argocd.unicommerce.co.in
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/name: argocd-cm
    app.kubernetes.io/part-of: argocd
  name: argocd-cm
  namespace: argocd
