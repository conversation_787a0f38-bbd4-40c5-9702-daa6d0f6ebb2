apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: projects-application-set
  namespace: argocd
spec:
  syncPolicy:
    preserveResourcesOnDeletion: true
  generators:
    - git:
        repoURL: 'https://github.com/devops-unicommerce/k8s-env.git'
        revision: HEAD
        directories:
          - path: 'projects'
  template:
    metadata:
      name: '{{path.basename}}'
    spec:
      project: infra
      source:
        repoURL: 'https://github.com/devops-unicommerce/k8s-env.git'
        targetRevision: HEAD
        path: '{{path}}'
      destination:
        server: 'https://kubernetes.default.svc'
        namespace: argocd
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
