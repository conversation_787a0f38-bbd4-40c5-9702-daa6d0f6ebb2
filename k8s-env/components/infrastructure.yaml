apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  labels:
    app.kubernetes.io/instance: root
  name: infrastructure
  namespace: argocd
spec:
  destination:
    namespace: argocd
    server: https://kubernetes.default.svc
  project: infra
  source:
    path: infrastructure
    repoURL: https://github.com/devops-unicommerce/k8s-env.git
    targetRevision: HEAD
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - ApplyOutOfSyncOnly=true
status:
  sync:
    comparedTo:
      destination:
        namespace: argocd
        server: https://kubernetes.default.svc
      source:
        path: infrastructure
        repoURL: https://github.com/devops-unicommerce/k8s-env.git
        targetRevision: HEAD