apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: infra
  namespace: argocd
spec:
  description: Devops Team Projects (Infra)
  sourceRepos:
    - 'https://grafana.github.io/helm-charts'
    - 'https://github.com/devops-unicommerce/k8s-env.git'
    - 'https://prometheus-community.github.io/helm-charts'
    - 'https://charts.appuio.ch'
    - 'https://haproxytech.github.io/helm-charts'
    - 'https://aws.github.io/eks-charts'
    - 'https://chartmuseum.github.io/charts'
    - 'https://helm.releases.hashicorp.com'
    - 'https://chartmuseum.unicommerce.co.in'
    - 'https://kubernetes.github.io/dashboard'
    - 'https://charts.karpenter.sh'
    - 'https://haproxy-ingress.github.io/charts'
    - 'https://charts.dexidp.io'
    - 'https://oauth2-proxy.github.io/manifests'
    - 'https://istio-release.storage.googleapis.com/charts'
    - 'https://charts.bitnami.com/bitnami'
  destinations:
    - namespace: '*'
      server: '*'
  clusterResourceWhitelist:
    - group: '*'
      kind: '*'
