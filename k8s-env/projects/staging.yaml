apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: staging
  namespace: argocd
spec:
  description: Staging team Projects
  permitOnlyProjectScopedClusters: true
  sourceRepos:
    - 'https://github.com/devops-unicommerce/devops-team-env.git'
    - 'https://chartmuseum.unicommerce.co.in'
  destinations:
    - namespace: '*'
      server: '*'
  clusterResourceWhitelist:
  - group: ''
    kind: Namespace
  - group: ''
    kind: PersistentVolume
  sourceNamespaces:
  - "staging"