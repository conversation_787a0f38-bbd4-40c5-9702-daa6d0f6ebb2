rbac:
  create: true

serviceAccount:
  create: true
  automountServiceAccountToken: true

nameOverride: ""
fullnameOverride: ""

controller:
  ingressClass: haproxy-private
  image:
    repository: quay.io/jcmoraisjr/haproxy-ingress
    tag: v0.14.7
    pullPolicy: IfNotPresent
  
  updateStrategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 2
  
  publishService:
    enabled: true
  
  extraArgs:
    enable-endpointslices-api: true
    report-node-internal-ip-address:

  
  ingressClassResource:
    enabled: true
    default: false
    controllerClass: "haproxy-private"
    parameters: {}

  healthzPort: 10253

  livenessProbe:
    path: /healthz
    port: 10253
    failureThreshold: 3
    initialDelaySeconds: 60
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1

  readinessProbe:
    path: /healthz
    port: 10253
    failureThreshold: 3
    initialDelaySeconds: 10
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1

  automountServiceAccountToken: true

  hostNetwork: false
  dnsPolicy: ClusterFirst

  terminationGracePeriodSeconds: 60

  kind: Deployment

  containerPorts:
    http: 80
    https: 443

  enableStaticPorts: true

  minReadySeconds: 0

  replicaCount: 0

  minAvailable: 1

  resources:
   limits:
     cpu: 500m
     memory: 2Gi
   requests:
     cpu: 500m
     memory: 2Gi

  autoscaling:
    enabled: false
    minReplicas: 2
    maxReplicas: 10
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80

  service:
    type: ClusterIP
    externalTrafficPolicy: Cluster
    # annotations:
      # service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "false"
      # service.beta.kubernetes.io/aws-load-balancer-type: external
      # service.beta.kubernetes.io/aws-load-balancer-connection-draining-enabled: "false"
      # service.beta.kubernetes.io/aws-load-balancer-connection-draining-timeout: "180"
      # service.beta.kubernetes.io/aws-load-balancer-connection-idle-timeout: "60"
      # service.beta.kubernetes.io/aws-load-balancer-additional-resource-tags: "Name=haproxy-private-k8s"
      # service.beta.kubernetes.io/aws-load-balancer-nlb-target-type: "instance"
      # service.beta.kubernetes.io/load-balancer-source-ranges: "10.0.0.0/8"
      # service.beta.kubernetes.io/aws-load-balancer-name: "haproxy-private-k8"
      # service.beta.kubernetes.io/aws-load-balancer-scheme: "internal"
      # service.beta.kubernetes.io/aws-load-balancer-ip-address-type: "ipv4"
      # service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-057ed4860a4024ced"
      # service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "tcp"
      # service.beta.kubernetes.io/aws-load-balancer-target-group-attributes: "preserve_client_ip.enabled=true,deregistration_delay.connection_termination.enabled=true,deregistration_delay.timeout_seconds=120"
    httpPorts:
      - port: 80
        targetPort: http
    extraPorts:
      - port: 8080
        targetPort: 8080
      - port: 5701
        targetPort: 5701
    httpsPorts: []

  haproxy:
    enabled: false

  stats:
    enabled: false

  metrics:
    enabled: false

  serviceMonitor:
    enabled: false

  logs:
    enabled: false

defaultBackend:
  enabled: true

  name: default-backend
  image:
    repository: k8s.gcr.io/defaultbackend-amd64
    tag: "1.5"
    pullPolicy: IfNotPresent

  replicaCount: 0

  minAvailable: 1

  resources:
    limits:
      cpu: 100m
      memory: 200Mi
    requests:
      cpu: 100m
      memory: 200Mi

  service:
    name: ingress-default-backend
    annotations: {}
    clusterIP: ""
    externalIPs: []

    loadBalancerClass: ""
    loadBalancerIP: ""
    loadBalancerSourceRanges: []

    servicePort: 8080
    type: ClusterIP
