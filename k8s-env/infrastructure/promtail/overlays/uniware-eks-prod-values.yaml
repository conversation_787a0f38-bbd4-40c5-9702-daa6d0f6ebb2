
configmap:
  enabled: true

image:
  registry: docker.io
  repository: grafana/promtail
  tag: 3.0.0
  pullPolicy: IfNotPresent


resources:
  limits:
    cpu: 200m
    memory: 256Mi
  requests:
    cpu: 100m
    memory: 128Mi


config:
  enabled: true
  logLevel: info
  logFormat: logfmt
  serverPort: 9080

  clients:
    - url: http://loki-gateway.monitoring.svc.cluster.local:80/loki/api/v1/push
      tenant_id: 'cluster'
  positions:
    filename: /run/promtail/positions.yaml

  snippets:
    pipelineStages:
      - docker: {}
      - cri: {}
      - multiline:
          firstline: '^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}'
          max_wait_time: 5s
          max_lines: 128
      - drop:
          source: "namespace"
          value:  "prod"
      - drop:
          source: "namespace"
          value:  "kube-system"
      - drop:
          source: "namespace"
          value:  "monitoring"
      - drop:
          source: "namespace"
          value:  "argocd"
      - drop:
          source: "namespace"
          value:  "kube-system"
      - drop:
          source: "namespace"
          value:  "karpenter"
      - drop:
          source: "namespace"
          value:  "cert-manager"
      - drop:
          source: "namespace"
          value:  "default"
      - drop:
          source: "namespace"
          value:  "unixpress-production"
      - labeldrop:
          - helm_sh_chart
          - version
          - type
          - pod_template_generation
          - app_kubernetes_io_managed_by
          - pod_template_hash
          - app_kubernetes_io_part_of
          - app_kubernetes_io_version
          - apps_kubernetes_io_pod_index
          - controller_revision_hash
          - eks_amazonaws_com_component
          - statefulset_kubernetes_io_pod_name
          - app_kubernetes_io_instance
          - app_kubernetes_io_component
          - chart
          - component
          - heritage
          - operator_prometheus_io_shard
          - operator_prometheus_io_name

    addScrapeJobLabel: false

    extraLimitsConfig: ""

    extraServerConfigs: ""

    extraScrapeConfigs: ""

    extraRelabelConfigs: []

    scrapeConfigs: |
      - job_name: pod-logs
        kubernetes_sd_configs:
          - role: pod
        pipeline_stages:
          {{- toYaml .Values.config.snippets.pipelineStages | nindent 4 }}
        relabel_configs:
          - source_labels:
              - __meta_kubernetes_pod_node_name
            target_label: __host__
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - action: replace
            replacement: $1
            separator: /
            source_labels:
              - __meta_kubernetes_namespace
              - __meta_kubernetes_pod_name
            target_label: job
          - action: replace
            source_labels:
              - __meta_kubernetes_namespace
            target_label: namespace
          - action: replace
            source_labels:
              - __meta_kubernetes_pod_name
            target_label: pod
          - action: replace
            source_labels:
              - __meta_kubernetes_pod_container_name
            target_label: container
          - replacement: /var/log/pods/*$1/*.log
            separator: /
            source_labels:
              - __meta_kubernetes_pod_uid
              - __meta_kubernetes_pod_container_name
            target_label: __path__


  file: |
    server:
      log_level: {{ .Values.config.logLevel }}
      log_format: {{ .Values.config.logFormat }}
      http_listen_port: {{ .Values.config.serverPort }}
      {{- with .Values.httpPathPrefix }}
      http_path_prefix: {{ . }}
      {{- end }}
      {{- tpl .Values.config.snippets.extraServerConfigs . | nindent 2 }}

    clients:
      {{- tpl (toYaml .Values.config.clients) . | nindent 2 }}

    positions:
      {{- tpl (toYaml .Values.config.positions) . | nindent 2 }}

    scrape_configs:
      {{- tpl .Values.config.snippets.scrapeConfigs . | nindent 2 }}
      {{- tpl .Values.config.snippets.extraScrapeConfigs . | nindent 2 }}

    limits_config:
      {{- tpl .Values.config.snippets.extraLimitsConfig . | nindent 2 }}

    tracing:
      enabled: {{ .Values.config.enableTracing }}

