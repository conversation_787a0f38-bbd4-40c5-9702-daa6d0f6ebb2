apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: promtail
  namespace: argocd
spec:
  syncPolicy:
    preserveResourcesOnDeletion: true
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
  - list:
      elements:
        - cluster: uniware-eks-prod
          autoSync: true
          prune: true
          project: infra
          namespace: monitoring
  template:
    metadata:
      name: '{{.cluster}}-promtail'
    spec:
      project: '{{.project}}'
      sources:
        - repoURL: 'https://grafana.github.io/helm-charts'
          chart: promtail
          targetRevision: 6.15.5
          helm:
            releaseName: promtail
            valueFiles:
              - $values/infrastructure/promtail/overlays/{{.cluster}}-values.yaml
        - repoURL: 'https://github.com/devops-unicommerce/k8s-env.git'
          targetRevision: HEAD
          ref: values
      destination:
        name: '{{.cluster}}'
        namespace: '{{.namespace}}'
    
  templatePatch: |
    spec:
    {{- if .autoSync }}
      syncPolicy:
        automated:
          prune: {{ .prune }}
          selfHeal: true
    {{- end }}
