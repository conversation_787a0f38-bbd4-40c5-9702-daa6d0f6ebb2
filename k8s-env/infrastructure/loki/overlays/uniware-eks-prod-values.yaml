lokiCanary:
  enabled: false
minio:
  enabled: false
test:
  enabled: false
read:
  replicas: 3
  resources:
    requests:
      cpu: 8
      memory: 24Gi
    limits:
      cpu: 8
      memory: 24Gi 
backend:
  replicas: 3
  persistence:
    size: 50Gi
  resources:
    requests:
      cpu: 1
      memory: 2Gi
    limits:
      cpu: 1
      memory: 2Gi 
write:
  replicas: 6
  persistence:
    size: 50Gi
  resources:
    requests:
      cpu: 2
      memory: 6Gi
    limits:
      cpu: 2
      memory: 6Gi 

deploymentMode: SimpleScalable

query_range:
  max_retries: 5
  timeout: 10m

serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/uniware_eks_prod_AmazonEKS_EBS_CSI_DriverRole

loki:
  commonConfig:
    replication_factor: 1
    compactor_address: 'http://loki-backend:3100'
    path_prefix: /var/loki

  useTestSchema: false

  limits_config:
    max_cache_freshness_per_query: 10m
    query_timeout: 900s
    reject_old_samples: true
    reject_old_samples_max_age: 168h
    split_queries_by_interval: 5m
    volume_enabled: true
    per_stream_rate_limit: 2048M
    per_stream_rate_limit_burst: 4096M
    ingestion_rate_mb: 1024
    ingestion_burst_size_mb: 1024
    retention_period: 180d
    retention_stream:
    - selector: '{job="cloudfront-logs"}'
      priority: 1
      period: 7d
    - selector: '{job="cloudfront-waf-logs"}'
      priority: 2
      period: 7d
    max_entries_limit_per_query: 20000
    max_queriers_per_tenant: 40
    max_query_parallelism: 5000
    max_query_series: *********
    max_global_streams_per_user: 0

  storage:
    bucketNames:
      chunks: unicommerce-loki-logs
      ruler: unicommerce-loki-logs
      admin: unicommerce-loki-logs
    type: s3
    s3:
      region: ap-south-1
      s3ForcePathStyle: false
      insecure: false

  compactor:
    working_directory: /var/loki/data/retention
    compaction_interval: 10m
    retention_enabled: true
    retention_delete_delay: 2h
    retention_delete_worker_count: 150
    delete_request_store: s3

  frontend:
    scheduler_address: loki-query-scheduler-discovery.monitoring.svc.cluster.local:9095
    tail_proxy_url: http://loki-read-headless.monitoring.svc.cluster.local:3100
    compress_responses: true
    scheduler_worker_concurrency: 32

  frontend_worker:
    scheduler_address: loki-query-scheduler-discovery.monitoring.svc.cluster.local:9095

  querier:
    multi_tenant_queries_enabled: true
    max_concurrent: 512

  index_gateway:
    mode: simple

  tracing:
    enabled: false

  pattern_ingester:
    enabled: false

  ingester:
    chunk_idle_period: 30m
    chunk_block_size: 202144
    chunk_encoding: gzip
    chunk_retain_period: 1m
    autoforget_unhealthy: true

  auth_enabled: true

  server:
    grpc_listen_port: 9095
    http_listen_port: 3100
    http_server_read_timeout: 10m
    http_server_write_timeout: 10m
    grpc_server_max_concurrent_streams: 1000
    http_server_idle_timeout: 10m
    grpc_server_max_recv_msg_size: 10485760000
    grpc_server_max_send_msg_size: 10485760000

  commonStorageConfig:
    s3:
      bucketnames: unicommerce-loki-logs
      insecure: false
      region: ap-south-1
      s3forcepathstyle: false

  runtime_config:
    file: /etc/loki/runtime-config/runtime-config.yaml

  chunksCache:
    enabled: true
    default_validity: 0s
    writebackParallelism: 10
    writebackBuffer: 500000
    writebackSizeLimit: 500MB
    batchSize: 4
    parallelism: 5
    timeout: 2000ms

  schemaConfig:
    configs:
    - from: "2024-04-01"
      index:
        period: 24h
        prefix: index_
      object_store: 's3'
      schema: v13
      store: tsdb

  storage_config:
    boltdb_shipper:
      index_gateway_client:
        server_address: dns+loki-backend-headless.monitoring.svc.cluster.local:9095
    hedging:
      at: 250ms
      max_per_second: 20
      up_to: 3
    tsdb_shipper:
      index_gateway_client:
        server_address: dns+loki-backend-headless.monitoring.svc.cluster.local:9095
      active_index_directory: /var/loki/data/tsdb-index
      cache_location: /var/loki/data/tsdb-cache

  query_range:
    max_retries: 5

ruler:
  storage:
    s3:
      bucketnames: unicommerce-loki-logs
      insecure: false
      region: ap-south-1
      s3forcepathstyle: false
    type: s3

resultsCache:
  enabled: true
  default_validity: 12h
  writebackParallelism: 1
  writebackBuffer: 500000
  writebackSizeLimit: 500MB

chunksCache:
  replicas: 2
  resources:
    requests:
      cpu: 0.5
      memory: 10Gi
    limits:
      cpu: 0.5
      memory: 10Gi

gateway:
  replicas: 5
  resources:
    requests:
      cpu: 1
      memory: 2Gi
    limits:
      cpu: 1
      memory: 2Gi
  nginxConfig:
    httpSnippet: |-
      client_max_body_size 50M;
    serverSnippet: |-
      client_max_body_size 50M;
  ingress:
    enabled: true
    ingressClassName: haproxy-public
    annotations:
      kubernetes.io/ingress.class: haproxy-public
      haproxy-ingress.github.io/auth-secret: "kube-system/haproxy-public-secret"
      haproxy-ingress.github.io/auth-type: "basic-auth"
    tls: false
    hosts:
      - host: loki-gateway.unicommerce.co.in
        paths:
          - path: /
            pathType: Prefix
  
  service:
    type: ClusterIP
