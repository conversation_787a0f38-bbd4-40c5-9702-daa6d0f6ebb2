rbac:
  create: true

serviceAccount:
  create: true
  automountServiceAccountToken: true

nameOverride: ""
fullnameOverride: ""

controller:
  ingressClass: haproxy-public
  image:
    repository: quay.io/jcmoraisjr/haproxy-ingress
    tag: v0.14.7
    pullPolicy: IfNotPresent
  
  # podAffinity:
  #   podAntiAffinity:
  #       requiredDuringSchedulingIgnoredDuringExecution:
  #       - labelSelector:
  #           matchExpressions:
  #           - key: app.kubernetes.io/instance
  #             operator: In
  #             values:
  #             - haproxy-public-ingress
  #         topologyKey: "kubernetes.io/hostname"
  
  updateStrategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 2
  
  publishService:
    enabled: false
  
  defaultBackendService: "kube-system/haproxy-external"
  
  extraArgs:
    enable-endpointslices-api: true
    report-node-internal-ip-address:
    allow-cross-namespace:
    master-worker: true

  
  ingressClassResource:
    enabled: true
    default: false
    controllerClass: ""
    parameters: {}

  healthzPort: 10253

  livenessProbe:
    path: /healthz
    port: 10253
    failureThreshold: 3
    initialDelaySeconds: 60
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1

  readinessProbe:
    path: /healthz
    port: 10253
    failureThreshold: 3
    initialDelaySeconds: 10
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1

  automountServiceAccountToken: true

  hostNetwork: false
  dnsPolicy: ClusterFirst

  terminationGracePeriodSeconds: 60

  kind: Deployment

  containerPorts:
    http: 80
    https: 443

  enableStaticPorts: true

  minReadySeconds: 0

  replicaCount: 1

  minAvailable: 1

  resources:
   limits:
     cpu: 500m
     memory: 2Gi
   requests:
     cpu: 500m
     memory: 2Gi

  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 2
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80

  service:
    httpPorts:
      - port: 80
        targetPort: http
    httpsPorts:
      - port: 443
        targetPort: https
    extraPorts:
      - port: 10253
        targetPort: 10253

    externalTrafficPolicy: Cluster

    healthCheckNodePort: 0

    type: NodePort

  haproxy:
    enabled: false

  stats:
    enabled: true

  metrics:
    enabled: true

  serviceMonitor:
    enabled: true
    labels:
      release: prometheus
    metrics:
      relabelings:
      - replacement: cl1
        targetLabel: cluster
      - sourceLabels: [__meta_kubernetes_pod_node_name]
        targetLabel: hostname
    ctrlMetrics:
      relabelings:
      - replacement: cl1
        targetLabel: cluster
      - sourceLabels: [__meta_kubernetes_pod_node_name]
        targetLabel: hostname


  logs:
    enabled: false
  
  config:
    max-connections: "65000"
    timeout-http-request: 10s
    timeout-connect: 10s
    timeout-client: 880s
    timeout-server: 800s
    timeout-keep-alive: 30s
    syslog-endpoint: stdout
    syslog-format: raw
    forwardfor: ignore
    use-forwarded-proto: "false"

    config-global: |
      tune.maxrewrite     1024
      tune.bufsize        16384
    
    config-defaults: |
      mode                    http
      option                  httplog
      option                  forwardfor
      option                  abortonclose
      no option checkcache
      retries                 3
    
    config-frontend: |
      acl match_url_app2 query -m sub 's=app2'
      http-request set-var(req.backend) var(req.backend),regsub(api,task) if match_url_app2 { var(req.backend) -m found }
    
    config-frontend-early: |
      option forwardfor
      option http-server-close
      tcp-request connection accept

defaultBackend:
  enabled: false

  name: default-backend
  image:
    repository: k8s.gcr.io/defaultbackend-amd64
    tag: "1.5"
    pullPolicy: IfNotPresent

  replicaCount: 1

  minAvailable: 1

  resources:
    limits:
      cpu: 100m
      memory: 200Mi
    requests:
      cpu: 100m
      memory: 200Mi

  service:
    name: ingress-default-backend
    annotations: {}
    clusterIP: ""
    externalIPs: []

    loadBalancerClass: ""
    loadBalancerIP: ""
    loadBalancerSourceRanges: []

    servicePort: 8080
    type: ClusterIP
