apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: haproxy-public
  namespace: argocd
spec:
  syncPolicy:
    preserveResourcesOnDeletion: true
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
  - list:
      elements:
        - cluster: uniware-eks-prod
          autoSync: true
          prune: true
          project: infra
          namespace: kube-system
  template:
    metadata:
      name: '{{.cluster}}-haproxy-public'
    spec:
      project: '{{.project}}'
      sources:
        - repoURL: 'https://haproxy-ingress.github.io/charts'
          chart: haproxy-ingress
          targetRevision: 0.14.7
          helm:
            releaseName: haproxy-public-ingress
            valueFiles:
              - $values/infrastructure/haproxy-public/overlays/{{.cluster}}-values.yaml
        - repoURL: 'https://github.com/devops-unicommerce/k8s-env.git'
          targetRevision: HEAD
          ref: values
      destination:
        name: '{{.cluster}}'
        namespace: '{{.namespace}}'
    
  templatePatch: |
    spec:
    {{- if .autoSync }}
      syncPolicy:
        automated:
          prune: {{ .prune }}
          selfHeal: true
    {{- end }}
