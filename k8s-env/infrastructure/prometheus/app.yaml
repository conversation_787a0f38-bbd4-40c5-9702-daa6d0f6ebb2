apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: prometheus
  namespace: argocd
spec:
  syncPolicy:
    preserveResourcesOnDeletion: true
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
  - list:
      elements:
        - cluster: uniware-eks-prod
          autoSync: true
          prune: true
          project: infra
          namespace: monitoring
  template:
    metadata:
      name: '{{.cluster}}-prometheus'
    spec:
      project: '{{.project}}'
      sources:
        - repoURL: 'https://prometheus-community.github.io/helm-charts'
          chart: kube-prometheus-stack
          targetRevision: 61.9.0
          helm:
            releaseName: prometheus
            valueFiles:
              - $values/infrastructure/prometheus/overlays/{{.cluster}}-values.yaml
        - repoURL: 'https://github.com/devops-unicommerce/k8s-env.git'
          targetRevision: HEAD
          ref: values
      destination:
        name: '{{.cluster}}'
        namespace: '{{.namespace}}'
    
  templatePatch: |
    spec:
    {{- if .autoSync }}
      syncPolicy:
        automated:
          prune: {{ .prune }}
          selfHeal: true
    {{- end }}
