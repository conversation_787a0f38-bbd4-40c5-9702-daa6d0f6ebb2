crds:
  enabled: false
alertmanager:
  enabled: false
kubeStateMetrics:
  enabled: true
grafana:
  enabled: false
nodeExporter:
  enabled: true
prometheus-node-exporter:
  prometheus:
    monitor:
      enabled: true
      attachMetadata:
        node: true
      relabelings:
        - sourceLabels: [__meta_kubernetes_node_label_karpenter_sh_nodepool]
          targetLabel: nodepool
        - sourceLabels: [__meta_kubernetes_node_label_kubernetes_io_hostname]
          targetLabel: hostname
windowsMonitoring:
  enabled: true
defaultRules:
  rules:
    alertmanager: false
    etcd: false
    configReloaders: false
    general: false
    k8sContainerCpuUsageSecondsTotal: false
    k8sContainerMemoryCache: false
    k8sContainerMemoryRss: false
    k8sContainerMemorySwap: false
    k8sContainerResource: false
    k8sContainerMemoryWorkingSetBytes: false
    k8sPodOwner: false
    kubeApiserverAvailability: false
    kubeApiserverBurnrate: false
    kubeApiserverHistogram: false
    kubeApiserverSlos: false
    kubeControllerManager: false
    kubelet: false
    kubeProxy: false
    kubePrometheusGeneral: false
    kubePrometheusNodeRecording: false
    kubernetesApps: false
    kubernetesResources: false
    kubernetesStorage: false
    kubernetesSystem: false
    kubeSchedulerAlerting: false
    kubeSchedulerRecording: false
    kubeStateMetrics: false
    network: false
    node: false
    nodeExporterAlerting: false
    nodeExporterRecording: false
    prometheus: false
    prometheusOperator: false
    windows: false

prometheus:
  enabled: true
  resources:
    limits:
      memory: 10Gi
    requests:
      memory: 10Gi
  prometheusSpec:
    replicas: 2
    persistentVolumeClaimRetentionPolicy:
      whenDeleted: Retain
      whenScaled: Retain
    retention: 1y
    storageSpec:
      volumeClaimTemplate:
        spec:
          storageClassName: gp3
          accessModes: ["ReadWriteOnce"]
          resources:
            requests:
              storage: 16Gi
    additionalAlertRelabelConfigs:
      - action: labeldrop
        regex: replica
    additionalScrapeConfigs:
      - job_name: 'argocd'
        static_configs:
          - targets:
              - 'argocd-server-metrics.argocd:8083'
      - job_name: 'activemq'
        scrape_timeout: 25s
        static_configs:
          - targets:
              - 'activemq.c1-in.unicommerce.infra:8080'
              - 'activemq.c2-in.unicommerce.infra:8080'
              - 'activemq.ec1-in.unicommerce.infra:8080'
              - 'activemq.ec2-in.unicommerce.infra:8080'
              - 'activemq.e1-in.unicommerce.infra:8080'
              - 'activemq.e2-in.unicommerce.infra:8080'
              - 'activemq1.e1-in.unicommerce.infra:8080'
              - 'activemq2.e1-in.unicommerce.infra:8080'
              - 'activemq.prefetch-in.unicommerce.infra:8080'
      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
        - role: pod
        relabel_configs:
        - action: keep
          source_labels: [__meta_kubernetes_pod_label_app]
          regex: ratelimit
        - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
          action: keep
          regex: true
        - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
          action: replace
          target_label: __metrics_path__
          regex: (.+)
        - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
          action: replace
          regex: ([^:]+)(?::\d+)?;(\d+)
          replacement: $1:$2
          target_label: __address__
        - action: labelmap
          regex: __meta_kubernetes_pod_label_(.+)
        - source_labels: [__meta_kubernetes_namespace]
          action: replace
          target_label: kubernetes_namespace
        - source_labels: [__meta_kubernetes_pod_name]
          action: replace
          target_label: kubernetes_pod_name
        - action: keep
          source_labels: [__meta_kubernetes_namespace]
          regex: istio-system
        
      
      - job_name: 'istiod'
        kubernetes_sd_configs:
        - role: endpoints
          namespaces:
            names:
            - istio-system
        relabel_configs:
        - source_labels: [__meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
          action: keep
          regex: istiod;http-monitoring
        
      - job_name: 'envoy-stats'
        metrics_path: /stats/prometheus
        kubernetes_sd_configs:
        - role: pod
        relabel_configs:
        - source_labels: [__meta_kubernetes_pod_container_port_name]
          action: keep
          regex: '.*-envoy-prom'
              
    alertingEndpoints:
      - name: "alertmanager"
        namespace: "monitoring"
        port: 9093
        scheme: http
  ingress:
    enabled: true
    ingressClassName: haproxy-public
    annotations:
      haproxy-ingress.github.io/auth-secret: "kube-system/haproxy-public-secret"
      haproxy-ingress.github.io/auth-type: "basic-auth"
      kubernetes.io/ingress.class: "haproxy-public"
    hosts:
      - prometheus-k8s.unicommerce.co.in
    path: /
    pathType: Prefix
