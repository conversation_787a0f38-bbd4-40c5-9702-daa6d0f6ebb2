apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: istio-base
  namespace: argocd
spec:
  syncPolicy:
    preserveResourcesOnDeletion: true
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
  - list:
      elements:
        - cluster: uniware-eks-prod
          autoSync: true
          prune: true
          project: infra
          namespace: istio-system
  template:
    metadata:
      name: '{{.cluster}}-istio-base'
    spec:
      project: '{{.project}}'
      sources:
        - repoURL: 'https://istio-release.storage.googleapis.com/charts'
          chart: base
          targetRevision: 1.24.2
          helm:
            releaseName: istio-base
            valueFiles:
              - $values/infrastructure/istio/istio-base/overlays/{{.cluster}}-values.yaml
        - repoURL: 'https://github.com/devops-unicommerce/k8s-env.git'
          targetRevision: HEAD
          ref: values
      destination:
        name: '{{.cluster}}'
        namespace: '{{.namespace}}'
      ignoreDifferences:
      - kind: ValidatingWebhookConfiguration
        name: istiod-default-validator
        jqPathExpressions:
          - '.webhooks[]?.failurePolicy'
    
  templatePatch: |
    spec:
    {{- if .autoSync }}
      syncPolicy:
        syncOptions:
          - CreateNamespace=true
        automated:
          prune: {{ .prune }}
          selfHeal: true
    {{- end }}
