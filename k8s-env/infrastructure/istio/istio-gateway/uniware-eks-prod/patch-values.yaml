rbac:
  enabled: true

serviceAccount:
  create: true

podAnnotations:
  prometheus.io/port: "15020"
  prometheus.io/scrape: "true"
  prometheus.io/path: "/stats/prometheus"
  inject.istio.io/templates: "gateway"
  sidecar.istio.io/inject: "true"

service:
  type: NodePort
  ports:
  - name: status-port
    port: 15021
    protocol: TCP
    targetPort: 15021
  - name: http2
    port: 80
    protocol: TCP
    targetPort: 80
  - name: https
    port: 443
    protocol: TCP
    targetPort: 443
  annotations: {}

resources:
  requests:
    cpu: 500m
    memory: 1024Mi
  limits:
    cpu: 500m
    memory: 1024Mi

autoscaling:
  enabled: true
  minReplicas: 5
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: {}
  autoscaleBehavior: {}

podDisruptionBudget:
  minAvailable: 3

terminationGracePeriodSeconds: 60


