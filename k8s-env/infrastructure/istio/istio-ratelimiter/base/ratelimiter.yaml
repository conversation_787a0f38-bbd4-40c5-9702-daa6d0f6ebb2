apiVersion: v1
kind: ConfigMap
metadata:
  name: ratelimit-config
  namespace: istio-system
data:
  config.yaml: |
    domain: ratelimit
    descriptors:
      - key: HOST
        descriptors:
          - key: PATH
            value: "services-rest-v1"
            rate_limit:
              unit: minute
              requests_per_unit: 200
            detailed_metric: true
            shadow_mode: true
---
apiVersion: v1
kind: Service
metadata:
  name: ratelimit
  labels:
    app: ratelimit
  namespace: istio-system
spec:
  ports:
  - name: http-port
    port: 8080
    targetPort: 8080
    protocol: TCP
  - name: grpc-port
    port: 8081
    targetPort: 8081
    protocol: TCP
  - name: http-debug
    port: 6070
    targetPort: 6070
    protocol: TCP
  - name: http-prometheus
    port: 9090
    targetPort: 9090
    protocol: TCP
  selector:
    app: ratelimit

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ratelimit
  namespace: istio-system
  annotations:
    prometheus.io/scrape: 'true'
spec:
  replicas: 5
  selector:
    matchLabels:
      app: ratelimit
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: ratelimit
      annotations:
        prometheus.io/scrape: 'true'
    spec:
      containers:
      - image: envoyproxy/ratelimit:60d8e81b
        imagePullPolicy: IfNotPresent
        name: ratelimit
        command: ["/bin/ratelimit"]
        resources:
          limits:
            cpu: 300m
            memory: 500Mi
          requests:
            cpu: 300m
            memory: 500Mi
        livenessProbe:
          httpGet:
            path: /healthcheck
            port: 8080
          failureThreshold: 5
          periodSeconds: 30
          successThreshold: 1
          timeoutSeconds: 10
          initialDelaySeconds: 30
        readinessProbe:
          httpGet:
            path: /healthcheck
            port: 8080
          failureThreshold: 5
          periodSeconds: 30
          successThreshold: 1
          timeoutSeconds: 10
          initialDelaySeconds: 30
        env:
        - name: LOG_LEVEL
          value: debug
        - name: REDIS_SOCKET_TYPE
          value: tcp
        - name: REDIS_URL
          value: mymaster,redis-sentinel-node-0.redis-sentinel-headless:26379,redis-sentinel-node-1.redis-sentinel-headless:26379,redis-sentinel-node-2.redis-sentinel-headless:26379
        - name: REDIS_POOL_SIZE
          value: "50"
        - name: REDIS_TYPE
          value: sentinel
        - name: USE_STATSD
          value: "false"
        - name: RUNTIME_ROOT
          value: /data
        - name: RUNTIME_SUBDIRECTORY
          value: ratelimit
        - name: RUNTIME_WATCH_ROOT
          value: "false"
        - name: USE_PROMETHEUS
          value: "true"
        - name: RUNTIME_IGNOREDOTFILES
          value: "true"
        - name: HOST
          value: "::"
        - name: GRPC_HOST
          value: "::"
        ports:
        - containerPort: 9090
        volumeMounts:
        - name: config-volume
          mountPath: /data/ratelimit/config
      volumes:
      - name: config-volume
        configMap:
          name: ratelimit-config
