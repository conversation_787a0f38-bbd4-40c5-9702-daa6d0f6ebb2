autoscaleEnabled: true
autoscaleMin: 2
autoscaleMax: 5
autoscaleBehavior: {}
replicaCount: 2
rollingMaxSurge: 100%
rollingMaxUnavailable: 25%

# Resources for a small pilot install
resources:
  requests:
    cpu: 500m
    memory: 2048Mi

cpu:
  targetAverageUtilization: 80
memory: {}
  # targetAverageUtilization: 80

sidecarInjectorWebhook:
  enableNamespacesByDefault: false
  neverInjectSelector:
    - matchLabels:
        app.kubernetes.io/name: uniware-api
    - matchLabels:
        app.kubernetes.io/name: uniware-task

base:
  enableIstioConfigCRDs: true