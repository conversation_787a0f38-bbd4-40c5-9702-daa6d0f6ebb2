apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: istiod
  namespace: argocd
spec:
  syncPolicy:
    preserveResourcesOnDeletion: true
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
  - list:
      elements:
        - cluster: uniware-eks-prod
          autoSync: true
          prune: true
          project: infra
          namespace: istio-system
  template:
    metadata:
      name: 'istiod-{{.cluster}}'
    spec:
      project: '{{.project}}'
      sources:
        - repoURL: 'https://github.com/devops-unicommerce/k8s-env.git'
          targetRevision: HEAD
          path: infrastructure/istio/istiod/{{.cluster}}
      destination:
        name: '{{.cluster}}'
        namespace: '{{.namespace}}'
      ignoreDifferences:
        - kind: ValidatingWebhookConfiguration
          name: istio-validator-istio-system
          jsonPointers:
            - /webhooks/0/failurePolicy
    
  templatePatch: |
    spec:
    {{- if .autoSync }}
      syncPolicy:
        syncOptions:
          - CreateNamespace=true
        automated:
          prune: {{ .prune }}
          selfHeal: true
    {{- end }}