config:
  enabled: true
  global:
    resolve_timeout: 5m
  templates:
  - '/etc/alertmanager/*.tmpl'
  route:
    receiver: default
    group_by: ['alertname', 'priority']
    group_wait: 10s
    repeat_interval: 30m
    routes:
    - match:
        alertname: DeadMansSwitch
      repeat_interval: 30m
      receiver: default
  receivers:
  - name: default
    webhook_configs:
    - send_resolved: true
      http_config:
        bearer_token: "UC@123#"
      url: http://signalilo.monitoring/webhook
