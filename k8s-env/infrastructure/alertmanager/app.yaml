apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: alertmanager
  namespace: argocd
spec:
  syncPolicy:
    preserveResourcesOnDeletion: true
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
  - list:
      elements:
        - cluster: uniware-eks-prod
          autoSync: true
          prune: false
          project: infra
          namespace: monitoring
  template:
    metadata:
      name: '{{.cluster}}-alertmanager'
    spec:
      project: '{{.project}}'
      sources:
        - repoURL: 'https://prometheus-community.github.io/helm-charts'
          chart: alertmanager
          targetRevision: 1.11.0
          helm:
            releaseName: alertmanager
            valueFiles:
              - $values/infrastructure/alertmanager/overlays/{{.cluster}}-values.yaml
        - repoURL: 'https://github.com/devops-unicommerce/k8s-env.git'
          targetRevision: HEAD
          ref: values
      destination:
        name: '{{.cluster}}'
        namespace: '{{.namespace}}'
    
  templatePatch: |
    spec:
    {{- if .autoSync }}
      syncPolicy:
        automated:
          prune: {{ .prune }}
          selfHeal: true
    {{- end }}
