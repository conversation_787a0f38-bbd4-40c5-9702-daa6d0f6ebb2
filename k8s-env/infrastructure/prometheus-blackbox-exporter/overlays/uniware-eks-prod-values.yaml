serviceMonitor:
  enabled: true
  defaults:
    interval: 30s
    scrapeTimeout: 30s
    module: http_2xx
  scheme: http
  path: "/probe"
  targets:
    - name: ecloud1-web-health
      url: http://ecloud1-uniware-web.prod.svc.cluster.local:8080/open/live
      labels: 
        environment: production
        release: prometheus
      interval: 30s
      scrapeTimeout: 30s
    - name: ecloud1-api-health
      url: http://ecloud1-uniware-api.prod.svc.cluster.local:8080/open/live
      labels: 
        environment: production
        release: prometheus
      interval: 30s
      scrapeTimeout: 30s
