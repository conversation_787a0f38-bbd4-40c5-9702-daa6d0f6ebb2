apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: prometheus-blackbox-exporter
  namespace: argocd
spec:
  syncPolicy:
    preserveResourcesOnDeletion: true
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
  - list:
      elements:
        - cluster: uniware-eks-prod
          autoSync: true
          prune: true
          project: infra
          namespace: monitoring
  template:
    metadata:
      name: '{{.cluster}}-prometheus-blackbox-exporter'
    spec:
      project: '{{.project}}'
      sources:
        - repoURL: 'https://prometheus-community.github.io/helm-charts'
          chart: prometheus-blackbox-exporter
          targetRevision: 9.0.0
          helm:
            releaseName: prometheus-blackbox-exporter 
            valueFiles:
              - $values/infrastructure/prometheus-blackbox-exporter/overlays/{{.cluster}}-values.yaml
        - repoURL: 'https://github.com/devops-unicommerce/k8s-env.git'
          targetRevision: HEAD
          ref: values
      destination:
        name: '{{.cluster}}'
        namespace: '{{.namespace}}'
    
  templatePatch: |
    spec:
    {{- if .autoSync }}
      syncPolicy:
        automated:
          prune: {{ .prune }}
          selfHeal: true
    {{- end }}
