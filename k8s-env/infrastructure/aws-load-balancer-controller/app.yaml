apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: aws-load-balancer-controller
  namespace: argocd
spec:
  syncPolicy:
    preserveResourcesOnDeletion: true
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
  - list:
      elements:
        - cluster: uniware-eks-prod
          autoSync: true
          prune: true
          project: infra
          namespace: kube-system
  template:
    metadata:
      name: '{{.cluster}}-aws-load-balancer-controller'
    spec:
      project: '{{.project}}'
      sources:
        - repoURL: 'https://aws.github.io/eks-charts'
          chart: aws-load-balancer-controller
          targetRevision: 1.8.1
          helm:
            releaseName: aws-load-balancer-controller
            valueFiles:
              - $values/infrastructure/aws-load-balancer-controller/overlays/{{.cluster}}-values.yaml
        - repoURL: 'https://github.com/devops-unicommerce/k8s-env.git'
          targetRevision: HEAD
          ref: values
      destination:
        name: '{{.cluster}}'
        namespace: '{{.namespace}}'
    
  templatePatch: |
    spec:
    {{- if .autoSync }}
      syncPolicy:
        automated:
          prune: {{ .prune }}
          selfHeal: true
    {{- end }}
