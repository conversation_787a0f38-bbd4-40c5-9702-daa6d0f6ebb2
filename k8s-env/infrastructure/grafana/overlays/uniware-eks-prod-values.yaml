persistence:
  type: pvc
  enabled: true
  storageClassName: gp3
  accessModes:
    - ReadWriteOnce
  size: 10Gi
  finalizers:
    - kubernetes.io/pvc-protection

admin:
  existingSecret: "grafana-admin-secrets"
  userKey: admin-user
  passwordKey: admin-password

ingress:
  enabled: false
  ingressClassName: haproxy-public
  labels: {}
  path: /
  pathType: Prefix
  hosts:
    - unitrail.unicommerce.co.in


grafana.ini:
  auth.ldap:
    enabled: true
    allow_sign_up: true
    config_file: /etc/grafana/ldap.toml
  log:
    mode: console
    level: debug
  users:
    viewers_can_edit: true

ldap:
  enabled: true
  existingSecret: ""
  config: |-
    verbose_logging = true
    [[servers]]
    host = "ipa.unicommerce.infra"
    port = 389
    use_ssl = false
    start_tls = false
    ssl_skip_verify = false

    bind_dn = "uid=%s,cn=users,cn=accounts,dc=ipa,dc=unicommerce,dc=infra"

    search_filter = "(uid=%s)"

    search_base_dns = ["cn=users,cn=accounts,dc=ipa,dc=unicommerce,dc=infra"]

    [servers.attributes]
    name = "givenName"
    surname = "sn"
    username = "uid"
    member_of = "memberOf"
    email =  "email"

    [[servers.group_mappings]]
    group_dn = "cn=admins,cn=groups,cn=accounts,dc=ipa,dc=unicommerce,dc=infra"
    org_role = "Admin"

    [[servers.group_mappings]]
    group_dn = "cn=developer,cn=groups,cn=accounts,dc=ipa,dc=unicommerce,dc=infra"
    org_role = "Editor"

    [[servers.group_mappings]]
    group_dn = "*"
    org_role = "Viewer"
