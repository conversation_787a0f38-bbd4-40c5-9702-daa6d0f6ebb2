apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: grafana
  namespace: argocd
spec:
  syncPolicy:
    preserveResourcesOnDeletion: true
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
  - list:
      elements:
        - cluster: uniware-eks-prod
          autoSync: true
          prune: true
          project: infra
          namespace: monitoring
  template:
    metadata:
      name: '{{.cluster}}-grafana'
    spec:
      project: '{{.project}}'
      sources:
        - repoURL: 'https://grafana.github.io/helm-charts'
          chart: grafana
          targetRevision: 8.4.8
          helm:
            releaseName: grafana
            valueFiles:
              - $values/infrastructure/grafana/overlays/{{.cluster}}-values.yaml
        - repoURL: 'https://github.com/devops-unicommerce/k8s-env.git'
          targetRevision: HEAD
          ref: values
      destination:
        name: '{{.cluster}}'
        namespace: '{{.namespace}}'
    
  templatePatch: |
    spec:
    {{- if .autoSync }}
      syncPolicy:
        automated:
          prune: {{ .prune }}
          selfHeal: true
    {{- end }}
