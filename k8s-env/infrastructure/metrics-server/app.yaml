apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: metrics-server
  namespace: argocd
spec:
  syncPolicy:
    preserveResourcesOnDeletion: true
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
  - list:
      elements:
        - cluster: uniware-eks-prod
          autoSync: true
          prune: true
          project: infra
          namespace: kube-system
  template:
    metadata:
      name: '{{.cluster}}-metrics-server'
    spec:
      project: '{{.project}}'
      sources:
        - repoURL: 'https://github.com/devops-unicommerce/k8s-env.git'
          targetRevision: HEAD
          path: infrastructure/metrics-server/base
      destination:
        name: '{{.cluster}}'
        namespace: '{{.namespace}}'
    
  templatePatch: |
    spec:
    {{- if .autoSync }}
      syncPolicy:
        automated:
          prune: {{ .prune }}
          selfHeal: true
    {{- end }}
