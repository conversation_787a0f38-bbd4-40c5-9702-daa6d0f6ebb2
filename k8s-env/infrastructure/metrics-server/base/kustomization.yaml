resources:
- manifest.yaml
patches:
- patch: |-
    - op: replace
      path: /spec/template/spec/containers/0/ports/0/containerPort
      value: 4443
    - op: replace
      path: /spec/template/spec/containers/0/args
      value: ["--cert-dir=/tmp", "--secure-port=4443", "--kubelet-preferred-address-types=InternalIP,ExternalIP,Hostname",  "--kubelet-use-node-status-port", "--metric-resolution=15s", "--kubelet-insecure-tls=true"]
    - op: add
      path: /spec/template/spec/hostNetwork
      value: true
  target:
    group: apps
    kind: Deployment
    name: metrics-server
    version: v1
