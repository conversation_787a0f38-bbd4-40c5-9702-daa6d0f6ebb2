apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: vault
  namespace: argocd
spec:
  syncPolicy:
    preserveResourcesOnDeletion: true
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
  - list:
      elements:
        - cluster: uniware-eks-prod
          autoSync: true
          prune: true
          project: infra
          namespace: kube-system
  template:
    metadata:
      name: '{{.cluster}}-vault'
    spec:
      project: '{{.project}}'
      sources:
        - repoURL: 'https://helm.releases.hashicorp.com'
          chart: vault
          targetRevision: 0.28.0
          helm:
            releaseName: vault
            valueFiles:
              - $values/infrastructure/vault/overlays/{{.cluster}}-values.yaml
        - repoURL: 'https://github.com/devops-unicommerce/k8s-env.git'
          targetRevision: HEAD
          ref: values
      destination:
        name: '{{.cluster}}'
        namespace: '{{.namespace}}'
    
  templatePatch: |
    spec:
    {{- if .autoSync }}
      syncPolicy:
        automated:
          prune: {{ .prune }}
          selfHeal: true
    {{- end }}
