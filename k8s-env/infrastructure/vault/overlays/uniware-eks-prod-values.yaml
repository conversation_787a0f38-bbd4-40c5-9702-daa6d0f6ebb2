global:
  storageClass: "gp3"
injector:
  enabled: false
csiProvider:
  enaled: false
ui:
  enabled: true
  serviceType: ClusterIP
  externalPort: 8200
server:
  enabled: true
  automountServiceAccountToken: true
  metrics:
    enabled: false
  rbac:
    create: true
  serviceAccount:
    create: true
    automountServiceAccountToken: true
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/uniware_eks_prod_AmazonEKS_EBS_CSI_DriverRole
  dataStorage:
    enabled: true
    size: 20Gi
    storageClass: gp3
  auditStorage:
    enabled: true
    size: 20Gi
    storageClass: gp3
  ingress:
    enabled: false
    ingressClassName: haproxy-public
    hosts:
      - host: k8s-vault.unicommerce.co.in
  extraEnvironmentVars:
    AWS_ROLE_ARN: "arn:aws:iam::************:role/uniware_eks_prod_AmazonEKS_EBS_CSI_DriverRole"
  ha:
    enabled: true
    replicas: 3
    clusterAddr: "http://$(HOSTNAME).vault-internal:8201"
    raft:
      enabled: true
      config: |
        ui = true
        listener "tcp" {
          tls_disable = 1
          address = "[::]:8200"
          cluster_address = "[::]:8201"
        }
        storage "raft" {
          path = "/vault/data"
            retry_join {
                leader_api_addr = "http://vault-0.vault-internal:8200"
            }
          retry_join {
              leader_api_addr = "http://vault-1.vault-internal:8200"
          }
          retry_join {
              leader_api_addr = "http://vault-2.vault-internal:8200"
          }
          autopilot {
            cleanup_dead_servers = "true"
            last_contact_threshold = "200ms"
            last_contact_failure_threshold = "10m"
            max_trailing_logs = 250000
            min_quorum = 2
            server_stabilization_time = "10s"
          }
        }
        seal "awskms" {
          region     = "ap-south-1"
          kms_key_id = "680b6a8b-2d0b-45eb-b438-5f2c93771845"
        }
        service_registration "kubernetes" {}
        log_level = "Debug"