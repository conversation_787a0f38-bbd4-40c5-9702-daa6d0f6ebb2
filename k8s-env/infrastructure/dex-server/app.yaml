apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: k8s-dashboard-dex-server
  namespace: argocd
spec:
  syncPolicy:
    preserveResourcesOnDeletion: true
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
  - list:
      elements:
        - cluster: uniware-eks-prod
          autoSync: true
          prune: true
          project: infra
          namespace: kube-system
  template:
    metadata:
      name: '{{.cluster}}-k8s-dashboard-dex-server'
    spec:
      project: '{{.project}}'
      sources:
        - repoURL: 'https://charts.dexidp.io'
          chart: dex
          targetRevision: 0.19.1
          helm:
            releaseName: k8s-dashboard-dex-server
            valueFiles:
              - $values/infrastructure/dex-server/overlays/{{.cluster}}-values.yaml
        - repoURL: 'https://github.com/devops-unicommerce/k8s-env.git'
          targetRevision: HEAD
          ref: values
      destination:
        name: '{{.cluster}}'
        namespace: '{{.namespace}}'
    
  templatePatch: |
    spec:
    {{- if .autoSync }}
      syncPolicy:
        automated:
          prune: {{ .prune }}
          selfHeal: true
    {{- end }}
