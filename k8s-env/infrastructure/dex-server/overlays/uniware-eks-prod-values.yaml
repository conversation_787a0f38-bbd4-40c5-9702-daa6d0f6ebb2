config:
  issuer: https://k8s-uniware-prod.unicommerce.co.in/dex
  storage:
    type: kubernetes
    config:
      inCluster: true
  web:
    http: 0.0.0.0:5556
  connectors:
  - type: ldap
    id: ldap
    name: LDAP
    config:
      host: ipa.unicommerce.infra:389
      insecureNoSSL: true
      insecureSkipVerify: true
      bindDN: "uid=admin,cn=users,cn=accounts,dc=ipa,dc=unicommerce,dc=infra"
      bindPW: ${LDAP_BIND_PW}
      userSearch:
        baseDN: "cn=users,cn=accounts,dc=ipa,dc=unicommerce,dc=infra"
        filter: ""
        username: uid
        idAttr: uid
        emailAttr: mail
        nameAttr: givenName
        preferredUsernameAttr: uid
      groupSearch:
        baseDN: "cn=groups,cn=compat,dc=ipa,dc=unicommerce,dc=infra"
        filter: "(objectClass=posixGroup)"
        userMatchers:
        - userAttr: uid
          groupAttr: memberUid
        nameAttr: cn
    skipApprovalScreen: true
    
  oauth2:
    skipApprovalScreen: true

  staticClients:
  - id: kubernetes-dashboard
    redirectURIs:
    - 'https://k8s-uniware-prod.unicommerce.co.in/oauth2/callback'
    name: 'Kubernetes Dashboard'
    secret: xyz@123#

  expiry:
    signingKeys: "6h"
    idTokens: "24h"

envFrom:
  - secretRef:
      name: ldap-secret


ingress:
  enabled: false
  className: haproxy-public
  hosts:
    - host: k8s-uniware-prod.unicommerce.co.in
      paths:
        - path: /dex
          pathType: ImplementationSpecific
