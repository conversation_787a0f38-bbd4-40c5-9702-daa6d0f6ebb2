ingress:
  annotations:
    haproxy.ingress.kubernetes.io/affinity: "cookie"
    haproxy.ingress.kubernetes.io/affinity-cookie-name: "_oauth2_proxy"
  enabled: false
  className: haproxy-public
  path: /
  pathType: ImplementationSpecific
  hosts:
    - k8s-uniware-prod.unicommerce.co.in

metrics:
  enabled: false

initContainers:
  waitForRedis:
    enabled: false

sessionStorage:
  type: cookie

config:
  clientID: "kubernetes-dashboard"
  clientSecret: "xyz@123#"
  cookieSecret: "QQ69KspBHMuTIesi1D4F94zK0SwgqehYLRtPjLZGAT0: "

extraArgs:
  provider: oidc
  oidc-issuer-url: https://k8s-uniware-prod.unicommerce.co.in/dex
  http-address: http://0.0.0.0:4180
  client-id: kubernetes-dashboard
  client-secret: xyz@123#
  cookie-secret: QQ69KspBHMuTIesi1D4F94zK0SwgqehYLRtPjLZGAT0=
  redirect-url: https://k8s-uniware-prod.unicommerce.co.in/oauth2/callback
  pass-basic-auth: false
  pass-access-token: true
  set-authorization-header: true
  pass-authorization-header: true
  skip-provider-button: true
  ssl-insecure-skip-verify: true
  ssl-upstream-insecure-skip-verify: true
  upstream: https://k8s-dashboard.kube-system.svc.cluster.local:443
  email-domain: '*'
  cookie-refresh: 1h
  cookie-expire: 24h
  scope: openid profile email groups
  cookie-secure: true
  cookie-csrf-per-request: false
  cookie-csrf-expire: 15m
  reverse-proxy: true
  skip-auth-preflight: true
  cookie-domain: k8s-uniware-prod.unicommerce.co.in
  cookie-path: /
  cookie-samesite: none
  pass-user-headers: true
  pass-host-header: true
  skip-jwt-bearer-tokens: true
  show-debug-on-error: true
  skip-auth-regex: '/api/v1/csrftoken/*'
  skip-auth-regex: '/api/v1/me'