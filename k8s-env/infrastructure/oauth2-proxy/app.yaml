apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: oauth2-proxy
  namespace: argocd
spec:
  syncPolicy:
    preserveResourcesOnDeletion: true
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
  - list:
      elements:
        - cluster: uniware-eks-prod
          autoSync: true
          prune: true
          project: infra
          namespace: kube-system
  template:
    metadata:
      name: '{{.cluster}}-oauth2-proxy'
    spec:
      project: '{{.project}}'
      sources:
        - repoURL: 'https://oauth2-proxy.github.io/manifests'
          chart: oauth2-proxy
          targetRevision: 7.7.9
          helm:
            releaseName: oauth2-proxy
            valueFiles:
              - $values/infrastructure/oauth2-proxy/overlays/{{.cluster}}-values.yaml
        - repoURL: 'https://github.com/devops-unicommerce/k8s-env.git'
          targetRevision: HEAD
          ref: values
      destination:
        name: '{{.cluster}}'
        namespace: '{{.namespace}}'
    
  templatePatch: |
    spec:
    {{- if .autoSync }}
      syncPolicy:
        automated:
          prune: {{ .prune }}
          selfHeal: true
    {{- end }}
