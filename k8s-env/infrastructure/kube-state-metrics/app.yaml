apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: kube-state-metrics
  namespace: argocd
spec:
  syncPolicy:
    preserveResourcesOnDeletion: true
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
  - list:
      elements:
        - cluster: uniware-eks-prod
          autoSync: true
          prune: true
          project: infra
          namespace: monitoring
  template:
    metadata:
      name: '{{.cluster}}-kube-state-metrics'
    spec:
      project: '{{.project}}'
      sources:
        - repoURL: 'https://prometheus-community.github.io/helm-charts'
          chart: kube-state-metrics
          targetRevision: 5.20.0
          helm:
            releaseName: kube-state-metrics
      destination:
        name: '{{.cluster}}'
        namespace: '{{.namespace}}'
    
  templatePatch: |
    spec:
    {{- if .autoSync }}
      syncPolicy:
        automated:
          prune: {{ .prune }}
          selfHeal: true
    {{- end }}
