env:
  open:
    AWS_SDK_LOAD_CONFIG: true
    STORAGE: amazon
    STORAGE_AMAZON_BUCKET: unicommerce-helm-charts
    STORAGE_AMAZON_PREFIX:
    STORAGE_AMAZON_REGION: ap-south-1
    DISABLE_API: false
  existingSecret: chartmuseum-secret
  existingSecretMappings:
    BASIC_AUTH_USER: basic-auth-user
    BASIC_AUTH_PASS: basic-auth-pass
extraArgs: ["--cache-interval=1m"]
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/uniware_eks_prod_AmazonEKS_EBS_CSI_DriverRole
ingress:
  enabled: true
  ingressClassName: haproxy-public
  hosts:
    - name: chartmuseum.unicommerce.co.in
      path: /
  pathType: ImplementationSpecific