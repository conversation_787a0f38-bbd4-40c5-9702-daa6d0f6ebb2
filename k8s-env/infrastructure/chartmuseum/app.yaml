apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: chartmuseum
  namespace: argocd
spec:
  syncPolicy:
    preserveResourcesOnDeletion: true
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
  - list:
      elements:
        - cluster: uniware-eks-prod
          autoSync: true
          prune: true
          project: infra
          namespace: kube-system
  template:
    metadata:
      name: '{{.cluster}}-chartmuseum'
    spec:
      project: '{{.project}}'
      sources:
        - repoURL: 'https://chartmuseum.github.io/charts'
          chart: chartmuseum
          targetRevision: 3.10.3
          helm:
            releaseName: chartmuseum
            valueFiles:
              - $values/infrastructure/chartmuseum/overlays/{{.cluster}}-values.yaml
        - repoURL: 'https://github.com/devops-unicommerce/k8s-env.git'
          targetRevision: HEAD
          ref: values
      destination:
        name: '{{.cluster}}'
        namespace: '{{.namespace}}'
    
  templatePatch: |
    spec:
    {{- if .autoSync }}
      syncPolicy:
        automated:
          prune: {{ .prune }}
          selfHeal: true
    {{- end }}