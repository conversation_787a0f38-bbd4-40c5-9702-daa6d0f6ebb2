apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  labels:
    prometheus: blackbox-exporter
    release: prometheus
  namespace: monitoring
  name: blackbox-alerts.rules
spec:
  groups:
  - name: blackbox-alerts.rules
    rules:
      # Alert when the probe fails (endpoint is down)
      - alert: EndpointDown
        expr: probe_success == 0
          unless (probe_success{instance="http://ecloud1-uniware-web.prod.svc.cluster.local:8080/open/live"} == 0)
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Endpoint {{ $labels.instance }} is down"
          description: "The endpoint {{ $labels.instance }} has not responded successfully for the last 5 minutes."

      # Alert when HTTP response time exceeds 2 seconds
      - alert: HighHttpLatency
        expr: probe_http_duration_seconds > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High HTTP latency for {{ $labels.instance }}"
          description: "The HTTP response time for {{ $labels.instance }} has exceeded 2 seconds for the last 5 minutes."

      # Alert for unexpected HTTP status codes (anything other than 200)
      - alert: UnexpectedHttpStatusCode
        expr: probe_http_status_code != 200
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Unexpected HTTP status code for {{ $labels.instance }}"
          description: "The endpoint {{ $labels.instance }} returned an unexpected HTTP status code for the last 5 minutes."

      # Alert when DNS lookup duration exceeds 1 second
      - alert: HighDnsLookupDuration
        expr: probe_dns_lookup_duration_seconds > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High DNS lookup duration for {{ $labels.instance }}"
          description: "The DNS lookup time for {{ $labels.instance }} has exceeded 1 second for the last 5 minutes."

      # Alert when TCP connection duration exceeds 1 second
      - alert: HighTcpConnectionDuration
        expr: probe_tcp_duration_seconds > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High TCP connection duration for {{ $labels.instance }}"
          description: "The TCP connection time for {{ $labels.instance }} has exceeded 1 second for the last 5 minutes."

      # Alert when an insecure SSL/TLS version is used
      - alert: InsecureSslVersion
        expr: probe_ssl_vers != 3.3  # Example for TLS 1.2, adjust as necessary
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Insecure SSL/TLS version used by {{ $labels.instance }}"
          description: "The endpoint {{ $labels.instance }} is using an insecure SSL/TLS version."
