apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  labels:
    prometheus: activemq-rules
    release: prometheus
  namespace: monitoring
  name: activemq-alerts.rules
spec:
  groups:
  - name: activemq
    interval: 1m
    rules:
      - alert: kahaDBStoreUsage
        expr: activemq_store_usage_ratio > 0.8
        for: 3m
        labels:
          severity: critical
        annotations:
          summary: "Store usage percentage is {{ $value | humanizePercentage }} for ActiveMQ {{ $labels.broker }}"
          description: "store usage percentage is {{ $value | humanizePercentage }} for ActiveMQ {{ $labels.broker }}"