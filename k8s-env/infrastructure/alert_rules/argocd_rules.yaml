apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  labels:
    prometheus: argocd-rules
    release: prometheus
  namespace: monitoring
  name: argocd-alerts.rules
spec:
  groups:
  - name: argo-cd
    interval: 1m
    rules:
      - alert: ArgoCdAppOutOfSync
        expr: |
          sum(
            argocd_app_info{
              job=~".*",
              sync_status!="Synced"
            }
          ) by (job, dest_server, project, name, sync_status) > 0
        for: 5m
        labels:
          severity: critical
        annotations:
          description: "The application {{ $labels.project }}/{{ $labels.name }} is out of sync with the sync status {{ $labels.sync_status }} for the past 5 minutes."
          summary: "An ArgoCD Application is Out Of Sync."
        
      - alert: ArgoCdAppUnhealthy
        expr: |
          sum(
            argocd_app_info{
              job=~".*",
              health_status!~"Healthy|Progressing"
            }
          ) by (job, dest_server, project, name, health_status) > 0
        for: 5m
        labels:
          severity: critical
        annotations:
          description: "The application {{ $labels.project }}/{{ $labels.name }} is unhealthy with the health status {{ $labels.health_status }} for the past 5 minutes."
          summary: "An ArgoCD Application is Unhealthy."

      - alert: ArgoCdAppSyncFailed
        expr: |
          sum(
            round(
              increase(
                argocd_app_sync_total{
                  job=~".*",
                  phase!="Succeeded"
                }[10m]
              )
            )
          ) by (job, dest_server, project, name, phase) > 0
        for: 1m
        labels:
          severity: critical
        annotations:
          description: "The application {{ $labels.project }}/{{ $labels.name }} has failed to sync with the status {{ $labels.phase }} the past 10 minutes."
          summary: "An ArgoCD Application has Failed to Sync."


