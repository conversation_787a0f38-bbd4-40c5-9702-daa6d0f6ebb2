app:
  ingress:
    enabled: false
    # annotations:
    #   haproxy-ingress.github.io/auth-url: "https://k8s-uniware-prod.unicommerce.co.in/oauth2/auth"
    #   haproxy-ingress.github.io/auth-signin: "https://k8s-uniware-prod.unicommerce.co.in/oauth2/start?rd=$escaped_request_uri"
    hosts:
      - k8s-uniware-kong.unicommerce.co.in
    issuer:
      scope: disabled
    ingressClassName: haproxy-public
    useDefaultIngressClass: false
    useDefaultAnnotations: false
    path: /
    pathType: ImplementationSpecific
    tls:
      enabled: false
metricsScraper:
  enabled: false
kong:
  enabled: true
  env:
    plugins: "bundled"
  manager:
    type: ClusterIP
  proxy:
    type: ClusterIP
    http:
      enabled: true
    tls:
      enabled: false
      servicePort: 80
  dblessConfig:
    configMap: ""
    config: |
      _format_version: "3.0"
      services:
        - name: auth
          host: kubernetes-dashboard-auth
          port: 8000
          protocol: http
          routes:
            - name: authLogin
              paths:
                - /api/v1/login
              strip_path: false
            - name: authCsrf
              paths:
                - /api/v1/csrftoken/login
              strip_path: false
            - name: authMe
              paths:
                - /api/v1/me
              strip_path: false
          plugins:
          - name: cors
            config:
              origins:
                - "*"
              methods:
                - "GET"
                - "POST"
                - "PUT"
                - "DELETE"
                - "OPTIONS"
              headers:
                - "Authorization"
                - "Content-Type"
                - "X-CSRF-Token"
              exposed_headers:
                - "X-CSRF-Token"
              max_age: 3600
          - name: request-transformer
            config:
              add:
                headers:
                  - Authorization:$http_authorization
                  - X-CSRF-Token:$http_x_csrf_token
                  - Cookie:$http_cookie
                  - token:$http_x_auth_request_access_token
        - name: api
          host: kubernetes-dashboard-api
          port: 8000
          protocol: http
          routes:
            - name: api
              paths:
                - /api
              strip_path: false
            - name: metrics
              paths:
                - /metrics
              strip_path: false
          plugins:
          - name: cors
            config:
              origins:
                - "*"
              methods:
                - "GET"
                - "POST"
                - "PUT"
                - "DELETE"
                - "OPTIONS"
              headers:
                - "Authorization"
                - "Content-Type"
                - "X-CSRF-Token"
              exposed_headers:
                - "X-CSRF-Token"
              max_age: 3600
          - name: request-transformer
            config:
              add:
                headers:
                  - Authorization:$http_authorization
                  - X-CSRF-Token:$http_x_csrf_token
                  - Cookie:$http_cookie
                  - token:$http_x_auth_request_access_token
        - name: web
          host: kubernetes-dashboard-web
          port: 8000
          protocol: http
          routes:
            - name: root
              paths:
                - /
              strip_path: false
          plugins:
          - name: cors
            config:
              origins:
                - "*"
              methods:
                - "GET"
                - "POST"
                - "PUT"
                - "DELETE"
                - "OPTIONS"
              headers:
                - "Authorization"
                - "Content-Type"
                - "X-CSRF-Token"
              exposed_headers:
                - "X-CSRF-Token"
              max_age: 3600
          - name: request-transformer
            config:
              add:
                headers:
                  - Authorization:$http_authorization
                  - X-CSRF-Token:$http_x_csrf_token
                  - Cookie:$http_cookie
                  - token:$http_x_auth_request_access_token
  plugins:
    configMaps:
      - pluginName: header-transformer
        name: kubernetes-dashboard-kong-custom-dbless-config