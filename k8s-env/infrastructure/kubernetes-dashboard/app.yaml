apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: kubernetes-dashboard
  namespace: argocd
spec:
  syncPolicy:
    preserveResourcesOnDeletion: true
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
  - list:
      elements:
        - cluster: uniware-eks-prod
          autoSync: true
          prune: true
          project: infra
          namespace: kube-system
  template:
    metadata:
      name: '{{.cluster}}-kubernetes-dashboard'
    spec:
      project: '{{.project}}'
      sources:
        - repoURL: 'https://chartmuseum.unicommerce.co.in'
          chart: kubernetes-dashboard
          targetRevision: 7.5.4
          helm:
            releaseName: kubernetes-dashboard
            valueFiles:
              - $values/infrastructure/kubernetes-dashboard/overlays/{{.cluster}}-values.yaml
        - repoURL: 'https://github.com/devops-unicommerce/k8s-env.git'
          targetRevision: HEAD
          ref: values
      destination:
        name: '{{.cluster}}'
        namespace: '{{.namespace}}'
    
  templatePatch: |
    spec:
    {{- if .autoSync }}
      syncPolicy:
        automated:
          prune: {{ .prune }}
          selfHeal: true
    {{- end }}
