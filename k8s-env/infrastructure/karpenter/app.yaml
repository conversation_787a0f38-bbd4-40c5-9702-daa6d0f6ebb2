apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: karpenter
  namespace: argocd
spec:
  syncPolicy:
    preserveResourcesOnDeletion: true
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
  - list:
      elements:
        - cluster: uniware-eks-prod
          autoSync: true
          prune: true
          project: infra
          namespace: karpenter
  template:
    metadata:
      name: '{{.cluster}}-karpenter'
    spec:
      project: '{{.project}}'
      sources:
        - repoURL: 'https://github.com/devops-unicommerce/k8s-env.git'
          targetRevision: HEAD
          path: infrastructure/karpenter/{{.cluster}}
      destination:
        name: '{{.cluster}}'
        namespace: '{{.namespace}}'
  templatePatch: |
    spec:
    {{- if .autoSync }}
      syncPolicy:
        syncOptions:
          - CreateNamespace=true  
        automated:
          prune: {{ .prune }}
          selfHeal: true
    {{- end }}

