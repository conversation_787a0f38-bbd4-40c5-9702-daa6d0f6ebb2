settings:
  interruptionQueue: "uniware-production-in"
  clusterEndpoint: "https://3054FCE6A53743DE218942CFDA546581.gr7.ap-south-1.eks.amazonaws.com"
  clusterName: "uniware-eks-prod"
  batchMaxDuration: 60s
  batchIdleDuration: 30s
  featureGates:
    spotToSpotConsolidation: true
serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: "arn:aws:iam::************:role/KarpenterControllerRole-uniware-eks-prod"
logLevel: debug
logConfig:
  logLevel:
    global: debug
    controller: debug
    webhook: debug
webhook:
  enabled: true
  port: 8443
hostNetwork: true
controller:
  metrics:
    port: 8000
topologySpreadConstraints: {}