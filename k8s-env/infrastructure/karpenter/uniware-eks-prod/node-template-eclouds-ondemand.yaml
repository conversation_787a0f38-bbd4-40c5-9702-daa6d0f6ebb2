---
apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: eclouds-ondemand-pool
spec:
  template:
    spec:
      requirements:
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["on-demand"]
        - key: karpenter.k8s.aws/instance-family
          operator: In
          values: ["c6a", "c5a", "r6a", "r5a"]
        - key: karpenter.k8s.aws/instance-size
          operator: NotIn
          values: ["32xlarge","48xlarge","metal"]
        - key: "topology.kubernetes.io/zone"
          operator: In
          values: ["ap-south-1a"]
        - key: "kubernetes.io/arch"
          operator: In
          values: ["amd64"]
      nodeClassRef:
        kind: EC2NodeClass
        name: eclouds-ondemand-nodeclass
        group: karpenter.k8s.aws
      terminationGracePeriod: 5m
      taints:
        - key: eclouds/ondemand
          effect: NoSchedule
  limits:
    cpu: '300'
    memory: 1150Gi
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 1h
    budgets:
    - nodes: "1"
      reasons:
      - "Empty"
      - "Underutilized"
    - nodes: "1"
      reasons:
      - "Drifted"
---
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: eclouds-ondemand-nodeclass
spec:
  metadataOptions:
    httpEndpoint: enabled
    httpPutResponseHopLimit: 2
    httpTokens: optional
  amiFamily: AL2
  role: KarpenterNodeRole-uniware-eks-prod
  amiSelectorTerms:
    - id: "ami-0948df8d3b200c4e6"
  associatePublicIPAddress: false
  subnetSelectorTerms:
    - tags:
        karpenter.sh/discovery: uniware-production-in
  securityGroupSelectorTerms:
    - tags:
        karpenter.sh/discovery: uniware-production-in
  blockDeviceMappings:
    - deviceName: /dev/sda1
      ebs:
        volumeSize: 35Gi
        volumeType: gp3
        encrypted: false
  tags:
    Name: eks-eclouds-ondemand-worker
