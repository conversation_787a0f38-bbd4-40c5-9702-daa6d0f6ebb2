apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: devops-spot-pool
spec:
  template:
    spec:
      requirements:
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["spot"]
        - key: karpenter.k8s.aws/instance-family
          operator: In
          values: ["c5", "c5a", "c6a", "m3", "m4", "m5", "m5a", "m6a", "r3", "r4", "r5", "r5a", "r6a"]
        - key: karpenter.k8s.aws/instance-size
          operator: NotIn
          values: ["32xlarge","48xlarge","metal"]
        - key: "topology.kubernetes.io/zone"
          operator: In
          values: ["ap-south-1a"]
        - key: "kubernetes.io/arch"
          operator: In
          values: ["amd64"]
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: devops-spot-nodeclass
      terminationGracePeriod: 5m
      taints:
        - key: devops/spot
          effect: NoSchedule
  limits:
    cpu: '1050'
    memory: 2900Gi
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 5m
    budgets:
    - nodes: "2"
      reasons:
      - "Empty"
      - "Underutilized"
    - nodes: "1"
      reasons:
      - "Drifted"
---
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: devops-spot-nodeclass
spec:
  metadataOptions:
    httpEndpoint: enabled
    httpPutResponseHopLimit: 2
    httpTokens: optional
  amiFamily: AL2
  role: KarpenterNodeRole-uniware-eks-prod
  amiSelectorTerms:
    - id: "ami-0948df8d3b200c4e6"
  associatePublicIPAddress: false
  subnetSelectorTerms:
    - tags:
        karpenter.sh/discovery: uniware-production-in
  securityGroupSelectorTerms:
    - tags:
        karpenter.sh/discovery: uniware-production-in
  blockDeviceMappings:
    - deviceName: /dev/sda1
      ebs:
        volumeSize: 30Gi
        volumeType: gp3
        encrypted: false
  userData: |
    #!/bin/bash
    echo -e "InhibitDelayMaxSec=45\n" >> /etc/systemd/logind.conf
    systemctl restart systemd-logind
    echo "$(jq ".shutdownGracePeriod=\"45s\"" /etc/kubernetes/kubelet/kubelet-config.json)" > /etc/kubernetes/kubelet/kubelet-config.json
    echo "$(jq ".shutdownGracePeriodCriticalPods=\"15s\"" /etc/kubernetes/kubelet/kubelet-config.json)" > /etc/kubernetes/kubelet/kubelet-config.json
  tags:
    Name: eks-devops-spot-worker