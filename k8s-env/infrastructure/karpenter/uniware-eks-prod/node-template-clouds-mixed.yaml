apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: eclouds-mixed-pool
  annotations:
    karpenter.sh/do-not-evict: "true"
spec:
  template:
    metadata:
      annotations:
        karpenter.sh/do-not-evict: "true"
    spec:
      requirements:
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["spot", "on-demand"]  # Supports both Spot and On-Demand instances
        - key: karpenter.k8s.aws/instance-family
          operator: In
          values: ["c6a", "c5a", "r6a", "r5a", "m5a", "m6a", "r5", "m5", "c5"]
        - key: karpenter.k8s.aws/instance-size
          operator: NotIn
          values: ["48xlarge", "metal"]
        - key: "topology.kubernetes.io/zone"
          operator: In
          values: ["ap-south-1a"]
        - key: "kubernetes.io/arch"
          operator: In
          values: ["amd64"]
      nodeClassRef:
        kind: EC2NodeClass
        name: eclouds-mixed-nodeclass
        group: karpenter.k8s.aws
      terminationGracePeriod: 5m
      taints:
        - key: eclouds/mixed
          effect: NoSchedule
  limits:
    cpu: '1050'      
    memory: 2900Gi   
  disruption:
    consolidationPolicy: WhenEmpty
    consolidateAfter: 6h
    budgets:
    - nodes: "2"
      reasons:
      - "Empty"
      - "Underutilized"
    - nodes: "1"
      reasons:
      - "Drifted"

---
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: eclouds-mixed-nodeclass
spec:
  metadataOptions:
    httpEndpoint: enabled
    httpPutResponseHopLimit: 2
    httpTokens: optional
  amiFamily: AL2
  role: KarpenterNodeRole-uniware-eks-prod
  amiSelectorTerms:
    - id: "ami-0948df8d3b200c4e6"
  associatePublicIPAddress: false
  subnetSelectorTerms:
    - tags:
        karpenter.sh/discovery: uniware-production-in
  securityGroupSelectorTerms:
    - tags:
        karpenter.sh/discovery: uniware-production-in
  blockDeviceMappings:
    - deviceName: /dev/sda1
      ebs:
        volumeSize: 35Gi
        volumeType: gp3
        encrypted: false
  tags:
    Name: eks-eclouds-mixed-worker
