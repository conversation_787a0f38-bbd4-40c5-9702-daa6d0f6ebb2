---
apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: karpenter-ondemand-static-pool
spec:
  template:
    spec:
      requirements:
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["on-demand"]
        - key: karpenter.k8s.aws/instance-family
          operator: In
          values: ["c6a","m6a","r6a"]
        - key: karpenter.k8s.aws/instance-size
          operator: NotIn
          values: ["8xlarge","10xlarge","12xlarge","16xlarge","24xlarge","32xlarge","48xlarge","metal"]
        - key: "topology.kubernetes.io/zone"
          operator: In
          values: ["ap-south-1a"]
        - key: "kubernetes.io/arch"
          operator: In
          values: ["amd64"]
      nodeClassRef:
        kind: EC2NodeClass
        name: ubuntu-prod-worker-template-static
        group: karpenter.k8s.aws
      terminationGracePeriod: 5m
  limits:
    cpu: '1050'
    memory: 2900Gi
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 5m
    budgets:
    - nodes: "1"
      reasons:
      - "Empty"
      - "Underutilized"
    - nodes: "1"
      reasons:
      - "Drifted"
---
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: ubuntu-prod-worker-template-static
spec:
  metadataOptions:
    httpEndpoint: enabled
    httpPutResponseHopLimit: 2
    httpTokens: optional
  amiFamily: AL2
  role: KarpenterNodeRole-uniware-eks-prod
  amiSelectorTerms:
    - id: "ami-0948df8d3b200c4e6"
  associatePublicIPAddress: false
  subnetSelectorTerms:
    - tags:
        karpenter.sh/discovery: uniware-production-in
  securityGroupSelectorTerms:
    - tags:
        karpenter.sh/discovery: uniware-production-in
  blockDeviceMappings:
    - deviceName: /dev/xvda
      ebs:
        volumeSize: 25Gi
        volumeType: gp3
        encrypted: false
  userData: |
    #!/bin/bash
    status_file="/opt/metadata-status.txt"
    REGION="ap-south-1"
    PRIMARY_IP=$(hostname -i)
    INSTANCE_ID=$(/usr/bin/aws ec2 describe-instances --region ${REGION} --filters "Name=private-ip-address,Values=${PRIMARY_IP}" --query 'Reservations[*].Instances[*].InstanceId' --output text)
    if [[ -z ${INSTANCE_ID} ]]; then
      echo -e "Metadata Version Change - FAILED\nPRIMARY_IP: ${PRIMARY_IP}\nREGION: ${REGION}\nINSTANCE_ID: ${INSTANCE_ID}" > ${status_file}
    else
      if /usr/bin/aws ec2 modify-instance-metadata-options --instance-id ${INSTANCE_ID} --region ${REGION} --http-endpoint enabled --http-tokens optional; then
        echo -e "Metadata Version Change - SUCCESS\nPRIMARY_IP: ${PRIMARY_IP}\nREGION: ${REGION}\nINSTANCE_ID: ${INSTANCE_ID}" > ${status_file}
      else
        echo -e "Metadata Version Change - FAILED\nPRIMARY_IP: ${PRIMARY_IP}\nREGION: ${REGION}\nINSTANCE_ID: ${INSTANCE_ID}" > ${status_file}
      fi
    fi
  tags:
    Name: uniware-prod-static-worker