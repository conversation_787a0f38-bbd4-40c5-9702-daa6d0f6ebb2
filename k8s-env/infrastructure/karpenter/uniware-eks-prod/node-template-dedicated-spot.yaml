apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: dedicated-spot-pool
spec:
  template:
    spec:
      requirements:
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["on-demand"]
        - key: karpenter.k8s.aws/instance-family
          operator: In
          values: ["r6a"]
        - key: karpenter.k8s.aws/instance-size
          operator: In
          values: ["12xlarge"]
        - key: "topology.kubernetes.io/zone"
          operator: In
          values: ["ap-south-1a"]
        - key: "kubernetes.io/arch"
          operator: In
          values: ["amd64"]
      nodeClassRef:
        kind: EC2NodeClass
        name: dedicated-spot-nodeclass
        group: karpenter.k8s.aws
      terminationGracePeriod: 10m
      taints:
        - key: dedicated/spot
          effect: NoSchedule
  limits:
    cpu: '96'
    memory: 768Gi
  disruption:
    consolidationPolicy: WhenEmpty
    consolidateAfter: 1h
    budgets:
    - nodes: "2"
      reasons:
      - "Empty"
      - "Underutilized"
    - nodes: "2"
      reasons:
      - "Drifted"
---
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: dedicated-spot-nodeclass
spec:
  metadataOptions:
    httpEndpoint: enabled
    httpPutResponseHopLimit: 2
    httpTokens: optional
  amiFamily: AL2
  role: KarpenterNodeRole-uniware-eks-prod
  amiSelectorTerms:
    - id: "ami-0948df8d3b200c4e6"
  associatePublicIPAddress: false
  subnetSelectorTerms:
    - tags:
        karpenter.sh/discovery: uniware-production-in
  securityGroupSelectorTerms:
    - tags:
        karpenter.sh/discovery: uniware-production-in
  blockDeviceMappings:
    - deviceName: /dev/sda1
      ebs:
        volumeSize: 35Gi
        volumeType: gp3
        encrypted: false
  tags:
    Name: eks-dedicated-spot-worker
