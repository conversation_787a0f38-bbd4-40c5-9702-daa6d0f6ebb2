apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: karpenter-lease-role
  namespace: kube-node-lease
rules:
- apiGroups: ["coordination.k8s.io"]
  resources: ["leases"]
  verbs: ["list", "get", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: karpenter-lease-binding
  namespace: kube-node-lease
subjects:
- kind: ServiceAccount
  name: karpenter
  namespace: karpenter
roleRef:
  kind: Role
  name: karpenter-lease-role
  apiGroup: rbac.authorization.k8s.io
  