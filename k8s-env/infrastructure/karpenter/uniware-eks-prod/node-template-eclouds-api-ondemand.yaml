---
apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: eclouds-api-ondemand-pool
spec:
  template:
    spec:
      requirements:
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["on-demand"]
        - key: karpenter.k8s.aws/instance-family
          operator: In
          values: ["c7g", "m6g", "r6g"]  # ARM64-compatible
        - key: karpenter.k8s.aws/instance-size
          operator: In
          values: ["2xlarge", "4xlarge"]  # removed "metal" unless explicitly needed
        - key: "topology.kubernetes.io/zone"
          operator: In
          values: ["ap-south-1a"]
        - key: "kubernetes.io/arch"
          operator: In
          values: ["arm64"]  # ← updated to ARM64
      nodeClassRef:
        kind: EC2NodeClass
        name: eclouds-api-ondemand-nodeclass
        group: karpenter.k8s.aws
      terminationGracePeriod: 5m
      taints:
        - key: eclouds/min-ondemand
          effect: NoSchedule
  limits:
    cpu: '200'
    memory: 956Gi
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 5m
    budgets:
    - nodes: "1"
      reasons:
      - "Empty"
      - "Underutilized"
    - nodes: "1"
      reasons:
      - "Drifted"
---
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: eclouds-api-ondemand-nodeclass
spec:
  metadataOptions:
    httpEndpoint: enabled
    httpPutResponseHopLimit: 2
    httpTokens: optional
  amiFamily: AL2
  role: KarpenterNodeRole-uniware-eks-prod
  amiSelectorTerms:
    - id: "ami-0344831e61d39b7d6"
  associatePublicIPAddress: false
  subnetSelectorTerms:
    - tags:
        karpenter.sh/discovery: uniware-production-in
  securityGroupSelectorTerms:
    - tags:
        karpenter.sh/discovery: uniware-production-in
  blockDeviceMappings:
    - deviceName: /dev/sda1
      ebs:
        volumeSize: 35Gi
        volumeType: gp3
        encrypted: false
  tags:
    Name: eks-eclouds-api-ondemand-worker
    karpenter.sh/provisioner-name: "eclouds-api-ondemand"
