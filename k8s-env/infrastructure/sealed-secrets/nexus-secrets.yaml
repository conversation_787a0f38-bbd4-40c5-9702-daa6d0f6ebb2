apiVersion: v1
stringData:
  .dockerconfigjson: <docker-config>
kind: Secret
metadata:
  name: nexus-secrets
  namespace: staging
  annotations:
    avp.kubernetes.io/path: "kv/data/k8s/nexus-registry-secrets"
type: kubernetes.io/dockerconfigjson
---
apiVersion: v1
stringData:
  .dockerconfigjson: <docker-config>
kind: Secret
metadata:
  name: nexus-secrets
  namespace: prod
  annotations:
    avp.kubernetes.io/path: "kv/data/k8s/nexus-registry-secrets"
type: kubernetes.io/dockerconfigjson