apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: signalilo
  namespace: argocd
spec:
  syncPolicy:
    preserveResourcesOnDeletion: true
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
  - list:
      elements:
        - cluster: uniware-eks-prod
          autoSync: true
          prune: true
          project: infra
          namespace: monitoring
  template:
    metadata:
      name: '{{.cluster}}-signalilo'
    spec:
      project: '{{.project}}'
      sources:
        - repoURL: 'https://charts.appuio.ch'
          chart: signalilo
          targetRevision: 0.12.1
          helm:
            releaseName: signalilo
            valueFiles:
              - $values/infrastructure/signalilo/overlays/{{.cluster}}-values.yaml
        - repoURL: 'https://github.com/devops-unicommerce/k8s-env.git'
          targetRevision: HEAD
          ref: values
      destination:
        name: '{{.cluster}}'
        namespace: '{{.namespace}}'
    
  templatePatch: |
    spec:
    {{- if .autoSync }}
      syncPolicy:
        automated:
          prune: {{ .prune }}
          selfHeal: true
    {{- end }}
