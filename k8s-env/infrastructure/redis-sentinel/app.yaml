apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: redis-sentinel
  namespace: argocd
spec:
  syncPolicy:
    preserveResourcesOnDeletion: true
  goTemplate: true
  goTemplateOptions: ["missingkey=error"]
  generators:
  - list:
      elements:
        - cluster: uniware-eks-prod
          autoSync: true
          prune: true
          project: infra
          namespace: istio-system
  template:
    metadata:
      name: '{{.cluster}}-redis-sentinel'
    spec:
      project: '{{.project}}'
      sources:
        - repoURL: 'https://chartmuseum.unicommerce.co.in'
          chart: redis
          targetRevision: 20.6.3
          helm:
            releaseName: redis-sentinel
            valueFiles:
              - $values/infrastructure/redis-sentinel/overlays/{{.cluster}}-values.yaml
        - repoURL: 'https://github.com/devops-unicommerce/k8s-env.git'
          targetRevision: HEAD
          ref: values
      destination:
        name: '{{.cluster}}'
        namespace: '{{.namespace}}'
    
  templatePatch: |
    spec:
    {{- if .autoSync }}
      syncPolicy:
        automated:
          prune: {{ .prune }}
          selfHeal: true
    {{- end }}
