replica:
  replicaCount: 3

auth:
  enabled: false
  sentinel: true

architecture: replication

commonConfiguration: |-
  appendonly yes
  save ""

sentinel:
  enabled: true
  masterSet: mymaster
  quorum: 2
  getMasterTimeout: 90
  automateClusterRecovery: true
  redisShutdownWaitFailover: true
  downAfterMilliseconds: 60000
  failoverTimeout: 180000
  parallelSyncs: 1
  
  enableServiceLinks: true
  
  externalMaster:
    enabled: false
    host: ""
    port: 6379
  
  containerPorts:
    sentinel: 26379
  
  startupProbe:
    enabled: true
    initialDelaySeconds: 10
    periodSeconds: 10
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 22
  
  livenessProbe:
    enabled: true
    initialDelaySeconds: 20
    periodSeconds: 10
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 6
  
  readinessProbe:
    enabled: true
    initialDelaySeconds: 20
    periodSeconds: 5
    timeoutSeconds: 1
    successThreshold: 1
    failureThreshold: 6
  
  persistence:
    enabled: false
    
  persistentVolumeClaimRetentionPolicy:
    enabled: false

  resources: {}
  
  service:
    type: ClusterIP
    ports:
      redis: 6379
      sentinel: 26379
    externalTrafficPolicy: Cluster

  masterService:
    enabled: false
    
serviceBindings:
  enabled: false

rbac:
  create: false

serviceAccount:
  create: true

pdb:
  create: true
  minAvailable: 2

metrics:
  enabled: true
  containerPorts:
    http: 9121
  startupProbe:
    enabled: false
    initialDelaySeconds: 10
    periodSeconds: 10
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 5
  livenessProbe:
    enabled: true
    initialDelaySeconds: 10
    periodSeconds: 10
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 5
  readinessProbe:
    enabled: true
    initialDelaySeconds: 5
    periodSeconds: 10
    timeoutSeconds: 1
    successThreshold: 1
    failureThreshold: 3
  
  redisTargetHost: "localhost"
  
  podAnnotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9121"
  
  service:
    enabled: true
    type: ClusterIP
    ports:
      http: 9121
    externalTrafficPolicy: Cluster
    
  serviceMonitor:
    port: http-metrics
    enabled: true
    interval: 60s
    scrapeTimeout: "30s"

    honorLabels: true
  
  podMonitor:
    port: metrics
    enabled: false

