apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    alb.ingress.kubernetes.io/load-balancer-name: uniware-prod-in
    alb.ingress.kubernetes.io/group.name: uniware-haproxy-alb
    alb.ingress.kubernetes.io/group.order: 10
    alb.ingress.kubernetes.io/tags: "Name=uniware-prod-in"
    alb.ingress.kubernetes.io/ip-address-type: "ipv4"
    alb.ingress.kubernetes.io/scheme: "internet-facing"
    alb.ingress.kubernetes.io/load-balancer-attributes: "idle_timeout.timeout_seconds=480,deletion_protection.enabled=false,routing.http2.enabled=false,routing.http.preserve_host_header.enabled=true"
    alb.ingress.kubernetes.io/subnets:  "subnet-002f616c381fea245,subnet-098b99a4466336c1b"
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/inbound-cidrs: "0.0.0.0/0"
    alb.ingress.kubernetes.io/backend-protocol: HTTP
    alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:ap-south-1:************:certificate/2c9a6166-9ef9-434f-94f7-657a3559e414,arn:aws:acm:ap-south-1:************:certificate/b670e472-13da-4091-80a9-fe0e2ba4c5d2"
    alb.ingress.kubernetes.io/target-type: "instance"
    alb.ingress.kubernetes.io/ssl-policy: "ELBSecurityPolicy-TLS-1-1-2017-01"
    alb.ingress.kubernetes.io/target-group-attributes: "deregistration_delay.timeout_seconds=120,slow_start.duration_seconds=30,load_balancing.algorithm.type=round_robin"
    alb.ingress.kubernetes.io/healthcheck-path: "/healthz"
    alb.ingress.kubernetes.io/healthcheck-port: "31200"
  labels:
    app.kubernetes.io/name: uniware-haproxy-ingress
  name: uniware-haproxy-ingress
  namespace: kube-system
spec:
  ingressClassName: alb
  rules:
  - host: "*.unicommerce.co.in"
    http:
      paths:
      - backend:
          service:
            name: haproxy-public-ingress-haproxy-ingress
            port:
              number: 80
        path: /
        pathType: Prefix
  - host: "*.unicommerce.com"
    http:
      paths:
      - backend:
          service:
            name: haproxy-public-ingress-haproxy-ingress
            port:
              number: 80
        path: /
        pathType: Prefix
  - http:
      paths:
        - path: /
          backend:
            service:
              name: haproxy-public-ingress-haproxy-ingress
              port:
                number: 80
          pathType: Prefix
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    alb.ingress.kubernetes.io/load-balancer-name: uniware-prod-in
    alb.ingress.kubernetes.io/group.name: uniware-haproxy-alb
    alb.ingress.kubernetes.io/group.order: 1
    alb.ingress.kubernetes.io/tags: "Name=uniware-prod-in"
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}]'
    alb.ingress.kubernetes.io/backend-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-path: "/healthz"
    alb.ingress.kubernetes.io/healthcheck-port: "31200"
    alb.ingress.kubernetes.io/conditions.rule-path1: >
      [{"field":"host-header","hostHeaderConfig":{"values":["tracelog-in.unicommerce.co.in","qa-qaunixpress.unicommerce.co.in","admin-qaunixpress.unicommerce.co.in"]}}]
    alb.ingress.kubernetes.io/actions.rule-path1: >
      {"type": "redirect","redirectConfig": {"protocol": "HTTPS","port": "443","statusCode": "HTTP_301"}}
  labels:
    app.kubernetes.io/name: uniware-haproxy-ingress-redirect
  name: uniware-haproxy-ingress-redirect
  namespace: kube-system
spec:
  ingressClassName: alb
  rules:
  - http:
      paths:
        - path: /
          backend:
            service:
              name: rule-path1
              port:
                name: use-annotation
          pathType: Prefix