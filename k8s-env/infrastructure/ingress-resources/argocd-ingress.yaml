# apiVersion: networking.k8s.io/v1
# kind: Ingress
# metadata:
#   labels:
#     app.kubernetes.io/name: argocd
#   annotations:
#     haproxy-ingress.github.io/secure-backends: "true"
#   name: argocd
#   namespace: argocd
# spec:
#   ingressClassName: haproxy-public
#   rules:
#   - host: argocd.unicommerce.co.in
#     http:
#       paths:
#       - backend:
#           service:
#             name: argocd-server
#             port:
#               number: 443
#         path: /
#         pathType: Prefix
# ---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: argocd-vs
  namespace: argocd
spec:
  gateways:
  - istio-system/ingress-gateway
  hosts:
  - argocd.unicommerce.co.in
  http:
  - route:
    - destination:
        host: argocd-server
        port:
          number: 443
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: argocd-destination-rule
  namespace: argocd
spec:
  host: argocd-server
  trafficPolicy:
    tls:
      mode: SIMPLE
      insecureSkipVerify: true