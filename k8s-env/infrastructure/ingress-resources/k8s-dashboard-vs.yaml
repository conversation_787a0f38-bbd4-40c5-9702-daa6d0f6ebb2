---
apiVersion: networking.istio.io/v1
kind: VirtualService
metadata:
  name: k8s-dashboard-vs
  namespace: kube-system
spec:
  gateways:
  - istio-system/ingress-gateway
  hosts:
  - k8s-uniware-prod.unicommerce.co.in
  http:
  - match:
    - uri:
        prefix: /dex
    route:
    - destination:
        host: k8s-dashboard-dex-server
        port:
          number: 5556
  - route:
    - destination:
        host: oauth2-proxy
        port:
          number: 80