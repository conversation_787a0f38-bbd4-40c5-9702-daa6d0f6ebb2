apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/name: haproxy-external
  name: haproxy-external
  namespace: kube-system
spec:
  ports:
    - name: http
      port: 8080
      protocol: TCP
      targetPort: 8080
  type: ClusterIP
status:
  loadBalancer: {}
---
addressType: IPv4
apiVersion: discovery.k8s.io/v1
endpoints:
  - addresses:
      - *********
    conditions:
      ready: true
  - addresses:
      - **********
    conditions:
      ready: true
kind: EndpointSlice
metadata:
  labels:
    app.kubernetes.io/name: haproxy-external
    kubernetes.io/service-name: haproxy-external
  name: haproxy-external
  namespace: kube-system
ports:
  - appProtocol: http
    name: http
    port: 8080
    protocol: TCP

