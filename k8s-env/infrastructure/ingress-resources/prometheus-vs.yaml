apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: prometheus-vs
  namespace: monitoring
spec:
  hosts:
  - prometheus-k8s.unicommerce.co.in
  gateways:
  - istio-system/ingress-gateway
  http:
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: haproxy-public-ingress-haproxy-ingress.kube-system.svc.cluster.local
        port:
          number: 80