apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    alb.ingress.kubernetes.io/load-balancer-name: uniware-gateway
    alb.ingress.kubernetes.io/group.name: uniware-gateway-alb
    alb.ingress.kubernetes.io/group.order: 10
    alb.ingress.kubernetes.io/tags: "Name=uniware-gateway"
    alb.ingress.kubernetes.io/ip-address-type: "ipv4"
    alb.ingress.kubernetes.io/scheme: "internet-facing"
    alb.ingress.kubernetes.io/load-balancer-attributes: "idle_timeout.timeout_seconds=480,deletion_protection.enabled=true,routing.http2.enabled=false,routing.http.preserve_host_header.enabled=true"
    alb.ingress.kubernetes.io/subnets:  "subnet-002f616c381fea245,subnet-098b99a4466336c1b"
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/inbound-cidrs: "0.0.0.0/0"
    alb.ingress.kubernetes.io/backend-protocol: HTTP
    alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:ap-south-1:************:certificate/2c9a6166-9ef9-434f-94f7-657a3559e414,arn:aws:acm:ap-south-1:************:certificate/b670e472-13da-4091-80a9-fe0e2ba4c5d2"
    alb.ingress.kubernetes.io/target-type: "instance"
    alb.ingress.kubernetes.io/ssl-policy: "ELBSecurityPolicy-TLS-1-1-2017-01"
    alb.ingress.kubernetes.io/target-group-attributes: "deregistration_delay.timeout_seconds=120,slow_start.duration_seconds=30,load_balancing.algorithm.type=round_robin"
    alb.ingress.kubernetes.io/healthcheck-path: "/healthz/ready"
    alb.ingress.kubernetes.io/healthcheck-port: "31957"
  labels:
    app.kubernetes.io/name: istio-gateway-ingress
  name: istio-gateway-ingress
  namespace: istio-system
spec:
  ingressClassName: alb
  rules:
  - host: "*.unicommerce.co.in"
    http:
      paths:
      - backend:
          service:
            name: istio-ingressgateway
            port:
              number: 80
        path: /
        pathType: Prefix
  - host: "*.unicommerce.com"
    http:
      paths:
      - backend:
          service:
            name: istio-ingressgateway
            port:
              number: 80
        path: /
        pathType: Prefix
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    alb.ingress.kubernetes.io/load-balancer-name: uniware-gateway
    alb.ingress.kubernetes.io/group.name: uniware-gateway-alb
    alb.ingress.kubernetes.io/group.order: 1
    alb.ingress.kubernetes.io/tags: "Name=uniware-gateway"
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}]'
    alb.ingress.kubernetes.io/inbound-cidrs: "0.0.0.0/0"
    alb.ingress.kubernetes.io/backend-protocol: HTTP
    alb.ingress.kubernetes.io/conditions.rule-path1: >
      [{"field":"host-header","hostHeaderConfig":{"values":["tracelog-in.unicommerce.co.in","qa-qaunixpress.unicommerce.co.in","admin-qaunixpress.unicommerce.co.in"]}}]
    alb.ingress.kubernetes.io/actions.rule-path1: >
      {"type": "redirect","redirectConfig": {"protocol": "HTTPS","port": "443","statusCode": "HTTP_301"}}
    alb.ingress.kubernetes.io/healthcheck-path: "/healthz/ready"
    alb.ingress.kubernetes.io/healthcheck-port: "31957"
  labels:
    app.kubernetes.io/name: istio-gateway-ingress
  name: istio-gateway-redirect
  namespace: istio-system
spec:
  ingressClassName: alb
  rules:
  - http:
      paths:
        - path: /
          backend:
            service:
              name: rule-path1
              port:
                name: use-annotation
          pathType: Prefix
---
apiVersion: networking.istio.io/v1
kind: Gateway
metadata:
  name: ingress-gateway
  namespace: istio-system
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "*.unicommerce.com"
    - "*.unicommerce.co.in"
---
apiVersion: networking.istio.io/v1
kind: VirtualService
metadata:
  name: istio-haproxy-external-ingress
  namespace: istio-system
spec:
  gateways:
  - istio-system/ingress-gateway
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: haproxy-external.kube-system.svc.cluster.local
        port:
          number: 8080