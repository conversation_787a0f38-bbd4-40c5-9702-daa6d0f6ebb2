---
{{- if .Values.service.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "externalIngress.fullname" . }}-api
  labels:
    {{- include "externalIngress.labels" . | nindent 4 }}
spec:
  {{- if gt (len .Values.backend) 1 }}
  ports:
    - name: http
      port: {{ .Values.service.port }}
      protocol: TCP
      targetPort: {{ .Values.service.port }}
  {{- else }}
  type: ExternalName
  {{- range .Values.backend }}
  externalName: {{ . }}
  {{- end }}
  {{- end }}
{{- end }}
---
{{- $context := . }}
{{- if and .Values.service.enabled (gt (len .Values.backend) 1) }}
apiVersion: discovery.k8s.io/v1
kind: EndpointSlice
metadata:
  name: {{ include "externalIngress.fullname" . }}-api
  labels:
    kubernetes.io/service-name: {{ include "externalIngress.fullname" . }}-api
    {{- include "externalIngress.labels" . | nindent 4 }}
addressType: IPv4
ports:
  - name: http
    appProtocol: http
    protocol: TCP
    port: {{ .Values.service.port }}
endpoints:
  {{- range .Values.backend }}
  - addresses:
      - {{ . }}
    conditions:
      ready: true
  {{- end }}
{{- end }}
---
{{- if .Values.task.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "externalIngress.fullname" . }}-task
  labels:
    {{- include "externalIngress.labels" . | nindent 4 }}
spec:
  {{- if .Values.task.k8s }}
  type: {{ .Values.task.service.type }}
  clusterIP: None
  selector:
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/name: uniware-task
  ports:
    - name: http
      port: {{ .Values.task.service.port }}
      protocol: TCP
      targetPort: {{ .Values.task.service.port }}
  {{- else }}
  type: ExternalName
  externalName: {{ .Values.task.backend }}
  {{- end }}
{{- end }}
---