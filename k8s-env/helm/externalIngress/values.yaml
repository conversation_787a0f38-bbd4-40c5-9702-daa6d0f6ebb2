enabled: true
nameOverride: "external-ingress"
fullnameOverride: ""

backend:
  - test.unicommerce.infra

service:
  enabled: true
  port: 8080
  type: ClusterIP

task:
  enabled: true
  k8s: true
  service:
    type: ClusterIP
    port: 8080
  backend: test-task.unicommerce.infra

ingress:
  enabled: true
  className: ""
  annotations: {}
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []



