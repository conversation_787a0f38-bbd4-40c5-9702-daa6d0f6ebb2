enabled: true
podService:
  enabled: true
replicaCount: 0
config:
  enabled: true
pdb:
  enabled: false
  minAvailable: 1
image:
  repository: nexus-docker.unicommerce.infra:8123
  pullPolicy: IfNotPresent
  tag: ""
init:
  arguments:
    - mkdir -p /tmp/RotatedAppLogs
    - /tmp/files/customizedTemplates
    - /tmp/files/reconciliationInvoices
    - /tmp/files/saleOrderReturn
    - /tmp/files/import/logs
    - /tmp/files/saleOrder
    - /tmp/files/shippingLabel
imagePullSecrets: []
nameOverride: "uniware-api"
fullnameOverride: ""
serviceAccount:
  create: true
  automount: true
  annotations: {}
  name: ""

resources: {}

podAnnotations: {}

podLabels: {}

podSecurityContext: {}

securityContext: {}

volumes: []

volumeMounts: []

nodeSelector: {}

tolerations: []

affinity: {}

terminationGracePeriodSeconds: 60

volumeClaimTemplates:
  - name: logs
    accessModes: 
      - ReadWriteOnce
    resources:
      requests:
        storage: 50Gi
    storageClassName: gp3

web:
  enabled: false
  replicaCount: 0
  nameOverride: "uniware-web"
  resources: {}
  affinity: {}
  service:
    enabled: true
    port: 8080
    type: ClusterIP
    hazelCastport: 5701
    catalinaport: 4000
    headless:
      enabled: true
  autoscaling:
    enabled: true
    minReplicas: 1
    maxReplicas: 2
    targetCPUUtilizationPercentage: 80
  podService:
    enabled: true
  pdb:
    enabled: false
    minAvailable: 1

service:
  enabled: true
  type: ClusterIP
  port: 8080
  hazelCastport: 5701
  catalinaport: 4000
  headless:
    enabled: true

ingress:
  enabled: true
  className: ""
  annotations: {}
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []

istio:
  enabled: false
  gateways:
    name: istio-system/ingress-gateway
    timeout: 480s
  web:
    enabled: false

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 2
  targetCPUUtilizationPercentage: 80

uniware-task:
  enabled: false
  terminationGracePeriodSeconds: 45
  service:
    enabled: true
    type: ClusterIP
    port: 8080
  replicaCount: 1
  
global:
  promtail:
    enabled: true
    image:
      repository: docker.io/grafana/promtail
      tag: 3.0.0
      pullPolicy: IfNotPresent
    configReloader:
      enabled: true
      image:
        registry: ghcr.io
        repository: jimmidyson/configmap-reload
        tag: v0.12.0
      config:
        serverPort: 9533
    config:
      enabled: true
      logLevel: info
      logFormat: logfmt
      serverPort: 9080
      clients:
        - url: http://loki-gateway.monitoring.svc.cluster.local:80/loki/api/v1/push
          tenant_id: 'app1.ecloud1-in'
      positions:
        filename: /usr/local/tomcat/logs/positions.yaml
      snippets:
        pipelineStages:
          - multiline:
              firstline: '^\d{2}:\d{2}:\d{2},\d{3}'
              max_wait_time: 5s
              max_lines: 1024
          - regex:
              source: filename
              expression: '/usr/local/tomcat/logs/(?P<filename>.+)'
          - labels:
              filename:
        scrapeConfigs: |
          - job_name: application
            pipeline_stages:
            {{- toYaml .Values.global.promtail.config.snippets.pipelineStages | nindent 4 }}
            static_configs:
              - targets:
                  - localhost
                labels:
                  name: "{{ .Release.Name }}"
                  __path__: /usr/local/tomcat/logs/*.log
                  __path_exclude__: '/usr/local/tomcat/logs/{app[0-9],service-metrics,catalina,import-export-logger,access_log,localhost,third-party-api-metrics,*.[0-9]}*'
                  pod_name: ${PODNAME}
                  namespace: ${NAMESPACE}
      file: |
        server:
          log_level: {{ .Values.global.promtail.config.logLevel }}
          log_format: {{ .Values.global.promtail.config.logFormat }}
          http_listen_port: {{ .Values.global.promtail.config.serverPort }}

        clients:
          {{- tpl (toYaml .Values.global.promtail.config.clients) . | nindent 2 }}

        positions:
          {{- tpl (toYaml .Values.global.promtail.config.positions) . | nindent 2 }}

        scrape_configs:
          {{- tpl .Values.global.promtail.config.snippets.scrapeConfigs . | nindent 2 }}

  filebeat:
    image:
      repository: nexus-docker.unicommerce.infra:8123/uni-filebeat
      tag: 6.5.0
    enabled: true
    securityContext:
      runAsGroup: 1000
      runAsUser: 1000
    config:
      file: |
        path.data: /usr/local/tomcat/logs/filebeat
        filebeat.inputs:
        - type: log
          enabled: true
          paths:
            - /usr/local/tomcat/logs/access_log*.txt
          fields:
            release_version: '${RELEASE_VERSION}'
          tail_files: true
          ignore_older: 1h

        - type: log
          enabled: true
          paths:
            - /usr/local/tomcat/logs/third-party-api-metrics.log
          fields:
            target_index: third-party-api-metrics
            release_version: '${RELEASE_VERSION}'
          tail_files: true
          ignore_older: 1h

        - type: log
          enabled: true
          paths:
            - /usr/local/tomcat/logs/job-metrics.log
          fields:
            target_index: job-metrics
            release_version: '${RELEASE_VERSION}'
          tail_files: true
          ignore_older: 1h

        - type: log
          enabled: true
          paths:
            - /usr/local/tomcat/logs/import-export-logger.log
          fields:
            target_index: import-export-logger
            release_version: '${RELEASE_VERSION}'
          tail_files: true
          ignore_older: 1h

        - type: log
          enabled: true
          paths:
            - /usr/local/tomcat/logs/gc.log.*
          include_lines: ["Full GC"]
          fields:
            target_index: gc-logger
          tail_files: true
          ignore_older: 1h

        - type: log
          enabled: true
          paths:
            - /usr/local/tomcat/logs/service-metrics.log
          fields:
            target_index: service-metrics
          multiline.pattern: 'activeThreadCount,'
          multiline.negate: true
          multiline.match: after
          tail_files: true
          ignore_older: 1h

        filebeat.config.modules:
          path: ${path.config}/modules.d/*.yml
          reload.enabled: false
        
        filebeat.config.inputs:
          enabled: true
          path: ${path.config}/configs/*.yml
          reload.enabled: true
          reload.period: 10s

        setup.template.settings:
          index.number_of_shards: 3

        name: {{ .Values.global.filebeat.config.name | quote }}

        setup.kibana:

        output.logstash:
          hosts:
          {{- range .Values.global.filebeat.config.hosts }}
            - {{ . | quote }}
          {{- end }}
          loadbalance: true
  
  efs:
    enabled: false
