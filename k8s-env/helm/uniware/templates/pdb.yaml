{{- if and .Values.pdb.enabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  labels:
    {{- include "uniware.labels" . | nindent 4 }}
  name: {{ include "uniware.fullname" . }}
spec:
  selector:
    matchLabels:
      {{- include "uniware.selectorLabels" . | nindent 6 }}
  minAvailable: {{ .Values.pdb.minAvailable }}
{{- end }}
---
{{- if and .Values.web.pdb.enabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  labels:
    {{- include "uniware-web.labels" . | nindent 4 }}
  name: {{ include "uniware-web.fullname" . }}
spec:
  selector:
    matchLabels:
      {{- include "uniware-web.selectorLabels" . | nindent 6 }}
  minAvailable: {{ .Values.web.pdb.minAvailable }}
{{- end }}