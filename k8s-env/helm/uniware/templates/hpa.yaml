{{- if .Values.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "uniware.fullname" . }}
  labels:
    {{- include "uniware.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: StatefulSet
    name: {{ include "uniware.fullname" . }}
  minReplicas: {{ .Values.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.autoscaling.targetCPUUtilizationPercentage }}
    - containerResource:
        container: {{ .Chart.Name }}
        name: cpu
        target:
          averageUtilization: {{ .Values.autoscaling.targetCPUUtilizationPercentage }}
          type: Utilization
      type: ContainerResource
    {{- end }}
    {{- if .Values.autoscaling.targetMemoryUtilizationPercentage }}
    - containerResource:
        container: {{ .Chart.Name }}
        name: memory
        target:
          averageUtilization: {{ .Values.autoscaling.targetMemoryUtilizationPercentage }}
          type: Utilization
      type: ContainerResource
    {{- end }}
{{- end }}
---
{{- if .Values.web.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "uniware-web.fullname" . }}
  labels:
    {{- include "uniware-web.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: StatefulSet
    name: {{ include "uniware-web.fullname" . }}
  minReplicas: {{ .Values.web.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.web.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.web.autoscaling.targetCPUUtilizationPercentage }}
    - containerResource:
        container: {{ .Chart.Name }}
        name: cpu
        target:
          averageUtilization: {{ .Values.web.autoscaling.targetCPUUtilizationPercentage }}
          type: Utilization
      type: ContainerResource
    {{- end }}
    {{- if .Values.web.autoscaling.targetMemoryUtilizationPercentage }}
    - containerResource:
        container: {{ .Chart.Name }}
        name: memory
        target:
          averageUtilization: {{ .Values.web.autoscaling.targetMemoryUtilizationPercentage }}
          type: Utilization
      type: ContainerResource
    {{- end }}
{{- end }}