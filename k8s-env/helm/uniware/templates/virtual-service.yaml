{{- if .Values.istio.enabled -}}
{{- $fullName := include "uniware.fullname" . -}}
{{- $webfullName := include "uniware-web.fullname" . -}}
{{- $svcPort := .Values.service.port -}}
{{- $svcTask := printf "%s-%s" .Release.Name "uniware-task" }}
{{- $context := . }}
apiVersion: networking.istio.io/v1
kind: VirtualService
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "uniware.labels" . | nindent 4 }}
  {{- with .Values.istio.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  gateways:
  - {{ .Values.istio.gateways.name }}  # Assuming .Values.istio.gateways is an object with a 'name' field
  hosts:
  {{- range .Values.ingress.hosts }}
  - {{ .host | quote }}
  {{- end }}
  http:
  - match:
    - queryParams:
        s:
          exact: "app2"
    headers:
      request:
        set:
          X-Forwarded-Proto: "https"
    route:
    - destination:
        host: {{ $svcTask }}
        port:
          number: {{ $svcPort }}
    timeout: {{ .Values.istio.gateways.timeout }}
{{- if and $context.Values.istio.web.enabled $context.Values.web.enabled }}
  - match:
    - uri:
        prefix: /services/rest
    headers:
      request:
        set:
          X-Forwarded-Proto: "https"
    route:
    - destination:
        host: {{ $fullName }}
        port:
          number: {{ $svcPort }}
    timeout: {{ .Values.istio.gateways.timeout }}
  - match:
    - uri:
        prefix: /services/soap
    headers:
      request:
        set:
          X-Forwarded-Proto: "https"
    route:
    - destination:
        host: {{ $fullName }}
        port:
          number: {{ $svcPort }}
    timeout: {{ .Values.istio.gateways.timeout }}
{{- end }}
  - match:
    - uri:
        prefix: /
    headers:
      request:
        set:
          X-Forwarded-Proto: "https"
    route:
    - destination:
      {{- if and $context.Values.istio.web.enabled $context.Values.web.enabled }}
        host: {{ $webfullName }}
      {{- else }}
        host: {{ $fullName }}
      {{- end }}
        port:
          number: {{ $svcPort }}
    timeout: {{ .Values.istio.gateways.timeout }}
{{- end }}
