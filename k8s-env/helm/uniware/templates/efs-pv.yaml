{{- if .Values.global.efs.enabled }}
apiVersion: v1
kind: PersistentVolume
metadata:
  name: {{ .Release.Name }}-uniware-files-pv
spec:
  capacity:
    storage:  {{ .Values.global.efs.storage }}
  volumeMode: Filesystem
  accessModes:
    - {{ .Values.global.efs.accessModes }}
  persistentVolumeReclaimPolicy: {{ .Values.global.efs.ReclaimPolicy }}
  storageClassName: {{ .Values.global.efs.storageClass }}
  csi:
    driver: efs.csi.aws.com
    volumeHandle: {{ .Values.global.efs.volumeId }}
{{- end }}
---
{{- if .Values.global.efs.enabled }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ .Release.Name }}-uniware-files-pvc
spec:
  accessModes:
    - {{ .Values.global.efs.accessModes }}
  storageClassName: {{ .Values.global.efs.storageClass }}
  resources:
    requests:
      storage: {{ .Values.global.efs.storage }}
{{- end }}