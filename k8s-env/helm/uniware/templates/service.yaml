{{- if .Values.service.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "uniware.fullname" . }}
  labels:
    {{- include "uniware.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  {{- if .Values.service.headless.enabled }}
  clusterIP: None
  {{- end }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
    - port: {{ .Values.service.hazelCastport }}
      targetPort: {{ .Values.service.hazelCastport }}
      protocol: TCP
      name: hazelcast
  selector:
    {{- include "uniware.selectorLabels" . | nindent 4 }}
{{- end }}
---
{{- $taskEnabled := default false (index .Values "uniware-task").enabled }}
{{- if not $taskEnabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ .Release.Name }}-uniware-task
  labels:
    app.kubernetes.io/name: uniware-task
    app.kubernetes.io/instance: {{ .Release.Name }}
spec:
  type: {{ .Values.service.type }}
  {{- if .Values.service.headless.enabled }}
  clusterIP: None
  {{- end }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
    - port: {{ .Values.service.hazelCastport }}
      targetPort: {{ .Values.service.hazelCastport }}
      protocol: TCP
      name: hazelcast
  selector:
    {{- include "uniware.selectorLabels" . | nindent 4 }}
{{- end }}
---
{{- $replicaCount := .Values.replicaCount | int }}
{{- if .Values.autoscaling.enabled }}
  {{- $replicaCount = .Values.autoscaling.maxReplicas | int }}
{{- end }}
{{- if and .Values.enabled .Values.podService.enabled }}
{{- $context := . }}
{{- range $i := until $replicaCount }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "uniware.fullname" $context }}-{{ $i }}
  labels:
    {{- include "uniware.labels" $context | nindent 4 }}
spec:
  type: ClusterIP
  ports:
    - port: {{ $context.Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
    - port: {{ $context.Values.service.hazelCastport }}
      targetPort: {{ $context.Values.service.hazelCastport }}
      protocol: TCP
      name: hazelcast
    - port: {{ $context.Values.service.catalinaport }}
      targetPort: {{ $context.Values.service.catalinaport }}
      protocol: TCP
      name: catalina
  selector:
    {{- include "uniware.selectorLabels" $context | nindent 4 }}
    statefulset.kubernetes.io/pod-name: {{ include "uniware.fullname" $context }}-{{ $i }}
{{- end }}
{{- end }}
---
{{- if .Values.web.service.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "uniware-web.fullname" . }}
  labels:
    {{- include "uniware-web.labels" . | nindent 4 }}
spec:
  type: {{ .Values.web.service.type }}
  {{- if .Values.web.service.headless.enabled }}
  clusterIP: None
  {{- end }}
  ports:
    - port: {{ .Values.web.service.port }}
      targetPort: http
      protocol: TCP
      name: http
    - port: {{ .Values.web.service.hazelCastport }}
      targetPort: {{ .Values.web.service.hazelCastport }}
      protocol: TCP
      name: hazelcast
  selector:
    {{- include "uniware-web.selectorLabels" . | nindent 4 }}
{{- end }}
---
{{- $replicaCount := .Values.web.replicaCount | int }}
{{- if .Values.web.autoscaling.enabled }}
  {{- $replicaCount = .Values.web.autoscaling.maxReplicas | int }}
{{- end }}
{{- if and .Values.web.enabled .Values.web.podService.enabled }}
{{- $context := . }}
{{- range $i := until $replicaCount }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "uniware-web.fullname" $context }}-{{ $i }}
  labels:
    {{- include "uniware-web.labels" $context | nindent 4 }}
spec:
  type: ClusterIP
  ports:
    - port: {{ $context.Values.web.service.port }}
      targetPort: http
      protocol: TCP
      name: http
    - port: {{ $context.Values.web.service.hazelCastport }}
      targetPort: {{ $context.Values.web.service.hazelCastport }}
      protocol: TCP
      name: hazelcast
    - port: {{ $context.Values.web.service.catalinaport }}
      targetPort: {{ $context.Values.web.service.catalinaport }}
      protocol: TCP
      name: catalina
  selector:
    {{- include "uniware-web.selectorLabels" $context | nindent 4 }}
    statefulset.kubernetes.io/pod-name: {{ include "uniware-web.fullname" $context }}-{{ $i }}
{{- end }}
{{- end }}