{{- if .Values.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "uniware.fullname" . }}
  labels:
    {{- include "uniware.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  {{- if .Values.service.headless.enabled }}
  clusterIP: None
  {{- end }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
    - port: {{ .Values.service.hazelCastport }}
      targetPort: {{ .Values.service.hazelCastport }}
      protocol: TCP
      name: hazelcast
  selector:
    {{- include "uniware.selectorLabels" . | nindent 4 }}
{{- end }}
