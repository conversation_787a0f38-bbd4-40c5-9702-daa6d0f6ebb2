{{- if .Values.enabled }}
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "uniware.fullname" . }}
  labels:
    {{- include "uniware.labels" . | nindent 4 }}
spec:
  serviceName: {{ include "uniware.fullname" . }}
  replicas: 1
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      {{- include "uniware.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "uniware.labels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ .Release.Name }}-uniware
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      initContainers:
        - name: init-mkdir
          image: busybox:1.28
          command: ['sh', '-c']
          {{- with .Values.init.arguments }}
          args:
            - |
              {{- range . }}
              {{ . }} \
              {{- end }}
          {{- end }}
          volumeMounts:
            - name: logs
              mountPath: /tmp
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- with .Values.lifecycle }}
          lifecycle:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.arguments }}
          args:
            {{- range . }}
            - {{ . }}
            {{- end }}
          {{- end }}
          {{- with .Values.env }}
          env:
            - name: ORDINAL_NUMBER
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['apps.kubernetes.io/pod-index']
            {{- range $key, $value := . }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
          {{- end }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          {{- with .Values.livenessProbe }}
          livenessProbe:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.readinessProbe }}
          readinessProbe:
            {{- toYaml .| nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: logs
              mountPath: /usr/local/tomcat/logs
            {{- with .Values.volumeMounts }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
        {{- if .Values.global.promtail.enabled }}
        - name: promtail
          image: {{ .Values.global.promtail.image.repository }}:{{ .Values.global.promtail.image.tag }}
          args:
            - "-config.file=/etc/promtail/promtail.yaml"
            - "-config.expand-env=true"
            {{- if .Values.global.promtail.configReloader.enabled }}
            - "-server.enable-runtime-reload"
            {{- end }}
            {{- with .Values.global.promtail.extraArgs }}
            {{- toYaml . | nindent 8 }}
            {{- end }}
          env:
            - name: HOSTNAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: PODNAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          resources:
            {{- toYaml .Values.global.promtail.resources | nindent 12 }}
          volumeMounts:
            - name: config
              mountPath: /etc/promtail
            - name: logs
              mountPath: /usr/local/tomcat/logs
        {{- end }}
        {{- if .Values.global.promtail.configReloader.enabled }}
        - name: config-reloader
          image: "{{ .Values.global.promtail.configReloader.image.registry }}/{{ .Values.global.promtail.configReloader.image.repository }}:{{ .Values.global.promtail.configReloader.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          args:
            - '-web.listen-address=:{{ .Values.global.promtail.configReloader.config.serverPort }}'
            - '-volume-dir=/etc/promtail/'
            - '-webhook-method=GET'
            - '-webhook-url=http://127.0.0.1:{{ .Values.global.promtail.config.serverPort }}/reload'
          {{- range .Values.global.promtail.configReloader.extraArgs }}
            - {{ . }}
          {{- end }}
          volumeMounts:
            - name: config
              mountPath: /etc/promtail
        {{- end }}
        {{- if .Values.global.filebeat.enabled }}
        - name: filebeat
          image: {{ .Values.global.filebeat.image.repository }}:{{ .Values.global.filebeat.image.tag }}
          imagePullPolicy: IfNotPresent
          env:
            - name: HOSTNAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: PODNAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: RELEASE_VERSION
              value: {{ .Values.image.tag | quote }}
          resources:
            {{- toYaml .Values.global.promtail.resources | nindent 12 }}
          securityContext:
            {{- toYaml .Values.global.filebeat.securityContext | nindent 12 }}
          volumeMounts:
          - mountPath: /usr/local/tomcat/logs
            name: logs
          - mountPath: /usr/share/filebeat/filebeat.yml
            name: filebeat-config
            subPath: filebeat.yml
        {{- end }}
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriodSeconds }}
      volumes:
        {{- if .Values.global.promtail.enabled }}
        - name: config
          configMap:
            name: {{ .Release.Name }}-uniware-promtail
        {{- end }}
        {{- if .Values.global.filebeat.enabled }}
        - name: filebeat-config
          configMap:
            defaultMode: 420
            name: {{ .Release.Name }}-uniware-filebeat
        {{- end }}
        {{- with .Values.volumes }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- toYaml . | nindent 8 }}
      {{- end }}
  volumeClaimTemplates:
    {{- range .Values.volumeClaimTemplates }}
    - metadata:
        name: {{ .name }}
      spec:
        accessModes:
          {{- toYaml .accessModes | nindent 8 }}
        resources:
          requests:
            storage: {{ .resources.requests.storage }}
        {{- with .storageClassName }}
        storageClassName: {{ . }}
        {{- end }}
    {{- end }}
{{- end }}
