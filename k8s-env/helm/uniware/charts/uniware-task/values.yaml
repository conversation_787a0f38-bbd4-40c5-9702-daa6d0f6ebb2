replicaCount: 0
enabled: false

image:
  repository: nexus.unicommerce.infra:8123
  pullPolicy: IfNotPresent
  tag: ""

init:
  arguments:
    - mkdir -p /tmp/RotatedAppLogs
    - /tmp/files/customizedTemplates
    - /tmp/files/reconciliationInvoices
    - /tmp/files/saleOrderReturn
    - /tmp/files/import/logs
    - /tmp/files/saleOrder
    - /tmp/files/shippingLabel
imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

podAnnotations: {}
podLabels: {}

podSecurityContext: {}

securityContext: {}

service:
  type: ClusterIP
  port: 8080
  hazelCastport: 5701
  headless:
    enabled: true
    
resources: {}

livenessProbe: {}
readinessProbe: {}

volumes: []

volumeMounts: []

nodeSelector: {}

tolerations: []

affinity: {}

volumeClaimTemplates:
  - name: logs
    accessModes: 
      - ReadWriteOnce
    resources:
      requests:
        storage: 50Gi
    storageClassName: gp3

global:
  promtail:
    enabled: false
    configReloader:
      enabled: false
    config:
      enabled: false
  filebeat:
    enabled: false